"""
Domain Modeler Agent

Intelligent agent for applying DDD methodology to create domain models.
"""

from typing import Any, Dict, List
from datetime import datetime
from pathlib import Path
import yaml

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class DomainModelerAgent(BaseAgent):
    """Agent for creating domain models using DDD methodology."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None, log_dir=None):
        super().__init__(
            name="domain_modeler",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer,
            log_dir=log_dir
        )
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for domain modeling."""
        try:
            prompts_path = Path(__file__).parent.parent / "prompts" / "domain_modeling.yaml"
            with open(prompts_path, 'r', encoding='utf-8') as f:
                prompts = yaml.safe_load(f)

            # Use the complete system prompt if available
            if 'complete_system_prompt' in prompts:
                return prompts['complete_system_prompt']
            else:
                # Use the regular system prompt
                if 'system_prompt' not in prompts:
                    raise Exception("Missing 'system_prompt' in domain modeling prompts configuration")
                return prompts['system_prompt']

        except Exception as e:
            self.logger.error(f"Failed to load system prompt from YAML: {e}")
            raise Exception(f"Cannot load system prompt from YAML: {e}")


    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process business analysis and create domain models."""
        try:
            # Get business analysis results
            business_analysis = input_data
            self.logger.debug(f"Input data type: {type(input_data)}")
            self.logger.debug(f"Input data: {input_data}")
            if not business_analysis:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No business analysis data provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Prepare input for LLM
            user_input = f"""
请基于以下业务分析结果，应用DDD方法论创建领域模型：

=== 业务分析结果 ===
{self._format_business_analysis(business_analysis)}

=== 项目上下文 ===
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}

=== 设计要求 ===
1. 识别清晰的边界上下文，避免过度分割
2. 设计合理的聚合边界，确保业务一致性
3. 定义富有行为的领域实体，不要贫血模型
4. 识别合适的值对象，提高模型表达力
5. 设计必要的领域服务，处理跨实体业务逻辑
6. 定义抽象的仓储接口，支持数据访问
7. 识别重要的领域事件，支持事件驱动架构

请确保输出的Markdown格式正确且领域模型设计合理。
"""
            
            # Execute LLM call
            system_prompt = self.get_system_prompt()
            messages = self._create_messages(system_prompt, user_input)
            response = self._execute_llm_call(messages)
            
            # Process markdown response
            processed_data = self._process_markdown_response(response)

            # Validate content completeness
            validation_errors = self._validate_markdown_content(processed_data)

            if validation_errors:
                return AgentResult(
                    success=False,
                    data=processed_data,
                    metadata={"agent_name": self.name, "raw_response": response},
                    errors=validation_errors,
                    execution_time=0.0,
                    timestamp=datetime.now()
                )
            
            # Post-process and validate domain model
            validated_data = self._validate_domain_model(processed_data)
            
            return AgentResult(
                success=True,
                data=validated_data,
                metadata={
                    "agent_name": self.name,
                    "raw_response": response,
                    "bounded_contexts_count": len(validated_data.get("bounded_contexts", [])),
                    "aggregates_count": len(validated_data.get("aggregates", [])),
                    "entities_count": len(validated_data.get("domain_entities", [])),
                    "repositories_count": len(validated_data.get("repositories", []))
                },
                errors=[],
                execution_time=0.0,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Domain modeling failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )
    
    def _format_business_analysis(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for LLM input."""
        # Handle case where analysis might be a string (JSON string)
        if isinstance(analysis, str):
            try:
                import json
                analysis = json.loads(analysis)
            except json.JSONDecodeError:
                return f"Raw analysis data:\n{analysis}"

        if not isinstance(analysis, dict):
            return f"Invalid analysis data type: {type(analysis)}"

        formatted = []

        # Business overview
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append("## 业务概览")
            formatted.append(f"项目名称: {overview.get('project_name', 'N/A')}")
            formatted.append(f"核心目标: {overview.get('core_purpose', 'N/A')}")
            formatted.append(f"目标用户: {', '.join(overview.get('target_users', []))}")
            formatted.append("")
        
        # Core entities
        if "core_entities" in analysis:
            formatted.append("## 核心业务实体")
            for entity in analysis["core_entities"]:
                if isinstance(entity, dict):
                    formatted.append(f"- {entity.get('name', 'Unknown')}: {entity.get('description', '')}")
                    if entity.get('key_attributes'):
                        formatted.append(f"  属性: {', '.join(entity['key_attributes'])}")
                else:
                    formatted.append(f"- {str(entity)}")
            formatted.append("")
        
        # Business rules
        if "business_rules" in analysis:
            formatted.append("## 业务规则")
            for rule in analysis["business_rules"]:
                if isinstance(rule, dict):
                    formatted.append(f"- [{rule.get('category', 'General')}] {rule.get('rule', '')}")
                else:
                    formatted.append(f"- {str(rule)}")
            formatted.append("")
        
        # Functional requirements
        if "functional_requirements" in analysis:
            formatted.append("## 功能需求")
            for req in analysis["functional_requirements"]:
                formatted.append(f"- {req.get('id', 'Unknown')}: {req.get('title', '')}")
                formatted.append(f"  {req.get('description', '')}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _validate_domain_model(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and enrich domain model."""
        validated = data.copy()
        
        # Add model metadata
        validated["model_metadata"] = {
            "creation_timestamp": datetime.now().isoformat(),
            "ddd_patterns_used": self._identify_ddd_patterns(data),
            "complexity_metrics": self._calculate_model_complexity(data)
        }
        
        # Validate aggregate consistency
        validated["validation_results"] = self._validate_aggregate_consistency(data)
        
        return validated
    
    def _identify_ddd_patterns(self, data: Dict[str, Any]) -> List[str]:
        """Identify DDD patterns used in the model."""
        patterns = []
        
        if data.get("bounded_contexts"):
            patterns.append("Bounded Context")
        if data.get("aggregates"):
            patterns.append("Aggregate")
        if data.get("domain_entities"):
            patterns.append("Entity")
        if data.get("value_objects"):
            patterns.append("Value Object")
        if data.get("domain_services"):
            patterns.append("Domain Service")
        if data.get("repositories"):
            patterns.append("Repository")
        if data.get("domain_events"):
            patterns.append("Domain Event")
        
        return patterns
    
    def _calculate_model_complexity(self, data: Dict[str, Any]) -> Dict[str, int]:
        """Calculate complexity metrics for the domain model."""
        return {
            "total_bounded_contexts": len(data.get("bounded_contexts", [])),
            "total_aggregates": len(data.get("aggregates", [])),
            "total_entities": len(data.get("domain_entities", [])),
            "total_value_objects": len(data.get("value_objects", [])),
            "total_services": len(data.get("domain_services", [])),
            "total_repositories": len(data.get("repositories", [])),
            "total_events": len(data.get("domain_events", []))
        }
    
    def _validate_aggregate_consistency(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Validate aggregate consistency and relationships."""
        issues = []
        warnings = []
        
        aggregates = data.get("aggregates", [])
        entities = data.get("domain_entities", [])
        repositories = data.get("repositories", [])
        
        # Check if each aggregate has a corresponding repository
        aggregate_names = {agg.get("name") for agg in aggregates}
        repository_aggregates = {repo.get("aggregate") for repo in repositories}
        
        missing_repos = aggregate_names - repository_aggregates
        if missing_repos:
            warnings.extend([f"Aggregate '{name}' has no corresponding repository" for name in missing_repos])
        
        # Check if entities belong to defined aggregates
        entity_aggregates = {entity.get("aggregate") for entity in entities}
        undefined_aggregates = entity_aggregates - aggregate_names
        if undefined_aggregates:
            issues.extend([f"Entity references undefined aggregate '{name}'" for name in undefined_aggregates])
        
        return {
            "issues": issues,
            "warnings": warnings
        }

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return '''
{
  "bounded_contexts": [
    {
      "name": "Server Management",
      "description": "Manages MCP server lifecycle and metadata",
      "responsibilities": ["Server registration", "Version management", "Metadata storage"],
      "ubiquitous_language": {
        "Server": "MCP server instance",
        "Version": "Server version release",
        "Metadata": "Server descriptive information"
      }
    },
    {
      "name": "User Management",
      "description": "Handles user accounts and authentication",
      "responsibilities": ["User registration", "Authentication", "Profile management"],
      "ubiquitous_language": {
        "User": "Platform user account",
        "Profile": "User information and preferences",
        "Authentication": "User identity verification"
      }
    },
    {
      "name": "Discovery",
      "description": "Enables server search and discovery",
      "responsibilities": ["Search functionality", "Categorization", "Filtering"],
      "ubiquitous_language": {
        "Search": "Server discovery process",
        "Category": "Server classification",
        "Filter": "Search refinement criteria"
      }
    }
  ],
  "aggregates": [
    {
      "name": "MCPServer",
      "bounded_context": "Server Management",
      "aggregate_root": "MCPServer",
      "entities": ["MCPServer", "ServerVersion"],
      "value_objects": ["ServerMetadata", "VersionInfo"],
      "business_rules": [
        "Server must have unique name within author scope",
        "Version numbers must follow semantic versioning",
        "Published servers cannot be deleted, only deprecated"
      ],
      "invariants": [
        "Server must have at least one version",
        "Latest version must be accessible"
      ]
    },
    {
      "name": "User",
      "bounded_context": "User Management",
      "aggregate_root": "User",
      "entities": ["User", "UserProfile"],
      "value_objects": ["Email", "Username"],
      "business_rules": [
        "Email must be unique across platform",
        "Username must be unique and follow naming conventions",
        "User can only modify their own profile"
      ],
      "invariants": [
        "User must have valid email",
        "User must have unique username"
      ]
    }
  ],
  "domain_entities": [
    {
      "name": "MCPServer",
      "aggregate": "MCPServer",
      "description": "Core entity representing an MCP server",
      "attributes": [
        {"name": "id", "type": "UUID", "description": "Unique server identifier"},
        {"name": "name", "type": "String", "description": "Server name"},
        {"name": "description", "type": "String", "description": "Server description"},
        {"name": "author_id", "type": "UUID", "description": "Server author reference"},
        {"name": "category_id", "type": "UUID", "description": "Server category"},
        {"name": "created_at", "type": "DateTime", "description": "Creation timestamp"},
        {"name": "updated_at", "type": "DateTime", "description": "Last update timestamp"}
      ],
      "business_methods": [
        "publish_version",
        "update_metadata",
        "deprecate",
        "transfer_ownership"
      ]
    },
    {
      "name": "User",
      "aggregate": "User",
      "description": "Platform user entity",
      "attributes": [
        {"name": "id", "type": "UUID", "description": "Unique user identifier"},
        {"name": "username", "type": "String", "description": "Unique username"},
        {"name": "email", "type": "String", "description": "User email address"},
        {"name": "created_at", "type": "DateTime", "description": "Registration timestamp"},
        {"name": "last_login", "type": "DateTime", "description": "Last login timestamp"}
      ],
      "business_methods": [
        "update_profile",
        "change_password",
        "deactivate_account"
      ]
    }
  ],
  "value_objects": [
    {
      "name": "ServerMetadata",
      "description": "Immutable server metadata information",
      "attributes": [
        {"name": "tags", "type": "List[String]", "description": "Server tags"},
        {"name": "documentation_url", "type": "URL", "description": "Documentation link"},
        {"name": "repository_url", "type": "URL", "description": "Source code repository"}
      ]
    },
    {
      "name": "Email",
      "description": "Valid email address",
      "attributes": [
        {"name": "value", "type": "String", "description": "Email address value"}
      ],
      "validation_rules": ["Must be valid email format", "Must not be empty"]
    }
  ],
  "domain_services": [
    {
      "name": "ServerDiscoveryService",
      "description": "Handles complex server search and discovery logic",
      "methods": [
        "search_servers",
        "recommend_servers",
        "categorize_server"
      ],
      "dependencies": ["ServerRepository", "CategoryRepository"]
    }
  ],
  "repositories": [
    {
      "name": "MCPServerRepository",
      "aggregate": "MCPServer",
      "description": "Persistence interface for MCP servers",
      "methods": [
        "find_by_id",
        "find_by_author",
        "find_by_category",
        "search_by_keywords",
        "save",
        "delete"
      ]
    },
    {
      "name": "UserRepository",
      "aggregate": "User",
      "description": "Persistence interface for users",
      "methods": [
        "find_by_id",
        "find_by_username",
        "find_by_email",
        "save",
        "delete"
      ]
    }
  ],
  "domain_events": [
    {
      "name": "ServerPublished",
      "description": "Raised when a new server is published",
      "attributes": [
        {"name": "server_id", "type": "UUID"},
        {"name": "author_id", "type": "UUID"},
        {"name": "published_at", "type": "DateTime"}
      ]
    },
    {
      "name": "UserRegistered",
      "description": "Raised when a new user registers",
      "attributes": [
        {"name": "user_id", "type": "UUID"},
        {"name": "username", "type": "String"},
        {"name": "registered_at", "type": "DateTime"}
      ]
    }
  ]
}
'''

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with streaming support."""
        return self._execute_llm_call_with_streaming(messages, "领域建模分析")

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response from LLM, handling multiple JSON objects."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        import re
        import json

        # Try to find all JSON objects in code blocks
        json_matches = re.findall(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)

        if json_matches:
            # Parse all JSON objects and merge them
            merged_data = {}
            for json_text in json_matches:
                try:
                    parsed = json.loads(json_text)
                    if isinstance(parsed, dict):
                        merged_data.update(parsed)
                except json.JSONDecodeError:
                    continue

            if merged_data:
                return merged_data

        # Fallback: try to find all JSON objects in text
        json_objects = []
        pos = 0
        while True:
            brace_start = response.find('{', pos)
            if brace_start == -1:
                break

            brace_count = 0
            for i, char in enumerate(response[brace_start:], brace_start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_text = response[brace_start:i+1]
                        try:
                            parsed = json.loads(json_text)
                            if isinstance(parsed, dict):
                                json_objects.append(parsed)
                        except json.JSONDecodeError:
                            pass
                        pos = i + 1
                        break
            else:
                break

        # Merge all found JSON objects
        if json_objects:
            merged_data = {}
            for obj in json_objects:
                merged_data.update(obj)
            return merged_data

        # Last resort: try to parse the entire response as JSON
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Response content: {response[:500]}...")
            return {}

    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """Validate that required fields are present in the data."""
        errors = []
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not data[field]:
                errors.append(f"Empty required field: {field}")
        return errors

    def _process_markdown_response(self, response: str) -> Dict[str, Any]:
        """Process markdown response and extract structured data."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        # Store the raw markdown content
        processed_data = {
            "raw_markdown": response,
            "content_type": "markdown",
            "sections": self._extract_markdown_sections(response)
        }

        return processed_data

    def _extract_markdown_sections(self, markdown_content: str) -> Dict[str, str]:
        """Extract sections from markdown content."""
        import re

        sections = {}

        # Split by main headers (## level)
        section_pattern = r'^## (\d+\.\s*)?(.+?)$'
        lines = markdown_content.split('\n')

        current_section = None
        current_content = []

        for line in lines:
            header_match = re.match(section_pattern, line.strip())
            if header_match:
                # Save previous section
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()

                # Start new section
                current_section = header_match.group(2).strip()
                current_content = []
            else:
                if current_section:
                    current_content.append(line)

        # Save last section
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def _validate_markdown_content(self, data: Dict[str, Any]) -> List[str]:
        """Validate markdown content completeness."""
        errors = []

        if not data.get("raw_markdown"):
            errors.append("No markdown content found")
            return errors

        sections = data.get("sections", {})

        # Check for required sections
        required_sections = [
            "边界上下文", "聚合设计", "领域实体",
            "值对象", "领域服务", "仓储接口", "领域事件"
        ]

        for section in required_sections:
            if not any(section in key for key in sections.keys()):
                errors.append(f"Missing required section: {section}")

        # Check content length
        total_content = data.get("raw_markdown", "")
        if len(total_content.strip()) < 800:
            errors.append("Content appears to be too short for comprehensive domain model")

        return errors
