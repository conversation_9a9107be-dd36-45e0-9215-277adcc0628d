"""
Requirements Generator Agent

Intelligent agent for generating detailed technical development requirements.
"""

import os
import re
from typing import Any, Dict, List, Optional
from datetime import datetime
from pathlib import Path

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext
from ..utils.config_manager import ConfigManager


class RequirementsGeneratorAgent(BaseAgent):
    """Agent for generating detailed technical development requirements."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None, log_dir=None):
        super().__init__(
            name="requirements_generator",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer,
            log_dir=log_dir
        )
        self.config_manager = ConfigManager()
        self.requirements_prompt_config = self._load_prompt_config("requirements_generation")

    def _load_prompt_config(self, prompt_name: str) -> Dict[str, Any]:
        """Load prompt configuration from YAML file."""
        try:
            import yaml
            prompt_file = Path(__file__).parent.parent / "prompts" / f"{prompt_name}.yaml"
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                return {}
        except Exception:
            return {}

    def get_system_prompt(self) -> str:
        """Get the system prompt for requirements generation."""
        return self.requirements_prompt_config.get("system_prompt", "")
    
    def _extract_modules_from_domain_model(self, domain_model: Dict[str, Any]) -> List[str]:
        """Extract module names from domain model."""
        modules = []
        
        # Extract from bounded contexts
        bounded_contexts = domain_model.get("bounded_contexts", [])
        for context in bounded_contexts:
            context_name = context.get("name", "")
            # Convert Chinese context name to English module name
            if "用户" in context_name or "User" in context_name:
                modules.append("user_management")
            elif "服务器" in context_name or "Server" in context_name:
                modules.append("server_marketplace")
            else:
                # Fallback: use context name as module name
                module_name = re.sub(r'[^a-zA-Z0-9_]', '_', context_name.lower())
                modules.append(module_name)
        
        return modules if modules else ["main_module"]
    
    def _create_output_directory(self, context: WorkflowContext) -> Path:
        """Create output directory for requirements and prompts."""
        # Use the log_dir from agent if available, otherwise fallback to project root
        if hasattr(self, 'log_dir') and self.log_dir:
            # Extract the parent directory of log_dir (which should be the timestamped output dir)
            output_base = Path(self.log_dir).parent / "results"
        else:
            # Fallback to project root with ai_generated (for backward compatibility)
            output_base = Path(context.project_root) / "ai_generated"

        output_dir = output_base / "requirements"
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir
    
    def _generate_requirement_for_module(self, module_name: str, domain_model: Dict[str, Any], 
                                       context: WorkflowContext, module_index: int) -> str:
        """Generate requirement document for a specific module."""
        
        # Prepare module-specific input
        user_input = f"""
请基于以下领域模型，为 **{module_name}** 模块生成详细的技术开发需求：

=== 领域模型 ===
{self._format_domain_model_for_module(domain_model, module_name)}

=== 项目约束 ===
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}

=== 项目规则 ===
{self._load_project_rules(context)}

=== 生成要求 ===
1. 专注于 {module_name} 模块的具体需求
2. API设计遵循RESTful原则和OpenAPI规范
3. 数据库设计考虑性能和数据完整性
4. 测试策略覆盖单元、集成、API三个层次
5. 实现顺序考虑依赖关系和风险控制
6. 所有字段使用英文命名，注释使用英文

请以Markdown格式输出，内容要详细且技术方案可行。
"""
        
        # Execute LLM call with streaming
        system_prompt = self.get_system_prompt()
        messages = self._create_messages(system_prompt, user_input)
        response = self._execute_llm_call_with_streaming(messages, f"生成 {module_name} 模块需求")
        
        return response
    
    def _generate_prompt_for_module(self, module_name: str, requirement_content: str, 
                                  module_index: int) -> str:
        """Generate AI development prompt for a specific module."""
        
        prompt_content = f"""# AI 开发提示词 - {module_name.title()} 模块

## 任务概述
你是一个专业的 Python 后端开发工程师，需要基于以下开发需求实现 {module_name} 模块的完整功能。

## 开发需求
{requirement_content}

## 开发约束
1. **架构要求**: 严格遵循 DDD 四层架构 (interfaces/application/domain/infrastructure)
2. **代码规范**: 遵循 PEP 8 规范，使用类型提示
3. **技术栈**: FastAPI + SQLAlchemy + Pydantic + Alembic + Pytest
4. **数据库**: 所有实体ID使用UUID，字段名只反映业务含义
5. **测试**: 必须包含单元测试、集成测试和API测试
6. **文档**: 所有注释和文档使用英文

## 实现要求
1. 按照 DDD 分层架构组织代码
2. 实现完整的 CRUD 操作和业务逻辑
3. 提供完整的 API 文档和测试用例
4. 确保代码质量和测试覆盖率
5. 遵循项目现有的代码风格和约定

## 输出格式
请按照以下顺序实现：
1. Domain 层 (实体、值对象、仓库接口)
2. Infrastructure 层 (ORM模型、仓库实现)
3. Application 层 (应用服务、用例)
4. Interfaces 层 (API路由、Schema)
5. 测试代码 (单元测试、集成测试、API测试)

每个文件都要包含完整的实现和必要的注释。
"""
        
        return prompt_content
    
    def _format_domain_model_for_module(self, domain_model: Dict[str, Any], module_name: str) -> str:
        """Format domain model data for a specific module."""

        # Check if this is markdown format
        if domain_model.get("content_type") == "markdown":
            return self._extract_module_relevant_markdown(domain_model, module_name)

        # Legacy JSON format handling
        formatted = []

        # Add bounded contexts
        bounded_contexts = domain_model.get("bounded_contexts", [])
        for context in bounded_contexts:
            if self._is_context_relevant_to_module(context, module_name):
                formatted.append(f"边界上下文: {context.get('name', '')}")
                formatted.append(f"职责: {', '.join(context.get('responsibilities', []))}")

        # Add relevant aggregates
        aggregates = domain_model.get("aggregates", [])
        for aggregate in aggregates:
            if self._is_aggregate_relevant_to_module(aggregate, module_name):
                formatted.append(f"\n聚合: {aggregate.get('name', '')}")
                formatted.append(f"聚合根: {aggregate.get('aggregate_root', '')}")
                formatted.append(f"实体: {', '.join(aggregate.get('entities', []))}")

        # Add relevant entities
        entities = domain_model.get("domain_entities", [])
        for entity in entities:
            if self._is_entity_relevant_to_module(entity, module_name):
                formatted.append(f"\n实体: {entity.get('name', '')}")
                formatted.append(f"描述: {entity.get('description', '')}")

        return "\n".join(formatted) if formatted else "无相关领域模型数据"

    def _extract_module_relevant_markdown(self, domain_model: Dict[str, Any], module_name: str) -> str:
        """Extract module-relevant content from markdown domain model."""
        raw_markdown = domain_model.get("raw_markdown", "")
        sections = domain_model.get("sections", {})

        # For now, return the full markdown content
        # In the future, we could implement more sophisticated filtering
        return raw_markdown
    
    def _is_context_relevant_to_module(self, context: Dict[str, Any], module_name: str) -> bool:
        """Check if a bounded context is relevant to the module."""
        context_name = context.get("name", "").lower()
        if "用户" in context_name and "user" in module_name:
            return True
        if "服务器" in context_name and "server" in module_name:
            return True
        return False
    
    def _is_aggregate_relevant_to_module(self, aggregate: Dict[str, Any], module_name: str) -> bool:
        """Check if an aggregate is relevant to the module."""
        aggregate_name = aggregate.get("name", "").lower()
        if "用户" in aggregate_name and "user" in module_name:
            return True
        if "服务器" in aggregate_name and "server" in module_name:
            return True
        return False
    
    def _is_entity_relevant_to_module(self, entity: Dict[str, Any], module_name: str) -> bool:
        """Check if an entity is relevant to the module."""
        entity_name = entity.get("name", "").lower()
        if "user" in entity_name and "user" in module_name:
            return True
        if "server" in entity_name and "server" in module_name:
            return True
        return False
    
    def _load_project_rules(self, context: Optional[WorkflowContext] = None) -> str:
        """Load project rules, prioritizing processed rules if available."""
        # Check if processed rules file is available in context
        processed_rules_file = None
        if context and hasattr(context, 'additional_context'):
            processed_rules_file = context.additional_context.get("processed_rules_file")

        # Load processed rules if available
        if processed_rules_file and Path(processed_rules_file).exists():
            try:
                processed_rules = Path(processed_rules_file).read_text(encoding='utf-8')
                self.logger.info(f"Using processed rules from: {processed_rules_file}")
                return processed_rules
            except Exception as e:
                self.logger.warning(f"Failed to load processed rules from {processed_rules_file}: {e}")

        # Fallback to original rules loading logic
        project_rules = ""
        try:
            rules_path = Path(".roo/rules/rules.md")
            if rules_path.exists():
                project_rules = rules_path.read_text(encoding='utf-8')
        except Exception:
            pass

        # Load agent-specific rules
        agent_rules = self.get_agent_rules()

        # Combine rules
        combined_rules = []
        if project_rules:
            combined_rules.append("# Project-Specific Rules\n\n" + project_rules)
        if agent_rules:
            combined_rules.append("# Development Standards and Guidelines\n\n" + agent_rules)

        if combined_rules:
            return "\n\n---\n\n".join(combined_rules)
        else:
            return "使用默认DDD架构规范和开发标准"

    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process domain model and generate technical requirements."""
        try:
            # Get domain model
            domain_model = input_data
            if not domain_model:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No domain model data provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )

            start_time = datetime.now()

            # Extract modules from domain model
            modules = self._extract_modules_from_domain_model(domain_model)

            # Create output directory
            output_dir = self._create_output_directory(context)

            # Generate requirements and prompts for each module
            generated_files = []

            for i, module_name in enumerate(modules, 1):
                # Generate requirement document
                requirement_content = self._generate_requirement_for_module(
                    module_name, domain_model, context, i
                )

                # Save requirement file
                requirement_filename = f"{i:02d}_{module_name}_requirements.md"
                requirement_path = output_dir / requirement_filename
                requirement_path.write_text(requirement_content, encoding='utf-8')
                generated_files.append(str(requirement_path))

                # Generate prompt document
                prompt_content = self._generate_prompt_for_module(
                    module_name, requirement_content, i
                )

                # Save prompt file
                prompt_filename = f"prompt_{i:02d}_{module_name}.md"
                prompt_path = output_dir / prompt_filename
                prompt_path.write_text(prompt_content, encoding='utf-8')
                generated_files.append(str(prompt_path))

                if self.verbose:
                    print(f"[SUCCESS] 已生成 {module_name} 模块的需求文档和提示词")

            execution_time = (datetime.now() - start_time).total_seconds()

            return AgentResult(
                success=True,
                data={
                    "modules": modules,
                    "generated_files": generated_files,
                    "output_directory": str(output_dir),
                    "file_count": len(generated_files)
                },
                metadata={
                    "agent_name": self.name,
                    "modules_processed": len(modules),
                    "output_format": "markdown"
                },
                errors=[],
                execution_time=execution_time,
                timestamp=datetime.now()
            )

        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Error processing domain model: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )

    def _format_domain_model(self, domain_model: Dict[str, Any]) -> str:
        """Format domain model data for display."""
        formatted = []

        # Add bounded contexts
        bounded_contexts = domain_model.get("bounded_contexts", [])
        if bounded_contexts:
            formatted.append("=== 边界上下文 ===")
            for context in bounded_contexts:
                formatted.append(f"- {context.get('name', '')}: {', '.join(context.get('responsibilities', []))}")

        # Add aggregates
        aggregates = domain_model.get("aggregates", [])
        if aggregates:
            formatted.append("\n=== 聚合 ===")
            for aggregate in aggregates:
                formatted.append(f"- {aggregate.get('name', '')}")
                formatted.append(f"  聚合根: {aggregate.get('aggregate_root', '')}")
                formatted.append(f"  实体: {', '.join(aggregate.get('entities', []))}")

        # Add entities
        entities = domain_model.get("domain_entities", [])
        if entities:
            formatted.append("\n=== 领域实体 ===")
            for entity in entities:
                formatted.append(f"- {entity.get('name', '')}: {entity.get('description', '')}")

        return "\n".join(formatted) if formatted else "无领域模型数据"
