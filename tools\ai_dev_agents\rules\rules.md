# Roo AI - FastAPI & DDD 项目代码生成规范

本文档为 AI 代码生成助手（Roo）提供指导，确保在基于 FastAPI 和领域驱动设计（DDD）的 Python 项目中生成的代码符合既定的架构原则和高质量标准。

## 1. 核心技术栈与原则

- **Web 框架**: **FastAPI**
- **数据验证与建模**: **Pydantic**
- **ORM 与数据库**: **SQLAlchemy** (与 Alembic 配合进行数据库迁移)
- **代码风格**: 严格遵循 **PEP 8** 规范。
- **类型提示 (Type Hinting)**: **强制要求**。所有函数签名和变量声明都必须包含明确的类型提示。
- **核心思想**: **领域驱动设计 (DDD)**。所有开发活动都应围绕领域模型展开，并严格遵守分层架构的职责分离原则。
- **文档语言**: **强制要求**。所有注释、文档字符串和文档内容必须使用**英文**编写。

## 2. 架构设计与项目结构

整体架构遵循领域驱动设计（DDD）和分层原则，但项目文件结构以 **业务模块（Module）** 为第一组织层级。

### 2.1 项目整体结构

项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。通用代码则放置在根目录的 `common` 文件夹下。

```
.
├── modules/
│   ├── auth/  # 用户认证模块
│   │   ├── __init__.py
│   │   ├── application/
│   │   ├── domain/
│   │   ├── infrastructure/
│   │   └── interfaces/
│   └── orders/ # 订单模块
│       ├── __init__.py
│       ├── application/
│       ├── domain/
│       ├── infrastructure/
│       └── interfaces/
├── common/  # 通用代码
│   ├── __init__.py
│   ├── security.py
│   └── db/
│       └── database.py
├── tests/
└── main.py
```

### 2.2 模块内部分层架构

每个业务模块（例如 `modules/auth/`）内部都遵循以下经典四层 DDD 架构。

```mermaid
graph TD;
    subgraph "模块外部"
        Client[客户端 / 其他模块]
    end

    subgraph "接口层 (Interfaces Layer)"
        A[<b>interfaces/</b><br>FastAPI Routers<br>Schemas (Pydantic)<br><i>职责：处理HTTP请求，数据转换</i>]
    end

    subgraph "应用层 (Application Layer)"
        B[<b>application/</b><br>Application Services<br>Use Cases / DTOs<br><i>职责：编排领域逻辑，协调用例</i>]
    end

    subgraph "领域层 (Domain Layer)"
        C[<b>domain/</b><br>Entities & Aggregates<br>Value Objects<br>Repository Interfaces<br><i>职责：核心业务逻辑</i>]
    end

    subgraph "基础设施层 (Infrastructure Layer)"
        D[<b>infrastructure/</b><br>Repository Implementations<br>ORM Models (SQLAlchemy)<br><i>职责：技术实现</i>]
    end

    Client --> A;
    A --> B;
    B --> C;
    D -- "实现 (Implements)" --> C;
```
*注意：上图描述了单个模块内部的依赖关系。*

### 2.3 核心依赖规则 (不可违背)

1.  **模块化**: 新功能应在 `modules/` 目录下创建新的业务模块，或在现有模块内扩展。
2.  **模块内部单向依赖**: 在一个模块内，依赖关系是严格单向的：`Interfaces` → `Application` → `Domain`。
3.  **依赖倒置**: `Application` 层依赖 `Domain` 层定义的 **抽象接口** (例如 `UserRepository`)，而 `Infrastructure` 层 **实现** 这些接口。
4.  **领域层纯粹性**: `Domain` 层是项目的核心。**绝对禁止** 在该层中出现任何对 `fastapi`, `sqlalchemy` 或其他外部框架的 `import` 语句。
5.  **模块间通信**: 一个业务模块如果需要调用另一个模块的功能，**必须** 通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。
6.  **通用模块使用**: 根目录 `common/` 或 `utils/` 下的通用代码可以被任何层级按需导入，但通用代码本身不能反向依赖任何业务模块。

## 3. 各层详细实现规范

以下规范适用于单个业务模块内（例如 `modules/auth/`）。

### 3.1 `interfaces/` (接口层)

- **位置**: `modules/{module_name}/interfaces/`
- **内容**:
  - `{module_name}_api.py` (例如 `auth_api.py`): 定义 FastAPI `APIRouter`，文件名应体现业务模块。
  - `schemas.py`: 定义此模块 API 专用的 Pydantic 请求/响应模型。
- **职责**:
  - 仅负责 HTTP 协议相关事务（解析请求、序列化响应、处理 HTTP 错误），不包含任何业务逻辑。
  - 通过依赖注入调用应用层服务。
  - **API 文档要求**: 所有端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档。文档内容必须与代码变更保持同步更新。

### 3.2 `application/` (应用层)

- **位置**: `modules/{module_name}/application/`
- **内容**:
  - `services.py`: 定义应用服务类（如 `AuthService`），封装业务用例。
  - `dtos.py` (可选): 定义应用层内部或与接口层交互的数据传输对象。
- **职责**: 编排领域对象和仓库来完成一个完整的业务用例。事务边界通常在这里定义。

### 3.3 `domain/` (领域层)

- **位置**: `modules/{module_name}/domain/`
- **内容**:
  - `models.py`: 定义领域实体（Entities）和值对象（Value Objects）。应包含丰富的业务方法。
  - `repositories.py`: 定义数据持久化的抽象接口（如 `UserRepository`）。
- **职责**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。

### 3.4 `infrastructure/` (基础设施层)

- **位置**: `modules/{module_name}/infrastructure/`
- **内容**:
  - `repositories.py`: 提供 `Domain` 层仓库接口的具体实现。为保持清晰，此文件可包含模块内所有仓库的实现。
    - **命名规范**: 实现类的命名应遵循 `[InterfaceName]Impl` 格式，例如 `UserRepositoryImpl`。**严禁**在命名中包含具体技术（如 `SQLAlchemy`）。
  - `orm.py` (或 `models.py`): 定义与数据库表严格映射的 SQLAlchemy 模型。
- **职责**: 实现所有与外部世界（数据库、文件系统、第三方 API）交互的技术细节。负责领域模型与 ORM 模型之间的转换。

## 4. 代码生成约束与流程

### 案例：在 `orders` 模块中添加“创建订单”功能

1.  **创建/定位模块**: 确保 `modules/orders/` 模块存在。
2.  **Domain First (`modules/orders/domain/`)**:
    - 在 `models.py` 中创建 `Order` 实体。
    - 在 `repositories.py` 中定义抽象的 `OrderRepository` 接口（**注意**: 非 `IOrderRepository`）。
3.  **Application (`modules/orders/application/`)**:
    - 在 `services.py` 中创建 `OrderService`。
    - 注入 `OrderRepository` 接口，并创建 `create_order` 方法。
4.  **Infrastructure (`modules/orders/infrastructure/`)**:
    - 在 `orm.py` 中创建 `OrderORM` SQLAlchemy 模型。
    - 在 `repositories.py` 中创建 `OrderRepositoryImpl`，实现 `OrderRepository` 接口。
5.  **Interface (`modules/orders/interfaces/`)**:
    - 在 `schemas.py` 中创建 `OrderCreateRequest` 和 `OrderResponse` Pydantic 模型。
    - 在 `api.py` 中创建一个新的 POST `/orders/` 端点，并使用 `Depends` 注入 `OrderService`。
6.  **集成到主应用**:
    - 在顶层的 `main.py` 文件中，导入 `orders` 模块的 `api` router，并使用 `app.include_router()` 将其挂载到主应用中。
## 5. 自动化测试规范 (Automated Testing)

为了确保代码质量和系统稳定性，所有代码生成和修改都必须伴随着严格的自动化测试。

### 5.1 测试框架与工具

- **主要框架**: **Pytest** 是项目的标准测试框架。
- **HTTP 测试**: 使用 FastAPI 提供的 `TestClient` 进行接口测试。
- **Mocking**: 使用 `unittest.mock` 或 `pytest-mock` 来模拟依赖项，实现单元测试的隔离性。

### 5.2 测试目录结构

`tests` 目录的结构必须与 `modules` 目录保持一致，按业务模块进行组织。

```
tests/
├── __init__.py
├── auth/  # 'auth' 模块的测试
│   ├── __init__.py
│   ├── test_application_services.py
│   ├── test_domain_models.py
│   └── test_interfaces_api.py
└── orders/ # 'orders' 模块的测试
    ├── __init__.py
    ├── test_application_services.py
    ├── test_domain_models.py
    └── test_interfaces_api.py
```

### 5.3 测试类型与分层

- **单元测试 (Unit Tests)**:
  - **位置**: `tests/{module_name}/test_domain_models.py`, `tests/{module_name}/test_application_services.py`
  - **目标**: 专注于测试单一模块或函数的逻辑正确性。
  - **`Domain Layer` 测试**: 测试实体（Entity）的业务方法。这些测试不应有任何外部依赖。
  - **`Application Layer` 测试**: 测试应用服务（Service）的业务流程。在此类测试中，应**模拟 (Mock)** 仓库（Repository）和其他外部依赖项，确保只测试服务本身的逻辑。

- **接口/集成测试 (Interface/Integration Tests)**:
  - **位置**: `tests/{module_name}/test_interfaces_api.py`
  - **目标**: 测试从 HTTP 端点到数据库的完整流程，验证各层之间的集成是否正确。
  - **方法**: 使用 `TestClient` 发起真实的网络请求，并与测试数据库进行交互来验证端到端的行为。
  - **依赖覆盖**: 依赖注入系统应该被用来覆盖（override）真实的依赖，例如使用一个独立的测试数据库。

### 5.4 核心测试原则

1.  **测试伴随**: 任何新的功能代码或对现有逻辑的修改，都**必须**有相应的测试用例进行覆盖。
2.  **极狐验证**: 代码生成后，必须立即运行相关的测试套件。所有测试用例**必须通过**。
3.  **优先修复代码**: 当出现测试失败时，首要任务是分析并**修复生产代码**中的问题。
4.  **禁止随意修改测试**: **严禁**为了让测试通过而随意修改测试用例本身。只有在需求变更或测试用例设计本身存在缺陷时，才允许修改测试。

### 5.5 测试用例命名规范
- **命名格式**：使用 `should_[预期行为]_when_[条件]` 格式（BDD风格）
- **语言要求**：必须使用**英文**描述测试场景
- **示例**：
  ```python
  def should_return_access_token_when_valid_credentials_provided()
  def should_deny_access_when_invalid_password_submitted()
  def should_allow_access_to_protected_route_with_valid_token()
  ```

## 6. 工程实践与环境管理 (Engineering Practices & Environment Management)

### 6.1 依赖管理 (Dependency Management)

1.  **依赖检查**: 在生成任何需要新库的代码之前，必须首先检查 `requirements.txt` 文件，确认所需的功能库是否已经存在。
2.  **依赖同步**: 如果需要添加新的依赖，在使用 `pip install <package>` 安装后，必须立即执行 `pip freeze > requirements.txt` 命令，将新依赖固化到声明文件中，以保持环境一致性。

### 6.2 环境变量管理 (Environment Variable Management)

1.  **优先复用**: 在添加任何配置或密钥之前，必须首先检查 `.env` 文件，优先复用已有的环境变量。
2.  **命名规范**: 如需添加新的环境变量，必须确保其命名具有清晰的业务含义，并统一使用 `AI4SE_MCP_HUB_` 作为前缀。
3.  **同步更新**: 添加新的环境变量后，应一并更新 `.env.example` 文件（如果存在），以方便其他开发者配置环境。

### 6.3 验证流程 (Verification Workflow)

1.  **测试提醒**: 任何代码（无论是新增还是修改）生成之后，都必须提醒用户运行相关的自动化测试套件，并将所有测试通过作为代码生成活动成功完成的标志。

### 6.4 虚拟环境管理 (Virtual Environment Management)

1.  **环境检查**: 在生成任何需要执行终端命令的指令之前，必须首先提醒用户，确保他们已经激活了项目的 `venv` 虚拟环境。
2.  **激活指令**: 应当在必要时，提供标准的激活命令作为参考：
    - **Windows**: `.\.venv\Scripts\activate`
    - **macOS/Linux**: `source .venv/bin/activate`
3.  **安全执行**: 在不确定环境状态时，应优先考虑将激活命令与真实命令串联执行，以保证命令在正确的依赖环境中运行。

## 6.5 数据库迁移规范 (Database Migration Practices)

1. **禁止自动建表**: 禁止在应用启动时自动创建数据库表（例如使用 `Base.metadata.create_all`）。所有数据库结构的变更必须通过 Alembic 迁移脚本完成，以确保数据库版本可控和可追溯。

## 6.6 数据库设计约束 (Database Design Constraints)

### 6.6.1 主键设计约束

1. **UUID 主键约束**: **强制要求**。所有实体的主键字段必须使用 UUID 类型，严禁使用 Integer 或其他类型作为主键。
   - **领域模型**: 所有实体类的 `id` 字段必须声明为 `UUID` 类型
   - **ORM 模型**: 使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`
   - **API Schema**: 所有涉及实体 ID 的 Pydantic 模型字段必须声明为 `UUID` 类型
   - **路由参数**: FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型
   - **外键关系**: 所有外键字段也必须使用 UUID 类型以保持一致性

### 6.6.2 字段命名约束

2. **业务语义命名**: **强制要求**。数据库字段名必须只反映业务含义，严禁包含技术实现细节。
   - **禁止技术后缀**: 严禁在字段名中包含技术类型信息，如 `id_uuid`, `name_str`, `count_int`, `is_active_bool`, `data_json` 等
   - **业务含义优先**: 字段名应清晰表达业务概念，如 `user_id`, `created_at`, `email`, `status` 等
   - **一致性要求**: 相同业务概念在不同表中应使用相同的字段名

   **正确示例**:
   ```sql
   -- ✅ 正确：只包含业务含义
   CREATE TABLE users (
       id UUID PRIMARY KEY,
       username VARCHAR(50) NOT NULL,
       email VARCHAR(100) NOT NULL,
       created_at TIMESTAMP DEFAULT NOW()
   );

   CREATE TABLE oauth_accounts (
       id UUID PRIMARY KEY,
       user_id UUID REFERENCES users(id),
       provider VARCHAR(20) NOT NULL,
       provider_account_id VARCHAR(255) NOT NULL
   );
   ```

   **错误示例**:
   ```sql
   -- ❌ 错误：包含技术实现细节
   CREATE TABLE users (
       id_uuid UUID PRIMARY KEY,           -- 包含技术类型
       username_str VARCHAR(50),           -- 包含技术类型
       email_varchar VARCHAR(100),         -- 包含技术类型
       is_active_bool BOOLEAN,             -- 包含技术类型
       metadata_json JSONB                 -- 包含技术类型
   );
   ```

### 6.6.3 表设计约束

3. **表命名规范**:
   - 使用复数形式的英文单词，如 `users`, `oauth_accounts`, `orders`
   - 使用下划线分隔多个单词，如 `user_profiles`, `order_items`
   - 避免缩写，使用完整的业务术语

4. **索引设计**:
   - 主键自动创建唯一索引
   - 外键字段应创建索引以提高查询性能
   - 经常用于查询条件的字段应考虑创建索引
   - 唯一约束字段自动创建唯一索引

### 6.6.4 实现示例

**完整的实体设计示例**:
```python
# 领域模型
class User(BaseModel):
    id: UUID
    username: str
    email: EmailStr
    status: UserStatus
    created_at: datetime

# ORM 模型
class UserDB(Base):
    __tablename__ = "users"

    id: ColumnType[UUID] = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username: ColumnType[str] = Column(String(50), unique=True, nullable=False)
    email: ColumnType[str] = Column(String(100), unique=True, nullable=False)
    status: ColumnType[UserStatus] = Column(Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False)
    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())

# API Schema
class UserPublic(BaseModel):
    id: UUID
    username: str
    email: EmailStr
    status: UserStatus

# API 路由
@router.get("/users/{user_id}")
async def get_user(user_id: UUID = Path(..., description="User unique identifier")):
    pass
```

## 7. 模块内代码组织规范 (Intra-Module Code Organization)

### 7.1 业务子域分离原则

当单个模块内包含多个相关但职责不同的业务子域时，**强制要求**通过文件命名前缀来区分不同的业务子域，而不是创建深层嵌套的目录结构。

### 7.2 文件命名规范

#### 7.2.0 通用命名约束

**强制要求**: 所有模块内的文件必须使用业务子域前缀命名，严禁使用通用文件名。这确保了：
1. **可读性**: 文件名直接反映其业务职责
2. **可维护性**: 避免在大型项目中出现同名文件混淆
3. **一致性**: 所有模块遵循统一的命名标准

**禁止的通用命名**:
- ❌ `models.py`, `repositories.py`, `services.py`, `schemas.py`, `orm.py`
- ❌ 这些通用名称无法体现具体的业务含义

**正确的业务化命名**:
- ✅ `user_models.py`, `user_repositories.py`, `user_service.py`, `user_schemas.py`, `user_orm.py`
- ✅ `credential_auth_models.py`, `oauth_provider_service.py`

#### 7.2.1 服务层命名规范

- **格式**: `{业务子域}_{层级类型}.py`
- **业务化命名**: 避免使用过于通用的词汇（如 `auth`, `service`），应使用具体的业务术语
- **一致性要求**: 同一业务子域的所有文件必须使用相同的前缀

**示例**:
```
application/
├── credential_auth_service.py         # 用户名密码认证服务
├── oauth_authentication_service.py   # OAuth认证服务
└── oauth_provider_service.py         # OAuth提供方管理服务
```

#### 7.2.2 领域层命名规范

- **模型文件**: `{业务子域}_models.py`
- **仓库接口**: `{业务子域}_repositories.py`

**示例**:
```
domain/
├── credential_auth_models.py          # 用户凭证认证模型
├── credential_auth_repositories.py    # 用户凭证认证仓库接口
├── oauth_account_models.py           # OAuth账户模型
├── oauth_account_repositories.py     # OAuth账户仓库接口
├── oauth_provider_models.py          # OAuth提供方模型
└── oauth_provider_repositories.py    # OAuth提供方仓库接口
```

#### 7.2.3 基础设施层命名规范

- **ORM 模型**: `{业务子域}_orm.py`
- **仓库实现**: `{业务子域}_repositories.py`

**示例**:
```
infrastructure/
├── credential_auth_orm.py             # 用户凭证认证ORM
├── credential_auth_repositories.py   # 用户凭证认证仓库实现
├── oauth_account_orm.py              # OAuth账户ORM
├── oauth_account_repositories.py     # OAuth账户仓库实现
├── oauth_provider_orm.py             # OAuth提供方ORM
└── oauth_provider_repositories.py    # OAuth提供方仓库实现
```

#### 7.2.4 接口层命名规范

- **API 路由**: `{业务子域}_api.py`
- **Schema 定义**: `{业务子域}_schemas.py`

**示例**:
```
interfaces/
├── credential_auth_api.py             # 用户名密码认证API
├── credential_auth_schemas.py         # 用户名密码认证Schema
├── oauth_authentication_api.py       # OAuth认证API
├── oauth_authentication_schemas.py   # OAuth认证Schema
├── oauth_provider_api.py             # OAuth提供方管理API
├── oauth_provider_schemas.py         # OAuth提供方Schema
└── dependencies.py                    # 统一的依赖注入
```

### 7.3 业务子域识别原则

#### 7.3.1 职责分离标准

不同的业务子域必须满足以下条件之一：
1. **数据模型不同**: 操作不同的实体或聚合根
2. **业务流程不同**: 解决不同的业务问题或用例
3. **外部依赖不同**: 依赖不同的外部系统或服务
4. **生命周期不同**: 具有不同的变更频率或演进路径

#### 7.3.2 常见业务子域示例

**认证模块 (auth) 的业务子域划分**:
- `credential_auth`: 用户名密码认证
- `oauth_authentication`: OAuth第三方认证
- `oauth_provider`: OAuth提供方配置管理

**订单模块 (orders) 的业务子域划分**:
- `order_management`: 订单创建和管理
- `order_payment`: 订单支付处理
- `order_fulfillment`: 订单履行和配送

### 7.4 禁止的组织方式

#### 7.4.1 禁止使用类似 `_management` 功能性命名的后缀

**错误示例**:
```
❌ oauth_provider_management_service.py
❌ user_management_api.py
❌ order_management_models.py
```

**正确示例**:
```
✅ oauth_provider_service.py
✅ user_admin_api.py
✅ order_models.py
```

#### 7.4.2 禁止过度嵌套目录

**错误示例**:
```
❌ modules/auth/authentication/credential/application/
❌ modules/auth/provider_management/oauth/domain/
```

**正确示例**:
```
✅ modules/auth/application/credential_auth_service.py
✅ modules/auth/domain/oauth_provider_models.py
```

### 7.5 跨业务子域依赖处理

#### 7.5.1 依赖方向约束

- **同模块内**: 业务子域之间可以相互依赖，但必须通过应用层服务接口
- **跨模块**: 必须通过目标模块的应用层服务接口，严禁直接依赖其他模块的业务子域

#### 7.5.2 共享组件处理

当多个业务子域需要共享代码时：
1. **模块内共享**: 创建 `shared/` 目录存放共享组件
2. **跨模块共享**: 放置在根目录的 `common/` 或 `shared/` 目录

**示例**:
```
modules/auth/
├── application/
├── domain/
├── infrastructure/
├── interfaces/
└── shared/                    # 模块内共享组件
    └── oauth/
        └── client_service.py  # OAuth客户端工具服务
```

## 8. 重构约束原则

1. **创建新文件优先**: 任何重构操作前，应先创建新文件并完成修改，最后再删除不必要的文件。
2. **上下文分析**: 重构前需分析被重构文件相关联的代码和测试，掌握完整上下文避免错误。
3. **测试保障**: 重构后必须运行自动化测试确保功能正常。对于没有测试覆盖的部分，应询问用户是否补充测试用例。

## 9. Git 提交规范 (Commit Message Guidelines)

为了保持版本历史的清晰、可读，并能够基于提交信息自动生成变更日志（Changelog），所有 Git 提交都必须遵循**约定式提交（Conventional Commits）**规范。

### 9.1 格式

每个 commit message 都由一个 **header**, 一个可选的 **body**, 和一个可选的 **footer** 组成。

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

---

### 9.2 Header (必需)

Header 部分只有一行，包含三个字段：`type`, `scope`, 和 `subject`。

#### `type` (必需)

`type` 必须是以下关键字之一：

*   **feat**: 新功能（feature）
*   **fix**: 修复 bug
*   **docs**: 仅文档更改
*   **style**: 不影响代码含义的更改（空格、格式、缺少分号等）
*   **refactor**: 代码重构，既不修复 bug 也不添加功能
*   **perf**: 提升性能的代码更改
*   **test**: 添加或修改测试
*   **build**: 影响构建系统或外部依赖项的更改（例如：`requirements.txt`, `Dockerfile`）
*   **ci**: 对 CI 配置文件和脚本的更改（例如：GitHub Actions）
*   **chore**: 其他不修改 `src` 或 `test` 文件的更改（例如：更新 `.gitignore`）
*   **revert**: 撤销之前的 commit

#### `scope` (可选)

`scope` 用于指定本次 commit 影响的范围。在本项目中，`scope` 应该是受影响的**业务模块名**。

*   **示例**: `auth`, `orders`, `common`, `tests`

#### `subject` (必需)

`subject` 是对 commit 目的的简短描述，不超过 50 个字符。

*   使用祈使句，现在时态："change" not "changed" nor "changes"。
*   第一个字母不要大写。
*   末尾不加句号（`.`）。
*   **语言要求**: 必须使用**英文**。

---

### 9.3 Body (可选)

`body` 部分是对本次 commit 的详细描述，可以分成多行。

*   与 `subject` 一样，使用祈使句，现在时态。
*   应该包括改变的动机，以及和之前行为的对比。
*   **语言要求**: 必须使用**英文**。

---

### 9.4 Footer (可选)

`footer` 部分只用于两种情况：

1.  **重大更改 (Breaking Change)**:
    *   以 `BREAKING CHANGE:` 开头，后面是对变动的描述、理由和迁移方法。

2.  **关闭 Issue**:
    *   例如: `Closes #123`, `Fixes #456`

---

### 9.5 示例

**修复 `auth` 模块中的密码验证 bug:**
```
fix(auth): correct password verification logic

The previous implementation was using an incorrect salt, causing all password checks to fail. This commit corrects the salt retrieval process.

Closes #234
```

**为 `orders` 模块添加新功能，并包含重大更改:**
```
feat(orders): allow multiple items per order

Users can now add more than one item to their shopping cart and create an order with all of them.

BREAKING CHANGE: The `create_order` service method signature has changed from `(item_id: int)` to `(item_ids: List[int])`. All clients must be updated.
```

---

### 9.6 提交策略：原子化与分批提交

为了保持版本历史的清晰和可追溯性，强烈建议遵循**原子化提交**的原则。当一次开发任务包含多种性质的变更（例如，既重构了代码，又添加了新功能，还更新了测试），应当将这些变更**分批提交**。

- **核心原则**: 每个提交应只关注一个独立的、逻辑相关的变更。
- **目的**:
    - 使代码审查（Code Review）更轻松、更聚焦。
    - 简化问题追溯和变更回滚 (`git revert`)。
    - 保持每个提交信息 (`feat`, `fix`, `docs` 等) 的纯粹性。

- **推荐的分批提交示例**:
  在一次大型重构后，提交序列可能如下：
  1. `git commit -m "docs: update development guidelines"`
  2. `git commit -m "refactor(auth): migrate authentication logic to modular structure"`
  3. `git commit -m "test(auth): refactor test cases for new architecture"`
  4. `git commit -m "build(deps): add email-validator dependency"`