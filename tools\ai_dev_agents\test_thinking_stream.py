#!/usr/bin/env python3
"""
Test script for thinking stream output functionality.

This script tests whether the thinking process from reasoning models
is properly displayed in streaming mode.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import ConfigManager
from utils.stream_displayer import StreamDisplayer
from core.base_agent import BaseAgent, AgentResult


class TestAgent(BaseAgent):
    """Simple test agent for testing thinking stream."""

    def process(self, input_data, **kwargs):
        """Simple process implementation for testing."""
        return AgentResult(success=True, data="Test completed")


def test_thinking_stream():
    """Test thinking stream display functionality."""
    print("[TEST] Testing thinking stream output...")
    
    # Initialize config manager
    config_manager = ConfigManager()
    
    # Get streaming config
    streaming_config = config_manager.get_streaming_config()
    print(f"[CONFIG] Streaming config: {streaming_config}")
    
    # Initialize stream displayer
    stream_displayer = StreamDisplayer(
        enabled=streaming_config.get('enabled', True),
        show_thinking=streaming_config.get('show_thinking', True)
    )
    
    print(f"[CONFIG] Stream displayer enabled: {stream_displayer.enabled}")
    print(f"[CONFIG] Show thinking: {stream_displayer.show_thinking}")
    
    # Test thinking content simulation
    print("\n[TEST] Simulating thinking stream chunks...")
    
    # Simulate thinking stream
    test_chunks = [
        "Let me think about this problem step by step.\n\n<thinking>\n",
        "First, I need to understand what the user is asking for.\n",
        "They want to test the thinking stream output functionality.\n",
        "This means I should:\n",
        "1. Analyze the current implementation\n",
        "2. Identify potential issues\n",
        "3. Provide a solution\n",
        "</thinking>\n\n",
        "Based on my analysis, I can see that the thinking stream should work properly now."
    ]
    
    stream_displayer.start_agent_session("TestAgent", "Testing thinking stream")
    
    for i, chunk in enumerate(test_chunks):
        print(f"\n[DEBUG] Processing chunk {i+1}: {repr(chunk[:50])}...")
        result = stream_displayer.process_stream_chunk(chunk)
    
    stream_displayer.complete_agent_session(True, "Test completed successfully")
    
    print(f"\n[RESULT] Final accumulated content length: {len(stream_displayer.current_content)}")
    print(f"[RESULT] Thinking content length: {len(stream_displayer.thinking_content)}")
    print(f"[RESULT] Output content length: {len(stream_displayer.output_content)}")


def test_real_llm_thinking():
    """Test with real LLM that supports thinking."""
    print("\n[TEST] Testing with real LLM...")
    
    try:
        # Initialize config manager and create LLM
        config_manager = ConfigManager()
        llm = config_manager.create_llm()

        if llm is None:
            print("[ERROR] Failed to create LLM instance")
            return

        llm_config = config_manager.get_llm_config()
        print(f"[LLM] Using model: {llm_config.provider}/{llm_config.model}")
        
        # Initialize stream displayer
        streaming_config = config_manager.get_streaming_config()
        stream_displayer = StreamDisplayer(
            enabled=streaming_config.get('enabled', True),
            show_thinking=streaming_config.get('show_thinking', True)
        )
        
        # Create a simple agent for testing
        test_agent = TestAgent(
            name="ThinkingTestAgent",
            llm=llm,
            verbose=True,
            stream_displayer=stream_displayer
        )
        
        # Test with a simple thinking prompt
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant. When answering, please show your thinking process using <thinking> tags."
            },
            {
                "role": "user", 
                "content": "What is 2+2? Please show your thinking process."
            }
        ]
        
        print("\n[TEST] Executing LLM call with thinking...")
        response = test_agent._execute_llm_call_with_streaming(messages, "Testing thinking with real LLM")
        
        print(f"\n[RESULT] Response received: {len(response)} characters")
        print(f"[RESULT] Contains thinking tags: {'<thinking>' in response}")
        
    except Exception as e:
        print(f"[ERROR] Real LLM test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("=" * 60)
    print("THINKING STREAM OUTPUT TEST")
    print("=" * 60)
    
    # Test 1: Simulate thinking stream
    test_thinking_stream()
    
    # Test 2: Real LLM test (if available)
    test_real_llm_thinking()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED")
    print("=" * 60)
