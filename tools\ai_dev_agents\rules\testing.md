# Testing Rules

## Test Framework

### Pytest Standards
- Use pytest as the primary testing framework
- Use FastAPI TestClient for API testing
- Use unittest.mock or pytest-mock for mocking

### Test Organization
- Mirror source code structure in tests
- Group tests by functionality
- Use descriptive test class and method names

## Test Types

### Unit Tests
- Test individual functions and methods
- Mock all external dependencies
- Focus on business logic
- Fast execution (< 1 second per test)

### Integration Tests
- Test component interactions
- Use test database for data operations
- Test complete request/response cycles
- Clean up test data after each test

### API Tests
- Test all endpoints
- Test authentication and authorization
- Test error conditions
- Validate response schemas

## Test Data Management

### Test Isolation
- Each test manages its own test data
- No shared test data between tests
- Clean up after each test
- Use factories for test data creation

### Database Testing
- Use separate test database
- Reset database state between tests
- Use transactions for test isolation
- Test database migrations

## Test Quality

### Coverage Requirements
- **MANDATORY**: Minimum 80% code coverage
- Cover all business logic paths
- Test error conditions
- Test edge cases

### Test Naming
- Use descriptive names: `should_[expected]_when_[condition]`
- **MANDATORY**: All test names in English
- Include context in test names
- Group related tests in classes
