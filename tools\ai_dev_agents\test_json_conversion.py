#!/usr/bin/env python3
"""
Temporary test script to verify JSON format conversion in DomainModeler
"""

import sys
import json
import re
from pathlib import Path
from typing import Dict, Any, List

# Add the parent directory to Python path to access modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# Mock the dependencies to test JSON processing in isolation
class MockLogger:
    def error(self, msg): print(f"ERROR: {msg}")
    def info(self, msg): print(f"INFO: {msg}")

class MockDomainModeler:
    def __init__(self):
        self.logger = MockLogger()

    def _process_json_response(self, response: str) -> Dict[str, Any]:
        """Process JSON response and extract structured data."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        import re
        import json

        # Try multiple strategies to extract JSON
        json_content = None

        # Strategy 1: Look for JSON code blocks
        json_matches = re.findall(r'```(?:json)?\s*(.*?)\s*```', response, re.DOTALL)
        if json_matches:
            json_content = json_matches[0].strip()

        # Strategy 2: Look for content between { and } (outermost braces)
        if not json_content:
            brace_match = re.search(r'\{.*\}', response, re.DOTALL)
            if brace_match:
                json_content = brace_match.group(0).strip()

        # Strategy 3: Use entire response as fallback
        if not json_content:
            json_content = response.strip()

        try:
            # Parse JSON content
            parsed_data = json.loads(json_content)

            if isinstance(parsed_data, dict):
                # Ensure content_type is set
                if "content_type" not in parsed_data:
                    parsed_data["content_type"] = "domain_model"

                return parsed_data
            else:
                self.logger.error(f"JSON parsing resulted in non-dict type: {type(parsed_data)}")
                return {"content_type": "domain_model", "raw_json": json_content, "parsing_error": "Non-dict result"}

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"JSON content: {json_content[:500]}...")
            # Return a fallback structure with the raw content for debugging
            return {
                "content_type": "domain_model",
                "raw_json": json_content,
                "parsing_error": str(e),
                "raw_response": response[:1000]  # Include original response for debugging
            }

    def _validate_json_content(self, data: Dict[str, Any]) -> List[str]:
        """Validate JSON content structure."""
        errors = []

        # Check required top-level fields
        required_fields = [
            "bounded_contexts", "aggregates", "domain_entities",
            "value_objects", "domain_services", "repositories", "domain_events"
        ]

        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")

        return errors


def test_json_parsing():
    """Test JSON parsing with various response formats"""

    # Initialize the mock agent
    agent = MockDomainModeler()
    
    # Test case 1: Valid JSON in code block
    test_response_1 = '''
    Here is the domain model:
    
    ```json
    {
        "content_type": "domain_model",
        "bounded_contexts": [
            {
                "name": "test_context",
                "description": "Test context"
            }
        ]
    }
    ```
    
    This completes the analysis.
    '''
    
    print("=== Test Case 1: JSON in code block ===")
    result_1 = agent._process_json_response(test_response_1)
    print(f"Result: {json.dumps(result_1, indent=2)}")
    print(f"Success: {'parsing_error' not in result_1}")
    print()
    
    # Test case 2: JSON without code block
    test_response_2 = '''
    {
        "content_type": "domain_model",
        "aggregates": [
            {
                "name": "test_aggregate",
                "entities": ["TestEntity"]
            }
        ]
    }
    '''
    
    print("=== Test Case 2: Raw JSON ===")
    result_2 = agent._process_json_response(test_response_2)
    print(f"Result: {json.dumps(result_2, indent=2)}")
    print(f"Success: {'parsing_error' not in result_2}")
    print()
    
    # Test case 3: JSON with extra text
    test_response_3 = '''
    Based on the analysis, here is the domain model:
    
    {
        "content_type": "domain_model",
        "domain_entities": [
            {
                "name": "User",
                "attributes": [
                    {"name": "id", "type": "UUID"}
                ]
            }
        ]
    }
    
    This model follows DDD principles.
    '''
    
    print("=== Test Case 3: JSON with surrounding text ===")
    result_3 = agent._process_json_response(test_response_3)
    print(f"Result: {json.dumps(result_3, indent=2)}")
    print(f"Success: {'parsing_error' not in result_3}")
    print()
    
    # Test case 4: Invalid JSON
    test_response_4 = '''
    This is not valid JSON content.
    No JSON structure here.
    '''
    
    print("=== Test Case 4: Invalid JSON ===")
    result_4 = agent._process_json_response(test_response_4)
    print(f"Result: {json.dumps(result_4, indent=2)}")
    print(f"Expected failure: {'parsing_error' in result_4}")
    print()


def test_json_validation():
    """Test JSON content validation"""

    agent = MockDomainModeler()
    
    # Valid JSON structure
    valid_data = {
        "content_type": "domain_model",
        "concept_analysis": {"similar_concepts": []},
        "bounded_contexts": [{"name": "test", "description": "test"}],
        "aggregates": [{"name": "test", "entities": ["Test"]}],
        "domain_entities": [{"name": "Test", "attributes": []}],
        "value_objects": [],
        "domain_services": [],
        "repositories": [],
        "domain_events": []
    }
    
    print("=== Test JSON Validation ===")
    errors = agent._validate_json_content(valid_data)
    print(f"Validation errors: {errors}")
    print(f"Success: {len(errors) == 0}")
    print()
    
    # Invalid JSON structure (missing required sections)
    invalid_data = {
        "content_type": "domain_model",
        "bounded_contexts": []
    }
    
    errors_invalid = agent._validate_json_content(invalid_data)
    print(f"Validation errors for incomplete data: {errors_invalid}")
    print(f"Expected failures: {len(errors_invalid) > 0}")
    print()


def test_system_prompt_loading():
    """Test system prompt loading"""

    print("=== Test System Prompt Loading ===")
    try:
        import yaml
        prompts_path = Path(__file__).parent / "prompts" / "domain_modeling.yaml"
        with open(prompts_path, 'r', encoding='utf-8') as f:
            prompts = yaml.safe_load(f)

        if 'complete_system_prompt' in prompts:
            prompt = prompts['complete_system_prompt']
        else:
            prompt = prompts.get('system_prompt', '')

        print(f"Prompt loaded successfully: {len(prompt)} characters")
        print(f"Contains JSON format instruction: {'JSON' in prompt}")
        print(f"Sample: {prompt[:200]}...")
    except Exception as e:
        print(f"Error loading prompt: {e}")
    print()


if __name__ == "__main__":
    print("Testing JSON conversion in DomainModeler...")
    print("=" * 50)
    
    test_json_parsing()
    test_json_validation()
    test_system_prompt_loading()
    
    print("=" * 50)
    print("Testing completed!")
