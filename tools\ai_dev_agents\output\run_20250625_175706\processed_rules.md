<!-- 
处理后的开发规则
生成时间: 2025-06-25 17:58:51
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年10月15日
- **规则范围**: FastAPI & DDD Python项目开发规范
- **主要改进**: 
  - 重构逻辑结构，消除冗余内容
  - 强化架构约束和分层原则
  - 补充测试规范和工程实践细节
  - 明确禁止性约束和最佳实践
  - 统一命名规范和代码组织标准

## 2. 核心原则
- **领域驱动设计(DDD)**: 所有开发活动围绕领域模型展开，确保业务逻辑与技术实现分离
- **分层架构**: 严格遵循接口层→应用层→领域层→基础设施层的单向依赖关系
- **模块化设计**: 按业务功能划分独立模块，模块间通过应用层服务接口通信
- **质量优先**: 强制类型提示(Python)、PEP8代码规范、全面文档和测试覆盖
- **纯粹领域层**: 领域层禁止引入任何外部框架依赖

## 3. 技术规范
### 技术栈要求
- **Web框架**: FastAPI
- **数据建模**: Pydantic
- **数据库**: SQLAlchemy + Alembic迁移
- **测试框架**: Pytest + FastAPI TestClient

### 编码标准
- **类型提示**: 所有函数签名和变量声明必须包含类型提示
- **文档语言**: 所有注释、文档字符串必须使用英文
- **命名规范**:
  - 文件命名：`{业务子域}_{类型}.py` (如 `user_models.py`)
  - 禁止通用文件名：`models.py`, `services.py`等
  - 类名：大驼峰式(UserService)
  - 变量：小写蛇形式(user_id)

### 框架使用规范
- **FastAPI**:
  - 路由定义在接口层`{module}/interfaces/`目录
  - 所有端点必须包含完整OpenAPI文档
- **SQLAlchemy**:
  - ORM模型定义在基础设施层
  - 禁止自动建表，必须使用Alembic迁移

## 4. 架构约束
### 分层架构规则
```mermaid
graph TD
    A[接口层 Interfaces] --> B[应用层 Application]
    B --> C[领域层 Domain]
    D[基础设施层 Infrastructure] --> C
```

### 模块组织
```
project/
├── modules/
│   ├── auth/
│   │   ├── application/
│   │   ├── domain/
│   │   ├── infrastructure/
│   │   └── interfaces/
├── common/
└── tests/
```

### 依赖关系约束
1. **模块内单向依赖**：Interfaces → Application → Domain
2. **跨模块通信**：必须通过目标模块的应用层服务接口
3. **领域层隔离**：禁止在domain/目录引入fastapi/sqlalchemy等框架依赖
4. **基础设施实现**：通过依赖倒置实现领域层定义的接口
5. **通用代码**：common/目录可被任意层引用，但不可反向依赖业务模块

## 5. 代码质量标准
### 代码风格
- 严格遵循PEP8规范
- 函数长度不超过50行
- 类长度不超过200行
- 避免深层嵌套（最大3层）

### 测试要求
- **测试结构**：
  ```
  tests/
    ├── auth/
    │   ├── test_application_services.py
    │   ├── test_domain_models.py
    │   └── test_interfaces_api.py
  ```
- **测试覆盖率**：
  - 单元测试：覆盖所有领域模型和业务逻辑
  - 集成测试：覆盖端到端业务流程
- **测试原则**：
  - 测试伴随：新功能必须包含测试用例
  - 优先修复代码：测试失败时先修复生产代码
  - 命名规范：`should_[预期行为]_when_[条件]` (英文)

### 文档标准
- 所有公共API必须包含OpenAPI文档
- 复杂业务逻辑需添加代码注释
- 数据库迁移脚本需说明变更原因

## 6. 工程实践
### 开发流程
1. Domain First：先定义领域模型和仓库接口
2. 应用层：实现业务用例服务
3. 基础设施：提供仓库实现和ORM映射
4. 接口层：创建API端点和DTO模型

### 版本控制
- **提交规范**：
  ```
  <type>(<scope>): <subject>
  ```
  类型包括：feat/fix/docs/style/refactor等
- **原子提交**：每个提交只包含单一逻辑变更
- **分支策略**：功能分支开发，PR合并前需通过CI

### 环境管理
- **依赖管理**：
  - 更新依赖后执行`pip freeze > requirements.txt`
  - 优先复用现有依赖
- **环境变量**：
  - 命名前缀：`AI4SE_MCP_HUB_`
  - 同步更新`.env`和`.env.example`
- **虚拟环境**：
  - Windows: `.\.venv\Scripts\activate`
  - Unix: `source .venv/bin/activate`

### 部署规范
- 数据库迁移必须通过Alembic执行
- 禁止应用启动时自动建表
- 生产环境配置与代码分离

## 7. 最佳实践
### 模块设计
- **业务子域划分**：
  - 认证模块：credential_auth, oauth_authentication
  - 订单模块：order_management, order_payment
- **共享组件**：
  - 模块内共享：`modules/{module}/shared/`
  - 跨模块共享：`common/`

### 性能优化
- 高频查询字段创建索引
- 批量操作使用SQLAlchemy批量API
- 避免N+1查询问题

### 异常处理
- 领域层定义业务异常
- 接口层转换异常为HTTP状态码
- 基础设施层处理技术异常

### 重构指南
1. 创建新文件完成修改
2. 分析关联代码上下文
3. 运行测试验证
4. 删除旧文件

## 8. 约束与限制
### 禁止性约束
1. **领域层**：
   - 禁止引入框架依赖
   - 禁止直接访问基础设施
2. **数据库**：
   - 禁止使用非UUID主键
   - 禁止字段名包含技术后缀（如`_str`, `_json`）
   - 禁止自动建表
3. **代码组织**：
   - 禁止使用通用文件名
   - 禁止深层嵌套目录
   - 禁止`_management`后缀
4. **测试**：
   - 禁止为通过测试而修改测试用例

### 强制性要求
1. **分层架构**：严格遵守四层依赖关系
2. **类型提示**：所有函数和变量必须声明类型
3. **测试覆盖**：新功能必须包含测试用例
4. **文档**：所有API端点必须有OpenAPI文档
5. **提交规范**：遵循约定式提交格式

### 异常处理原则
- 业务异常在领域层定义
- 技术异常在基础设施层处理
- 接口层统一转换异常为HTTP响应
- 关键异常必须有日志记录

> 本规范通过严格的架构约束和质量标准，确保项目在快速迭代中保持高可维护性和扩展性，所有开发活动必须遵循上述规则。