<!-- 
处理后的开发规则
生成时间: 2025-06-25 20:43:51
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI & DDD Python项目开发规范
- **主要改进**: 
  - 重构了文档结构，使其更加系统化
  - 消除了重复内容，如架构设计原则
  - 补充了测试规范和工程实践细节
  - 明确了数据库设计和命名约束
  - 增强了规则的可操作性

## 2. 核心原则
- **领域驱动设计(DDD)**: 所有开发活动围绕领域模型展开
- **分层架构**: 严格遵循接口层→应用层→领域层→基础设施层的单向依赖
- **模块化设计**: 按业务功能划分模块，模块内高内聚、模块间低耦合
- **类型安全**: 强制使用Python类型提示(Type Hinting)
- **文档规范**: 所有文档和注释必须使用英文

## 3. 技术规范
- **Web框架**: FastAPI
- **数据建模**: Pydantic
- **数据库**: SQLAlchemy + Alembic
- **测试框架**: Pytest
- **代码风格**: 严格遵循PEP 8规范

## 4. 架构约束
### 4.1 项目结构
```
.
├── modules/          # 业务模块
│   ├── auth/        # 认证模块
│   └── orders/      # 订单模块
├── common/          # 通用代码
├── tests/           # 测试代码
└── main.py          # 应用入口
```

### 4.2 分层架构
1. **接口层(Interfaces)**: 处理HTTP请求/响应
2. **应用层(Application)**: 编排业务用例
3. **领域层(Domain)**: 核心业务逻辑
4. **基础设施层(Infrastructure)**: 技术实现

### 4.3 依赖规则
- 模块内部严格单向依赖
- 领域层禁止依赖外部框架
- 模块间通信必须通过应用层接口
- 通用代码不得反向依赖业务模块

## 5. 代码质量标准
### 5.1 编码规范
- 强制类型提示
- 遵循PEP 8代码风格
- 业务逻辑与框架代码分离
- 领域模型保持纯粹性

### 5.2 测试要求
- **单元测试**: 覆盖领域模型和应用服务
- **集成测试**: 验证端到端流程
- **测试覆盖率**: 关键路径必须100%覆盖
- **测试命名**: 使用BDD风格(should_when格式)

### 5.3 文档标准
- API端点必须有OpenAPI文档
- 复杂逻辑必须有详细注释
- 类型提示作为补充文档
- 变更日志遵循约定式提交

## 6. 工程实践
### 6.1 开发流程
1. 领域模型优先
2. 应用服务实现用例
3. 基础设施层实现
4. 接口层暴露API
5. 配套测试开发

### 6.2 版本控制
- 提交信息遵循约定式提交规范
- 原子化提交原则
- 禁止直接推送master分支
- 代码变更必须通过CI流水线

### 6.3 环境管理
- 使用虚拟环境隔离依赖
- 环境变量统一管理
- 依赖变更及时更新requirements.txt
- 数据库迁移使用Alembic

## 7. 最佳实践
### 7.1 模块设计
- 按业务子域组织代码
- 避免过度嵌套目录
- 共享代码放入common目录
- 模块接口保持稳定

### 7.2 数据库设计
- 主键使用UUID类型
- 字段命名反映业务语义
- 禁止自动建表，必须使用迁移脚本
- 外键字段必须创建索引

### 7.3 性能优化
- 批量操作代替循环单次操作
- 合理使用缓存
- 避免N+1查询问题
- 异步处理耗时操作

## 8. 约束与限制
### 8.1 严格禁止
- 领域层引入框架依赖
- 模块间直接访问领域层
- 修改测试用例使测试通过
- 使用通用文件名(models.py等)

### 8.2 必须遵守
- 新功能必须配套测试
- 数据库变更必须通过迁移脚本
- 提交信息必须规范
- 文档必须与代码同步

### 8.3 异常处理
- 领域异常在应用层转换
- 接口层处理HTTP异常
- 关键操作必须有回滚机制
- 不可恢复错误必须记录完整上下文

## 质量要求
- **完整性**: 覆盖从架构设计到部署的全流程
- **一致性**: 各层级规则相互支撑无矛盾
- **可操作性**: 每条规则都有明确实现方式
- **可维护性**: 模块化结构便于扩展
- **专业性**: 使用准确的DDD和Python技术术语