# AI 开发提示词 - Main_Module 模块

## 任务概述
你是一个专业的 Python 后端开发工程师，需要基于以下开发需求实现 main_module 模块的完整功能。

## 开发需求
```markdown
# 技术导向业务分析报告

## 1. 技术可行性评估

### 技术栈要求
- **前端技术**：需要支持富文本编辑、代码高亮展示的框架（如React+Markdown编辑器）
- **后端技术**：需处理结构化数据存储和复杂查询（推荐Spring Boot/Node.js+GraphQL）
- **数据库**：需要支持JSON字段的关系型数据库（PostgreSQL）或文档数据库（MongoDB）
- **搜索引擎**：需实现全文检索和高级过滤（Elasticsearch）
- **安全认证**：OAuth2.0+JWT实现多平台登录

### 实现复杂度
- **高复杂度模块**：
  1. 服务器工具定义的结构化输入（需支持OpenAPI/JSON Schema验证）
  2. 动态安装指令生成（需客户端环境检测）
  3. 自动化质量评分系统（需集成GitHub API等外部服务）
- **中复杂度模块**：分类系统、用户反馈聚合

### 技术风险
1. **元数据完整性风险**：用户提交的服务器信息质量不可控
2. **性能风险**：服务器列表页面的复杂过滤查询性能
3. **安全风险**：用户安装第三方服务器代码的安全隐患

## 2. 系统架构需求

### 架构模式
推荐采用**微服务架构**，划分边界上下文：
- 目录服务（处理发现/搜索）
- 提交服务（处理生命周期管理）
- 用户服务（身份/权限）
- 社区服务（评论/反馈）

### 核心组件
1. **元数据提取引擎**：从服务器代码库自动提取工具定义
2. **动态安装生成器**：根据客户端类型生成安装指令
3. **质量评估服务**：定期扫描GitHub仓库更新安全评分
4. **搜索聚合器**：统一处理各类过滤条件

### 数据流设计
```mermaid
graph LR
   用户 -->|提交| 提交服务
   提交服务 -->|存储| 数据库
   数据库 -->|索引| Elasticsearch
   用户 -->|查询| 目录服务
   目录服务 -->|检索| Elasticsearch
   社区服务 -->|反馈数据| 推荐引擎
```

## 3. 技术约束

### 性能要求
- 列表页面加载时间 <1s（万级数据量）
- 搜索响应时间 <500ms
- 详情页API响应时间 <300ms

### 安全要求
1. 服务器安装包需进行静态代码扫描
2. 用户提交内容需XSS过滤
3. 敏感操作需二次认证

### 兼容性需求
- 必须支持主流MCP客户端配置生成：
  - Claude Desktop
  - Smithery
  - 主流IDE插件

## 4. 集成需求

### 外部系统集成
| 集成对象 | 集成方式 | 数据字段 |
|---------|----------|----------|
| GitHub API | REST | star数、commit记录 |
| 代码扫描工具 | Webhook | 安全漏洞报告 |
| 社区平台 | OAuth | 用户社交图谱 |

### 内部服务接口
```json
{
  "server_submission": {
    "endpoint": "/api/servers",
    "method": "POST",
    "schema": {
      "tools": {"type": "array", "items": {"$ref": "#/definitions/tool"}},
      "requires_validation": true
    }
  }
}
```

## 5. 数据模型需求

### 核心实体关系
```mermaid
erDiagram
    USER ||--o{ SERVER : submits
    SERVER ||--|{ TOOL : contains
    SERVER }|--|{ CATEGORY : belongs_to
    USER ||--o{ REVIEW : writes
```

### 特殊字段要求
1. **工具定义**：必须包含机器可读的OpenAPI Schema
2. **安装指令**：需支持变量替换（如${API_KEY}）
3. **质量指标**：应包含数据来源和时间戳

## 6. 关键业务规则

### 服务器提交验证
```python
def validate_submission(data):
    required_fields = ['name', 'author', 'description']
    if not all(field in data for field in required_fields):
        raise ValidationError("Missing required fields")
    
    if data['license_type'] not in APPROVED_LICENSES:
        raise ValidationError("Unapproved license")
```

### 搜索排序算法
```
权重 = 
   (关键词匹配度 * 0.4) +
   (下载量标准化值 * 0.3) + 
   (质量评分 * 0.2) +
   (更新新鲜度 * 0.1)
```

## 7. 社交领域特别考量

1. **信任体系**：
   - 实现开发者认证徽章系统
   - 用户反馈影响搜索排名
   - 透明展示评分计算方式

2. **社区驱动**：
   - 设置服务器维护者角色
   - 问题讨论区与GitHub Issues同步
   - 贡献者排行榜机制

3. **内容传播**：
   - 支持服务器分享到开发者社区
   - 集成Reddit/Discord讨论
   - 热门服务器自动推荐
```# 改进后的业务分析报告

## 1. 技术可行性评估

### 技术栈要求
- **前端技术**：
  - 需要支持富文本编辑、代码高亮展示的框架（如React+Markdown编辑器）
  - 实现响应式设计以适配不同设备
  - 集成图表库（如ECharts）用于数据可视化
- **后端技术**：
  - 需处理结构化数据存储和复杂查询（推荐Spring Boot/Node.js+GraphQL）
  - 支持OpenAPI规范的API文档生成
  - 实现WebSocket实时通信
- **数据库**：
  - 需要支持JSON字段的关系型数据库（PostgreSQL）或文档数据库（MongoDB）
  - 考虑时序数据库用于存储性能指标
- **搜索引擎**：
  - 需实现全文检索和高级过滤（Elasticsearch）
  - 支持同义词扩展和模糊搜索
- **安全认证**：
  - OAuth2.0+JWT实现多平台登录
  - 实现RBAC权限控制系统

### 实现复杂度
- **高复杂度模块**：
  1. 服务器工具定义的结构化输入（需支持OpenAPI/JSON Schema验证）
  2. 动态安装指令生成（需客户端环境检测）
  3. 自动化质量评分系统（需集成GitHub API等外部服务）
  4. 多维度搜索排序算法实现
  5. 实时数据同步机制
- **中复杂度模块**：
  1. 分类系统（支持多级分类和标签）
  2. 用户反馈聚合与分析
  3. 服务器版本管理
  4. 自动化文档生成
- **低复杂度模块**：
  1. 基础CRUD操作
  2. 静态内容管理
  3. 简单报表生成

### 技术风险
1. **元数据完整性风险**：用户提交的服务器信息质量不可控
   - 缓解措施：实现自动化验证和人工审核双机制
2. **性能风险**：服务器列表页面的复杂过滤查询性能
   - 缓解措施：引入缓存层和查询优化
3. **安全风险**：用户安装第三方服务器代码的安全隐患
   - 缓解措施：实现代码静态扫描和沙箱环境
4. **数据一致性风险**：分布式系统数据同步问题
   - 缓解措施：采用最终一致性模式和补偿事务
5. **扩展性风险**：用户量快速增长带来的系统压力
   - 缓解措施：设计可水平扩展的架构

## 2. 系统架构需求

### 架构模式
推荐采用**微服务架构**，划分边界上下文：
- 目录服务（处理发现/搜索）
- 提交服务（处理生命周期管理）
- 用户服务（身份/权限）
- 社区服务（评论/反馈）
- 分析服务（数据处理和报表）
- 通知服务（实时消息推送）

### 核心组件
1. **元数据提取引擎**：从服务器代码库自动提取工具定义
   - 支持多种语言解析
   - 提供插件机制扩展
2. **动态安装生成器**：根据客户端类型生成安装指令
   - 支持主流IDE和客户端
   - 提供预览功能
3. **质量评估服务**：定期扫描GitHub仓库更新安全评分
   - 多维度评分体系
   - 可视化评分趋势
4. **搜索聚合器**：统一处理各类过滤条件
   - 支持复杂布尔查询
   - 提供搜索建议
5. **实时同步服务**：保持各服务数据一致性
6. **自动化文档服务**：生成API文档和用户手册

### 数据流设计
```mermaid
graph LR
   用户 -->|提交| 提交服务
   提交服务 -->|存储| 数据库
   数据库 -->|索引| Elasticsearch
   用户 -->|查询| 目录服务
   目录服务 -->|检索| Elasticsearch
   社区服务 -->|反馈数据| 推荐引擎
   分析服务 -->|数据处理| 数据库
   通知服务 -->|实时推送| 用户
```

## 3. 技术约束

### 性能要求
- 列表页面加载时间 <1s（万级数据量）
- 搜索响应时间 <500ms
- 详情页API响应时间 <300ms
- 并发用户支持 >1000TPS
- 数据同步延迟 <5s

### 安全要求
1. 服务器安装包需进行静态代码扫描
   - 集成SonarQube等工具
2. 用户提交内容需XSS过滤
   - 实现多层级防护
3. 敏感操作需二次认证
   - 支持多种验证方式
4. 数据传输加密
   - 强制HTTPS
5. 定期安全审计
   - 自动化扫描+人工检查

### 兼容性需求
- 必须支持主流MCP客户端配置生成：
  - Claude Desktop
  - Smithery
  - 主流IDE插件
  - 移动端适配
  - API兼容性保证

## 4. 集成需求

### 外部系统集成
| 集成对象 | 集成方式 | 数据字段 | 频率 | 认证方式 |
|---------|----------|----------|------|----------|
| GitHub API | REST | star数、commit记录 | 实时 | OAuth |
| 代码扫描工具 | Webhook | 安全漏洞报告 | 事件驱动 | API Key |
| 社区平台 | OAuth | 用户社交图谱 | 按需 | OAuth |
| 支付系统 | REST | 交易记录 | 实时 | JWT |
| 邮件服务 | SMTP | 通知内容 | 异步 | 账号密码 |

### 内部服务接口
```json
{
  "server_submission": {
    "endpoint": "/api/servers",
    "method": "POST",
    "schema": {
      "tools": {"type": "array", "items": {"$ref": "#/definitions/tool"}},
      "requires_validation": true,
      "metadata": {
        "type": "object",
        "properties": {
          "license": {"type": "string"},
          "compatibility": {"type": "array"}
        }
      }
    }
  },
  "search": {
    "endpoint": "/api/search",
    "method": "GET",
    "parameters": {
      "query": {"type": "string"},
      "filters": {"type": "object"}
    }
  }
}
```

## 5. 数据模型需求

### 核心实体关系
```mermaid
erDiagram
    USER ||--o{ SERVER : submits
    SERVER ||--|{ TOOL : contains
    SERVER }|--|{ CATEGORY : belongs_to
    USER ||--o{ REVIEW : writes
    SERVER ||--o{ VERSION : has
    USER ||--o{ BOOKMARK : creates
    SERVER ||--o{ INSTALLATION : has
```

### 特殊字段要求
1. **工具定义**：
   - 必须包含机器可读的OpenAPI Schema
   - 支持多语言文档
   - 包含示例代码片段
2. **安装指令**：
   - 需支持变量替换（如${API_KEY}）
   - 支持条件逻辑
   - 提供多平台版本
3. **质量指标**：
   - 应包含数据来源和时间戳
   - 支持历史记录查询
   - 可视化展示

## 6. 关键业务规则

### 服务器提交验证
```python
def validate_submission(data):
    required_fields = ['name', 'author', 'description', 'license_type']
    if not all(field in data for field in required_fields):
        raise ValidationError("Missing required fields")
    
    if data['license_type'] not in APPROVED_LICENSES:
        raise ValidationError("Unapproved license")
    
    if len(data['tools']) == 0:
        raise ValidationError("At least one tool is required")
    
    for tool in data['tools']:
        if not validate_tool_schema(tool['schema']):
            raise ValidationError(f"Invalid schema for tool {tool['name']}")
```

### 搜索排序算法
```
权重 = 
   (关键词匹配度 * 0.4) +
   (下载量标准化值 * 0.3) + 
   (质量评分 * 0.2) +
   (更新新鲜度 * 0.1) -
   (负面反馈惩罚 * 0.2)
   
其中：
- 关键词匹配度 = TF-IDF + 语义相似度
- 下载量标准化值 = log(下载量+1)
- 质量评分 = (安全评分 + 许可证评分 + 代码质量评分)/3
- 更新新鲜度 = 1/(当前时间-最后更新时间)
- 负面反馈惩罚 = min(负面反馈数/总反馈数, 0.5)
```

## 7. 社交领域特别考量

1. **信任体系**：
   - 实现开发者认证徽章系统
     - 邮箱验证
     - 社交账号绑定
     - 代码贡献证明
   - 用户反馈影响搜索排名
     - 加权评分机制
     - 反作弊检测
   - 透明展示评分计算方式
     - 可视化评分构成
     - 提供申诉渠道

2. **社区驱动**：
   - 设置服务器维护者角色
     - 多级权限控制
     - 责任划分
   - 问题讨论区与GitHub Issues同步
     - 双向同步机制
     - 状态跟踪
   - 贡献者排行榜机制
     - 多维度的贡献评估
     - 定期奖励

3. **内容传播**：
   - 支持服务器分享到开发者社区
     - 多渠道分享按钮
     - 定制化分享内容
   - 集成Reddit/Discord讨论
     - 实时消息推送
     - 讨论内容聚合
   - 热门服务器自动推荐
     - 个性化推荐算法
     - 场景化推荐

4. **知识共享**：
   - 建立最佳实践库
   - 开发教程和案例
   - 举办线上研讨会

5. **生态系统健康**：
   - 监控服务器活跃度
   - 识别并奖励优质贡献
   - 建立淘汰机制

## 8. 新增业务实体

### 8.1 版本管理实体
```mermaid
erDiagram
    SERVER ||--o{ VERSION : has
    VERSION {
        string versionId PK
        string versionNumber
        string releaseNotes
        datetime releaseDate
        string compatibility
        string downloadUrl
    }
```

### 8.2 书签实体
```mermaid
erDiagram
    USER ||--o{ BOOKMARK : creates
    BOOKMARK {
        string bookmarkId PK
        string userId FK
        string serverId FK
        string tags
        string notes
        datetime createdAt
    }
```

### 8.3 安装记录实体
```mermaid
erDiagram
    SERVER ||--o{ INSTALLATION : has
    INSTALLATION {
        string installationId PK
        string serverId FK
        string userId FK
        string clientType
        datetime installedAt
        string status
    }
```

## 9. 新增功能需求

### 9.1 服务器版本管理
- 支持语义化版本控制
- 提供版本差异比较
- 实现版本回滚机制
- 展示版本历史图表

### 9.2 个性化推荐
- 基于用户行为的推荐
- 基于社交关系的推荐
- 基于技术栈的推荐
- 混合推荐策略

### 9.3 自动化测试
- 集成测试框架
- 持续集成流水线
- 测试覆盖率报告
- 性能基准测试

### 9.4 数据分析
- 用户行为分析
- 服务器流行度分析
- 质量趋势分析
- 自定义报表生成

## 10. 改进点总结

1. **完整性提升**：
   - 新增了版本管理、书签、安装记录等核心业务实体
   - 补充了自动化测试、数据分析等功能需求
   - 完善了技术风险评估和缓解措施

2. **准确性提高**：
   - 细化了搜索排序算法
   - 明确了数据模型关系
   - 规范了接口定义

3. **一致性增强**：
   - 统一了术语使用
   - 保持了风格一致
   - 确保了逻辑连贯

4. **细节完善**：
   - 补充了性能指标
   - 细化了安全要求
   - 明确了兼容性标准

5. **可操作性**：
   - 提供了具体的实现建议
   - 包含了示例代码
   - 给出了可衡量的标准# 领域建模分析报告

## 1. 概念分析与合并建议

### 1.1 相似概念识别

#### 概念组：[用户相关概念]
- **相似术语**: 用户, 开发者, 最终用户
- **合并建议**:
  - **方案1**: User - 统一用户概念，通过角色区分不同类型用户
    - 优点: 简化模型，减少重复属性
    - 缺点: 可能需要处理不同角色的特殊属性
  - **方案2**: 分离为User和Developer两个实体
    - 优点: 可以更精确地表达不同角色的差异
    - 缺点: 增加模型复杂度
- **推荐方案**: 方案1
- **最终概念名称**: User

#### 概念组：[认证相关概念]
- **相似术语**: auth, oauth_provider
- **合并建议**:
  - **方案1**: Authentication - 统一认证上下文
    - 优点: 集中管理所有认证逻辑
    - 缺点: 可能需要处理多种认证方式的差异
- **推荐方案**: 方案1
- **最终概念名称**: Authentication

### 1.2 建模决策

- **决策**: 合并用户相关概念为单一User实体
  - **理由**: 不同用户类型共享大部分属性和行为，角色差异可通过角色属性区分
  - **影响**: 需要设计灵活的角色系统来支持不同类型的用户

## 2. 边界上下文

### 上下文：用户管理上下文
- **描述**: 负责用户账户的创建、管理和基本信息维护
- **职责**: 用户注册、用户信息管理、用户状态管理
- **关系**:
  - 与 Authentication上下文 的关系: Customer-Supplier - 用户管理上下文依赖认证上下文进行身份验证

### 上下文：认证上下文
- **描述**: 负责用户认证和授权相关功能
- **职责**: 登录认证、OAuth集成、权限验证
- **关系**:
  - 与 用户管理上下文 的关系: Supplier-Customer - 为其他上下文提供认证服务

## 3. 聚合设计

### 聚合：用户聚合
- **所属上下文**: 用户管理上下文
- **聚合根**: User
- **包含实体**: UserProfile
- **包含值对象**: Email, PhoneNumber
- **业务规则**:
  - 用户注册时必须提供有效邮箱
  - 用户状态变更需记录变更历史
- **不变量**:
  - 用户ID不可变
  - 用户邮箱必须唯一

## 4. 领域实体

### 实体：User
- **所属聚合**: 用户聚合
- **描述**: 系统用户，可以是开发者或最终用户
- **属性**:
  - **id** (UUID, 必需): 用户唯一标识
  - **username** (str, 必需): 用户名
  - **status** (UserStatus, 必需): 用户状态(活跃/禁用/删除)
  - **roles** (List[Role], 必需): 用户角色列表
- **业务方法**:
  - **change_status**(new_status: UserStatus) -> None: 变更用户状态
  - **add_role**(role: Role) -> None: 添加用户角色
- **业务规则**:
  - 用户名必须唯一
  - 已删除用户不能恢复

## 5. 值对象

### 值对象：Email
- **描述**: 用户邮箱地址
- **属性**:
  - **value** (str): 邮箱地址字符串
- **验证规则**:
  - 必须符合邮箱格式
  - 长度不超过255个字符
- **不可变**: 是

### 值对象：PhoneNumber
- **描述**: 用户电话号码
- **属性**:
  - **value** (str): 电话号码字符串
- **验证规则**:
  - 必须符合电话号码格式
  - 长度不超过20个字符
- **不可变**: 是

## 6. 领域服务

### 服务：UserRegistrationService
- **所属上下文**: 用户管理上下文
- **描述**: 处理用户注册流程
- **方法**:
  - **register_user**(username: str, email: Email, password: str) -> User: 创建新用户
- **依赖**: UserRepository, PasswordHasher

### 服务：AuthenticationService
- **所属上下文**: 认证上下文
- **描述**: 处理用户认证
- **方法**:
  - **authenticate**(username: str, password: str) -> AuthenticationResult: 验证用户凭据
- **依赖**: UserRepository, TokenGenerator

## 7. 仓储接口

### 仓储：UserRepository
- **管理的聚合**: 用户聚合
- **描述**: 用户数据访问接口
- **方法**:
  - **get_by_id**(user_id: UUID) -> Optional[User]: 根据ID获取用户
  - **get_by_username**(username: str) -> Optional[User]: 根据用户名获取用户
  - **add**(user: User) -> None: 添加新用户
  - **update**(user: User) -> None: 更新用户信息

## 8. 领域事件

### 事件：UserRegisteredEvent
- **描述**: 用户注册成功事件
- **触发条件**:
  - 新用户注册成功
- **事件数据**:
  - **user_id** (UUID): 注册用户ID
  - **username** (str): 注册用户名
  - **timestamp** (datetime): 注册时间
- **处理器**: 发送欢迎邮件处理器, 初始化用户数据处理器

### 事件：UserStatusChangedEvent
- **描述**: 用户状态变更事件
- **触发条件**:
  - 用户状态发生变更
- **事件数据**:
  - **user_id** (UUID): 用户ID
  - **old_status** (UserStatus): 原状态
  - **new_status** (UserStatus): 新状态
- **处理器**: 用户状态同步处理器, 通知服务处理器由于没有提供具体的领域模型数据，我将基于DDD架构和给定的技术栈(FastAPI+SQLAlchemy)为main_module模块生成一个通用的技术开发需求模板。您可以根据实际业务需求进行补充和调整。

```markdown
## 1. 模块结构设计

### 模块名称: main_module
- **边界上下文**: 核心业务上下文
- **模块职责**: 处理系统核心业务逻辑和流程

#### 分层架构
- **接口层 (interfaces/)**
  - 职责: 提供REST API接口和Web界面
  - 组件: 
    - `api.py` - FastAPI路由定义
    - `schemas.py` - Pydantic模型定义
    - `dependencies.py` - API依赖项

- **应用层 (application/)**
  - 职责: 协调领域对象完成用例
  - 组件:
    - `services.py` - 应用服务
    - `commands.py` - CQRS命令
    - `queries.py` - CQRS查询

- **领域层 (domain/)**
  - 职责: 封装核心业务逻辑
  - 组件:
    - `models.py` - 领域模型
    - `repositories.py` - 仓储接口
    - `domain_services.py` - 领域服务
    - `events.py` - 领域事件

- **基础设施层 (infrastructure/)**
  - 职责: 提供技术实现支持
  - 组件:
    - `database.py` - SQLAlchemy模型和会话
    - `repositories_impl.py` - 仓储实现
    - `migrations/` - Alembic迁移脚本

#### 模块依赖
- 依赖auth模块进行认证
- 依赖user模块获取用户信息
- 依赖oauth_provider进行第三方认证

## 2. API设计规范

### API端点: `POST /api/main/entities`
- **描述**: 创建新的核心业务实体
- **所属模块**: main_module
- **认证要求**: JWT Token
- **授权要求**: create:entities权限

#### 请求参数
```json
{
  "name": "string - 实体名称",
  "description": "string - 实体描述",
  "status": "string - 状态(ACTIVE/INACTIVE)"
}
```

#### 响应格式 (201 Created)
```json
{
  "id": "UUID - 实体ID",
  "name": "string - 实体名称",
  "status": "string - 状态",
  "created_at": "datetime - 创建时间"
}
```

### API端点: `GET /api/main/entities/{entity_id}`
- **描述**: 获取实体详情
- **认证要求**: JWT Token
- **授权要求**: read:entities权限

## 3. 数据模型设计

### MainEntity (表名: main_entities)
- **所属模块**: main_module
- **描述**: 核心业务实体

#### 字段定义
| 字段名 | 类型 | 描述 | 约束 | 可空 | 默认值 |
|--------|------|------|------|------|--------|
| id | UUID | 实体ID | PRIMARY KEY | NO | gen_random_uuid() |
| name | String(100) | 实体名称 | UNIQUE | NO | - |
| description | Text | 详细描述 | - | YES | NULL |
| status | String(20) | 状态 | - | NO | 'ACTIVE' |
| created_at | DateTime | 创建时间 | - | NO | now() |
| updated_at | DateTime | 更新时间 | - | NO | now() |
| created_by | UUID | 创建人 | FOREIGN KEY | NO | - |

#### 关系定义
- **多对一**: 与user模块的User模型(created_by字段)

#### 索引设计
- **idx_main_entity_name**: (name) - 唯一索引
- **idx_main_entity_status**: (status) - 普通索引

## 4. 业务逻辑实现

### MainEntityService
- **所属模块**: main_module
- **描述**: 处理核心业务实体的创建和管理
- **依赖服务**: UserService, AuthService

#### 用例: 创建新实体
**描述**: 创建新的业务实体并验证权限

**实现步骤**:
1. 验证用户权限(调用AuthService)
2. 验证实体名称唯一性
3. 创建实体记录
4. 发布EntityCreated事件
5. 返回创建结果

**前置条件**:
- 用户已认证
- 用户有create:entities权限

**后置条件**:
- 实体记录已持久化
- EntityCreated事件已发布

**错误处理**:
- **PermissionDenied**: 返回403状态码
- **DuplicateEntityName**: 返回400状态码
- **DatabaseError**: 返回500状态码

## 5. 集成需求

### 集成类型: 用户认证集成
- **描述**: 与auth模块集成进行用户认证
- **外部系统**: auth模块
- **协议**: HTTP
- **数据格式**: JSON
- **认证方式**: JWT Token
- **错误处理**: 重试3次后记录错误
- **监控要求**: 请求延迟、错误率

### 集成类型: 事件发布
- **描述**: 使用消息队列发布领域事件
- **技术方案**: Redis Streams
- **事件类型**: EntityCreated, EntityUpdated
- **序列化格式**: JSON

## 6. 技术约束

### FastAPI约束
- **描述**: 必须使用Pydantic v2进行请求验证
- **影响范围**: 所有API接口
- **缓解措施**: 统一使用BaseModel派生所有请求模型

### 数据库约束
- **描述**: 必须使用Alembic进行数据库迁移
- **影响范围**: 所有模型变更
- **缓解措施**: 每次模型变更需生成迁移脚本

## 7. 性能要求

### API响应时间
- **目标值**: <500ms (p99)
- **测量方法**: Prometheus监控
- **优化策略**: 
  - 数据库查询优化
  - 热点数据缓存
  - 异步事件处理

### 测试策略
- **单元测试**: 使用pytest测试领域模型和服务
- **集成测试**: 测试数据库集成和外部服务调用
- **API测试**: 使用TestClient测试所有API端点
- **覆盖率目标**: >80%代码覆盖率
```

这个模板提供了main_module模块的完整技术需求框架，您可以根据实际业务需求:
1. 补充具体的领域模型和业务规则
2. 调整API端点和数据模型
3. 细化业务逻辑实现步骤
4. 添加更多的集成需求

建议先定义核心领域模型后再细化各部分的实现细节。

## 开发约束
1. **架构要求**: 严格遵循 DDD 四层架构 (interfaces/application/domain/infrastructure)
2. **代码规范**: 遵循 PEP 8 规范，使用类型提示
3. **技术栈**: FastAPI + SQLAlchemy + Pydantic + Alembic + Pytest
4. **数据库**: 所有实体ID使用UUID，字段名只反映业务含义
5. **测试**: 必须包含单元测试、集成测试和API测试
6. **文档**: 所有注释和文档使用英文

## 实现要求
1. 按照 DDD 分层架构组织代码
2. 实现完整的 CRUD 操作和业务逻辑
3. 提供完整的 API 文档和测试用例
4. 确保代码质量和测试覆盖率
5. 遵循项目现有的代码风格和约定

## 输出格式
请按照以下顺序实现：
1. Domain 层 (实体、值对象、仓库接口)
2. Infrastructure 层 (ORM模型、仓库实现)
3. Application 层 (应用服务、用例)
4. Interfaces 层 (API路由、Schema)
5. 测试代码 (单元测试、集成测试、API测试)

每个文件都要包含完整的实现和必要的注释。
