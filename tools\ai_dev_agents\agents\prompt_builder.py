"""
Prompt Builder Agent

Intelligent agent for building comprehensive AI development prompts.
"""

from typing import Any, Dict, List
from datetime import datetime
from pathlib import Path
import yaml

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class PromptBuilderAgent(BaseAgent):
    """Agent for building comprehensive AI development prompts."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None, log_dir=None):
        super().__init__(
            name="prompt_builder",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer,
            log_dir=log_dir
        )

    def _load_and_compress_rules(self, project_root: str) -> str:
        """Load and compress project rules for inclusion in prompts."""
        try:
            rules_path = Path(project_root) / ".roo" / "rules" / "rules.md"
            if not rules_path.exists():
                return "项目规则文件未找到，请参考标准 DDD + FastAPI 架构原则。"

            with open(rules_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract key sections and compress
            lines = content.split('\n')
            compressed_sections = []
            current_section = ""
            in_important_section = False

            for line in lines:
                # Keep important sections
                if any(keyword in line.lower() for keyword in [
                    '## 2. 架构设计与项目结构', '### 2.1 项目整体结构',
                    '### 2.2 模块内部分层架构', '### 2.3 核心依赖规则',
                    '## 3. 各层详细实现规范', '### 3.1', '### 3.2', '### 3.3', '### 3.4',
                    '## 4. 代码生成约束与流程'
                ]):
                    in_important_section = True
                    if current_section:
                        compressed_sections.append(current_section.strip())
                    current_section = line + '\n'
                elif line.startswith('## ') and '测试' not in line and '工程实践' not in line:
                    in_important_section = False
                    if current_section:
                        compressed_sections.append(current_section.strip())
                    current_section = ""
                elif in_important_section:
                    # Skip code blocks and mermaid diagrams for compression
                    if not line.strip().startswith('```') and 'mermaid' not in line:
                        current_section += line + '\n'

            if current_section:
                compressed_sections.append(current_section.strip())

            return '\n\n'.join(compressed_sections)

        except Exception as e:
            self.logger.warning(f"Failed to load project rules: {e}")
            return "项目规则加载失败，请参考标准 DDD + FastAPI 架构原则。"
    
    def get_system_prompt(self, context: WorkflowContext) -> str:
        """Get the system prompt for prompt building."""
        try:
            prompts_path = Path(__file__).parent.parent / "prompts" / "prompt_building.yaml"
            with open(prompts_path, 'r', encoding='utf-8') as f:
                prompts = yaml.safe_load(f)

            # Load project rules
            project_rules = self._load_and_compress_rules(context.project_root)

            # Use the complete system prompt template if available
            if 'complete_system_prompt_template' in prompts:
                template = prompts['complete_system_prompt_template']
                return template.format(project_rules=project_rules)
            else:
                # Fallback to the regular system prompt
                return prompts.get('system_prompt', self._get_fallback_system_prompt())

        except Exception as e:
            self.logger.warning(f"Failed to load system prompt from YAML: {e}")
            return self._get_fallback_system_prompt()

    def _get_fallback_system_prompt(self) -> str:
        """Fallback system prompt if external file is not available."""
        return """**重要：请务必使用中文回答所有问题，所有输出内容必须是中文。**

你是一个AI提示词工程专家，专门构建高质量的中文代码生成提示词。你的任务是基于技术需求和项目上下文，创建包含完整信息的AI开发提示词。

请基于以下技术需求构建AI开发提示词：
"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process requirements generation results and validate prompt files."""
        try:
            # Get requirements generation results
            requirements_result = input_data
            if not requirements_result:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No requirements generation results provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )

            start_time = datetime.now()

            # Get generated files from requirements generator
            generated_files = requirements_result.get("generated_files", [])
            output_directory = requirements_result.get("output_directory", "")
            modules = requirements_result.get("modules", [])

            if not generated_files:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No files were generated by requirements generator"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )

            # Validate and organize files
            prompt_files = []
            requirement_files = []

            for file_path in generated_files:
                file_name = Path(file_path).name
                if file_name.startswith("prompt_"):
                    prompt_files.append(file_path)
                elif file_name.endswith("_requirements.md"):
                    requirement_files.append(file_path)

            # Create summary file
            summary_content = self._create_summary_file(
                modules, prompt_files, requirement_files, context
            )

            summary_path = Path(output_directory) / "00_workflow_summary.md"
            summary_path.write_text(summary_content, encoding='utf-8')

            execution_time = (datetime.now() - start_time).total_seconds()

            return AgentResult(
                success=True,
                data={
                    "modules": modules,
                    "prompt_files": prompt_files,
                    "requirement_files": requirement_files,
                    "summary_file": str(summary_path),
                    "output_directory": output_directory,
                    "total_files": len(generated_files) + 1  # +1 for summary
                },
                metadata={
                    "agent_name": self.name,
                    "modules_processed": len(modules),
                    "output_format": "markdown"
                },
                errors=[],
                execution_time=execution_time,
                timestamp=datetime.now()
            )

        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Prompt building failed: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )

    def _create_summary_file(self, modules: List[str], prompt_files: List[str],
                           requirement_files: List[str], context: WorkflowContext) -> str:
        """Create a summary file for the generated requirements and prompts."""

        summary_content = f"""# AI 开发工作流程总结

## 生成概览
- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **项目根目录**: {context.project_root}
- **架构风格**: {context.architecture_style}
- **技术栈**: {', '.join(context.tech_stack)}
- **现有模块**: {', '.join(context.existing_modules)}

## 识别的业务模块
总共识别出 **{len(modules)}** 个业务模块：

"""

        for i, module in enumerate(modules, 1):
            summary_content += f"{i}. **{module}** - 对应的开发需求和提示词已生成\n"

        summary_content += f"""

## 生成的文件

### 开发需求文档
"""

        for req_file in requirement_files:
            file_name = Path(req_file).name
            summary_content += f"- `{file_name}` - 详细的技术开发需求\n"

        summary_content += f"""

### AI 开发提示词
"""

        for prompt_file in prompt_files:
            file_name = Path(prompt_file).name
            summary_content += f"- `{file_name}` - 对应的 AI 开发提示词\n"

        summary_content += f"""

## 使用指南

### 1. 开发需求文档
每个需求文档包含：
- 模块结构设计
- API 设计规范
- 数据模型设计
- 业务逻辑实现
- 集成需求
- 技术约束
- 性能要求

### 2. AI 开发提示词
每个提示词文件包含：
- 任务概述
- 详细的开发需求
- 开发约束和技术栈要求
- 实现要求和输出格式
- 项目规则和架构约束

### 3. 推荐的开发流程
1. 阅读对应的开发需求文档，理解业务需求
2. 使用对应的 AI 开发提示词进行代码生成
3. 按照 DDD 四层架构组织代码
4. 实现完整的测试用例
5. 运行 `inv ci` 检查代码质量
6. 提交代码并创建 PR

### 4. 注意事项
- 严格遵循项目的 DDD 架构规范
- 所有实体 ID 使用 UUID 类型
- 数据库字段名只反映业务含义
- API 设计遵循 RESTful 原则
- 测试覆盖率要求达到 80% 以上
- 所有代码必须通过 CI 检查

## 下一步行动
1. 选择一个模块开始开发（建议从依赖最少的模块开始）
2. 使用对应的提示词文件指导 AI 代码生成
3. 逐步完成所有模块的开发
4. 进行集成测试和系统测试
"""

        return summary_content
    
    def _format_requirements(self, requirements: Dict[str, Any]) -> str:
        """Format technical requirements for LLM input."""
        formatted = []
        
        # Module overview
        if "module_overview" in requirements:
            overview = requirements["module_overview"]
            formatted.append("## 模块概览")
            formatted.append(f"模块名: {overview.get('module_name', 'Unknown')}")
            formatted.append(f"职责: {overview.get('primary_responsibility', 'Unknown')}")
            formatted.append(f"业务价值: {overview.get('business_value', 'Unknown')}")
            formatted.append("")
        
        # User stories
        if "user_stories" in requirements:
            formatted.append("## 用户故事")
            for story in requirements["user_stories"][:5]:  # Limit to first 5
                formatted.append(f"- {story.get('id', 'Unknown')}: {story.get('title', 'Unknown')}")
                formatted.append(f"  {story.get('story', '')}")
            formatted.append("")
        
        # API design
        if "api_design" in requirements:
            api_design = requirements["api_design"]
            formatted.append("## API设计")
            formatted.append(f"基础路径: {api_design.get('base_path', '/api/v1')}")
            endpoints = api_design.get("endpoints", [])
            formatted.append(f"端点数量: {len(endpoints)}")
            for endpoint in endpoints[:3]:  # Limit to first 3
                formatted.append(f"- {endpoint.get('method', 'GET')} {endpoint.get('path', 'unknown')}")
            formatted.append("")
        
        # Data models
        if "data_models" in requirements:
            tables = requirements["data_models"].get("tables", [])
            formatted.append(f"## 数据模型")
            formatted.append(f"数据表数量: {len(tables)}")
            for table in tables[:3]:  # Limit to first 3
                formatted.append(f"- {table.get('name', 'unknown')}: {table.get('description', '')}")
            formatted.append("")
        
        return "\n".join(formatted)
    
    def _format_business_context(self, analysis: Dict[str, Any]) -> str:
        """Format business analysis for context."""
        if not analysis:
            return "无业务分析数据"
        
        formatted = []
        
        if "business_overview" in analysis:
            overview = analysis["business_overview"]
            formatted.append(f"项目: {overview.get('project_name', 'Unknown')}")
            formatted.append(f"目标: {overview.get('core_purpose', 'Unknown')}")
        
        if "core_entities" in analysis:
            entities = analysis["core_entities"]
            formatted.append(f"核心实体: {', '.join([e.get('name', 'unknown') for e in entities[:5]])}")
        
        return "\n".join(formatted)
    
    def _format_domain_context(self, model: Dict[str, Any]) -> str:
        """Format domain model for context."""
        if not model:
            return "无领域模型数据"
        
        formatted = []
        
        if "bounded_contexts" in model:
            contexts = model["bounded_contexts"]
            formatted.append(f"边界上下文: {', '.join([c.get('name', 'unknown') for c in contexts])}")
        
        if "aggregates" in model:
            aggregates = model["aggregates"]
            formatted.append(f"聚合: {', '.join([a.get('name', 'unknown') for a in aggregates])}")
        
        return "\n".join(formatted)
    
    def _process_prompt_content(self, response: str, requirements: Dict[str, Any], context: WorkflowContext) -> str:
        """Process and enhance the prompt content."""
        # Extract markdown content from response
        content = response
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        content = content.replace("{timestamp}", timestamp)
        
        # Add module-specific information
        module_name = requirements.get("module_overview", {}).get("module_name", "unknown")
        content = content.replace("{module_name}", module_name)
        
        # Add project context
        content = content.replace("{architecture_style}", context.architecture_style)
        content = content.replace("{tech_stack}", ", ".join(context.tech_stack))
        content = content.replace("{existing_modules}", ", ".join(context.existing_modules))
        
        return content
    
    def _extract_prompt_sections(self, content: str) -> List[str]:
        """Extract section names from prompt content."""
        sections = []
        lines = content.split('\n')
        
        for line in lines:
            if line.startswith('## '):
                section_name = line[3:].strip()
                sections.append(section_name)
        
        return sections
    
    def _assess_requirements_complexity(self, requirements: Dict[str, Any]) -> str:
        """Assess the complexity of requirements."""
        complexity_score = 0
        
        # Count various elements
        user_stories = len(requirements.get("user_stories", []))
        api_endpoints = len(requirements.get("api_design", {}).get("endpoints", []))
        data_tables = len(requirements.get("data_models", {}).get("tables", []))
        domain_entities = len(requirements.get("business_logic", {}).get("domain_entities", []))
        
        complexity_score = user_stories + api_endpoints * 2 + data_tables * 2 + domain_entities * 3
        
        if complexity_score < 20:
            return "Low"
        elif complexity_score < 50:
            return "Medium"
        else:
            return "High"
    
    def _assess_context_richness(self, context: WorkflowContext) -> str:
        """Assess the richness of project context."""
        richness_score = 0
        
        # Check context completeness
        if context.project_rules:
            richness_score += 3
        if context.existing_modules:
            richness_score += len(context.existing_modules)
        if context.tech_stack:
            richness_score += len(context.tech_stack)
        if context.additional_context:
            richness_score += len(context.additional_context) * 2
        
        if richness_score < 10:
            return "Basic"
        elif richness_score < 20:
            return "Good"
        else:
            return "Rich"

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with streaming support."""
        return self._execute_llm_call_with_streaming(messages, "AI开发提示词生成")

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return """
# AI Development Prompt for MCP Server Market Platform

## Project Context
This is a FastAPI-based marketplace platform for Model Context Protocol (MCP) servers, following Domain-Driven Design (DDD) principles.

## Technical Requirements
- Use FastAPI for web framework
- Implement DDD architecture with clear layer separation
- Use SQLAlchemy for ORM
- Include comprehensive type hints
- Follow PEP 8 coding standards

## Implementation Tasks
1. Create domain entities and value objects
2. Implement repository interfaces and implementations
3. Build application services
4. Create API endpoints with proper validation
5. Add comprehensive tests

Please implement the specified module following these guidelines.
"""
