"""
Enhanced Business Analyzer Agent

Intelligent agent for analyzing PRD documents and extracting business requirements
with advanced prompt engineering, error recovery, and quality assurance.
"""

import re
import json
import yaml
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


@dataclass
class AnalysisQualityMetrics:
    """Quality metrics for business analysis."""
    completeness_score: float  # 0-1, how complete the analysis is
    consistency_score: float   # 0-1, how consistent the extracted data is
    clarity_score: float       # 0-1, how clear and well-structured the output is
    coverage_score: float      # 0-1, how well the analysis covers the input
    confidence_score: float    # 0-1, overall confidence in the analysis

    @property
    def overall_quality(self) -> float:
        """Calculate overall quality score."""
        return (self.completeness_score + self.consistency_score +
                self.clarity_score + self.coverage_score + self.confidence_score) / 5


@dataclass
class AnalysisContext:
    """Context information for business analysis."""
    document_type: str = "PRD"
    domain: Optional[str] = None
    complexity_level: str = "medium"  # low, medium, high
    target_audience: str = "technical"  # technical, business, mixed
    analysis_depth: str = "comprehensive"  # basic, standard, comprehensive
    focus_areas: List[str] = None

    def __post_init__(self):
        if self.focus_areas is None:
            self.focus_areas = []


class BusinessAnalyzerAgent(BaseAgent):
    """
    Enhanced agent for analyzing business requirements from PRD documents.

    Features:
    - Adaptive prompt engineering based on document characteristics
    - Multi-pass analysis for quality assurance
    - Intelligent error recovery and self-correction
    - Quality metrics and confidence scoring
    - Context-aware analysis depth adjustment
    """

    def __init__(self, llm=None, verbose: bool = False, quality_threshold: float = 0.7, stream_displayer=None, log_dir=None):
        super().__init__(
            name="business_analyzer",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer,
            log_dir=log_dir
        )
        self.quality_threshold = quality_threshold
        self.prompts = self._load_prompts()
        self.analysis_templates = self._load_analysis_templates()
        self.validation_rules = self._load_validation_rules()

    def _load_prompts(self) -> Dict[str, str]:
        """Load prompts from external YAML file."""
        try:
            prompts_path = Path(__file__).parent.parent / "prompts" / "business_analysis.yaml"
            with open(prompts_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"Failed to load external prompts: {e}")
            raise Exception(f"Cannot load prompts from {prompts_path}: {e}")



    def _load_analysis_templates(self) -> Dict[str, str]:
        """Load different analysis templates for various scenarios."""
        return {
            "comprehensive": self._get_comprehensive_template(),
            "focused": self._get_focused_template(),
            "technical": self._get_technical_template(),
            "business": self._get_business_template(),
            "validation": self._get_validation_template()
        }

    def _load_validation_rules(self) -> Dict[str, Any]:
        """Load validation rules for quality assurance."""
        return {
            "required_sections": [
                "business_overview", "core_entities", "business_rules",
                "functional_requirements", "non_functional_requirements", "user_stories"
            ],
            "min_entities": 2,
            "min_requirements": 3,
            "min_user_stories": 2,
            "quality_checks": [
                "completeness", "consistency", "clarity", "coverage"
            ]
        }
    
    def _get_comprehensive_template(self) -> str:
        """Get comprehensive analysis template."""
        if "comprehensive_analysis" not in self.prompts:
            raise Exception("Missing 'comprehensive_analysis' template in prompts configuration")
        return self.prompts["comprehensive_analysis"]

    def _get_focused_template(self) -> str:
        """Get focused analysis template for specific areas."""
        if "focused_analysis" not in self.prompts:
            raise Exception("Missing 'focused_analysis' template in prompts configuration")
        return self.prompts["focused_analysis"]

    def _get_technical_template(self) -> str:
        """Get technical-focused analysis template."""
        if "technical_analysis" not in self.prompts:
            raise Exception("Missing 'technical_analysis' template in prompts configuration")
        return self.prompts["technical_analysis"]

    def _get_business_template(self) -> str:
        """Get business-focused analysis template."""
        if "business_focused" not in self.prompts:
            raise Exception("Missing 'business_focused' template in prompts configuration")
        return self.prompts["business_focused"]

    def _get_validation_template(self) -> str:
        """Get validation template for quality checking."""
        if "validation_analysis" not in self.prompts:
            raise Exception("Missing 'validation_analysis' template in prompts configuration")
        return self.prompts["validation_analysis"]

    def get_system_prompt(self, template_type: str = "comprehensive", context: Optional[AnalysisContext] = None) -> str:
        """Get adaptive system prompt based on context."""
        if template_type in self.analysis_templates:
            base_template = self.analysis_templates[template_type]

            if context and template_type == "focused" and context.focus_areas:
                focus_description = "\n".join([f"- {area}" for area in context.focus_areas])
                return base_template.format(focus_areas=focus_description)

            return base_template

        return self.analysis_templates["comprehensive"]
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """
        Enhanced process method with intelligent analysis and quality assurance.

        Features:
        - Adaptive analysis based on document characteristics
        - Multi-pass analysis for quality improvement
        - Intelligent error recovery
        - Quality metrics and confidence scoring
        """
        start_time = datetime.now()

        try:
            # Extract and validate input
            prd_content = input_data.get("prd_content", "")
            if not prd_content:
                return self._create_error_result("No PRD content provided", start_time)

            # Analyze document characteristics
            doc_analysis = self._analyze_document_characteristics(prd_content)
            analysis_context = self._create_analysis_context(input_data, doc_analysis)

            # Perform initial analysis
            initial_result = self._perform_initial_analysis(prd_content, analysis_context)

            if not initial_result.success:
                # Attempt error recovery
                recovery_result = self._attempt_error_recovery(prd_content, initial_result, analysis_context)
                if recovery_result.success:
                    initial_result = recovery_result
                else:
                    return initial_result

            # Quality assessment and improvement
            quality_metrics = self._assess_analysis_quality(initial_result.data, prd_content)

            if quality_metrics.overall_quality < self.quality_threshold:
                # Attempt quality improvement
                improved_result = self._improve_analysis_quality(
                    prd_content, initial_result.data, quality_metrics, analysis_context
                )
                if improved_result.success:
                    initial_result = improved_result
                    quality_metrics = self._assess_analysis_quality(initial_result.data, prd_content)

            # Enrich and finalize results
            enriched_data = self._enrich_business_analysis(initial_result.data, prd_content, quality_metrics)

            execution_time = (datetime.now() - start_time).total_seconds()

            return AgentResult(
                success=True,
                data=enriched_data,
                metadata={
                    "agent_name": self.name,
                    "analysis_context": analysis_context.__dict__,
                    "quality_metrics": quality_metrics.__dict__,
                    "document_analysis": doc_analysis,
                    "entities_count": len(enriched_data.get("core_entities", [])),
                    "requirements_count": len(enriched_data.get("functional_requirements", [])),
                    "user_stories_count": len(enriched_data.get("user_stories", [])),
                    "analysis_passes": getattr(initial_result, 'analysis_passes', 1)
                },
                execution_time=execution_time,
                timestamp=datetime.now()
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"Business analysis failed: {e}")

            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name, "error_type": type(e).__name__},
                errors=[f"Business analysis failed: {str(e)}"],
                execution_time=execution_time,
                timestamp=datetime.now()
            )

    def _analyze_document_characteristics(self, content: str) -> Dict[str, Any]:
        """Analyze document characteristics to adapt analysis approach."""
        return {
            "length": len(content),
            "complexity": self._estimate_complexity(content),
            "structure": self._analyze_structure(content),
            "domain_indicators": self._detect_domain_indicators(content),
            "language_style": self._analyze_language_style(content)
        }

    def _estimate_complexity(self, content: str) -> str:
        """Estimate document complexity based on various factors."""
        word_count = len(content.split())

        # Count technical terms, entities, and requirements
        technical_terms = len(re.findall(r'\b(?:API|database|system|service|interface|protocol)\b', content, re.IGNORECASE))
        entity_mentions = len(re.findall(r'\b(?:user|customer|product|order|payment|account)\b', content, re.IGNORECASE))
        requirement_indicators = len(re.findall(r'\b(?:must|should|shall|require|need)\b', content, re.IGNORECASE))

        complexity_score = (word_count / 100) + (technical_terms * 2) + (entity_mentions * 1.5) + (requirement_indicators * 1)

        if complexity_score < 20:
            return "low"
        elif complexity_score < 50:
            return "medium"
        else:
            return "high"

    def _analyze_structure(self, content: str) -> Dict[str, Any]:
        """Analyze document structure and organization."""
        lines = content.split('\n')

        return {
            "has_headers": bool(re.search(r'^#+\s', content, re.MULTILINE)),
            "has_lists": bool(re.search(r'^\s*[-*+]\s', content, re.MULTILINE)),
            "has_numbered_lists": bool(re.search(r'^\s*\d+\.\s', content, re.MULTILINE)),
            "paragraph_count": len([line for line in lines if line.strip() and not line.startswith('#')]),
            "section_count": len(re.findall(r'^#+\s', content, re.MULTILINE))
        }

    def _detect_domain_indicators(self, content: str) -> List[str]:
        """Detect domain-specific indicators in the content."""
        domain_patterns = {
            "e-commerce": r'\b(?:shop|cart|payment|order|product|customer|checkout)\b',
            "fintech": r'\b(?:bank|finance|transaction|account|credit|debit|payment)\b',
            "healthcare": r'\b(?:patient|doctor|medical|health|treatment|diagnosis)\b',
            "education": r'\b(?:student|teacher|course|lesson|grade|exam)\b',
            "social": r'\b(?:user|friend|post|comment|like|share|follow)\b',
            "enterprise": r'\b(?:employee|department|workflow|approval|admin)\b'
        }

        detected_domains = []
        for domain, pattern in domain_patterns.items():
            if len(re.findall(pattern, content, re.IGNORECASE)) >= 3:
                detected_domains.append(domain)

        return detected_domains

    def _analyze_language_style(self, content: str) -> str:
        """Analyze the language style of the document."""
        technical_ratio = len(re.findall(r'\b(?:API|SDK|JSON|HTTP|REST|database|algorithm)\b', content, re.IGNORECASE)) / max(len(content.split()), 1)
        business_ratio = len(re.findall(r'\b(?:customer|revenue|market|business|strategy|goal)\b', content, re.IGNORECASE)) / max(len(content.split()), 1)

        if technical_ratio > business_ratio * 1.5:
            return "technical"
        elif business_ratio > technical_ratio * 1.5:
            return "business"
        else:
            return "mixed"
    
    def _create_analysis_context(self, input_data: Dict[str, Any], doc_analysis: Dict[str, Any]) -> AnalysisContext:
        """Create analysis context based on input and document characteristics."""
        return AnalysisContext(
            document_type=input_data.get("document_type", "PRD"),
            domain=doc_analysis["domain_indicators"][0] if doc_analysis["domain_indicators"] else None,
            complexity_level=doc_analysis["complexity"],
            target_audience=doc_analysis["language_style"],
            analysis_depth=input_data.get("analysis_depth", "comprehensive"),
            focus_areas=input_data.get("focus_areas", [])
        )

    def _perform_initial_analysis(self, prd_content: str, analysis_context: AnalysisContext) -> AgentResult:
        """Perform initial business analysis with adaptive prompting."""
        try:
            # Select appropriate template based on context
            template_type = self._select_template_type(analysis_context)
            system_prompt = self.get_system_prompt(template_type, analysis_context)

            # Create adaptive user prompt
            user_prompt = self._create_adaptive_user_prompt(prd_content, analysis_context)

            # Execute LLM call
            messages = self._create_messages(system_prompt, user_prompt)
            response = self._execute_llm_call(messages)

            # Process markdown response
            processed_data = self._process_markdown_response(response)
            validation_errors = self._validate_markdown_content(processed_data)

            if validation_errors:
                return AgentResult(
                    success=False,
                    data=processed_data,
                    metadata={"validation_errors": validation_errors, "raw_response": response},
                    errors=validation_errors
                )

            return AgentResult(
                success=True,
                data=processed_data,
                metadata={"template_type": template_type, "raw_response": response, "analysis_passes": 1}
            )

        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"error_type": type(e).__name__},
                errors=[f"Initial analysis failed: {str(e)}"]
            )

    def _select_template_type(self, context: AnalysisContext) -> str:
        """Select appropriate template based on analysis context."""
        if context.focus_areas:
            return "focused"
        elif context.target_audience == "technical":
            return "technical"
        elif context.target_audience == "business":
            return "business"
        else:
            return "comprehensive"

    def _create_adaptive_user_prompt(self, prd_content: str, context: AnalysisContext) -> str:
        """Create adaptive user prompt based on context."""
        base_prompt = f"""
请分析以下{context.document_type}文档并提取业务需求信息：

=== 文档内容 ===
{prd_content}

=== 分析要求 ===
"""

        # Add context-specific instructions
        if context.complexity_level == "high":
            base_prompt += """
这是一个复杂的文档，请特别注意：
1. 深度分析复杂的业务逻辑和规则
2. 识别多层次的实体关系
3. 提取详细的功能需求和约束条件
"""

        if context.domain:
            base_prompt += f"""
文档属于{context.domain}领域，请结合该领域的特点进行分析。
"""

        if context.focus_areas:
            focus_list = "\n".join([f"- {area}" for area in context.focus_areas])
            base_prompt += f"""
请重点关注以下领域：
{focus_list}
"""

        base_prompt += """
请确保输出的JSON格式正确且信息完整。特别注意：
1. 深度理解业务语义，不要只是简单提取文字
2. 识别隐含的业务规则和约束
3. 考虑用户的真实使用场景和痛点
4. 确保提取的信息具有可操作性
"""

        return base_prompt

    def _attempt_error_recovery(self, prd_content: str, failed_result: AgentResult, context: AnalysisContext) -> AgentResult:
        """Attempt to recover from analysis errors using alternative approaches."""
        self.logger.info("Attempting error recovery for business analysis")

        try:
            # Try with simplified template
            simplified_prompt = self._get_simplified_recovery_prompt()
            user_prompt = f"""
之前的分析遇到了问题，现在使用简化的方法重新分析：

{prd_content}

请提供基本的业务分析结果，使用Markdown格式输出。
"""

            messages = self._create_messages(simplified_prompt, user_prompt)
            response = self._execute_llm_call(messages)

            processed_data = self._process_markdown_response(response)

            # Basic validation
            if isinstance(processed_data, dict) and processed_data.get("raw_markdown"):
                return AgentResult(
                    success=True,
                    data=processed_data,
                    metadata={"recovery_attempt": True, "raw_response": response, "analysis_passes": 2},
                    warnings=["Analysis recovered using simplified approach"]
                )

        except Exception as e:
            self.logger.error(f"Error recovery failed: {e}")

        return failed_result

    def _get_simplified_recovery_prompt(self) -> str:
        """Get simplified prompt for error recovery."""
        return """
你是一个业务分析师。请分析PRD文档并提供基本的业务信息。

请以Markdown格式输出，包含以下结构：

# 业务需求分析报告

## 1. 业务概览
- **项目名称**: [项目名称]
- **项目描述**: [项目的详细描述]
- **核心目标**: [主要目标列表]
- **目标用户**: [用户群体描述]
- **价值主张**: [项目带来的业务价值]

## 2. 核心实体分析
### 实体1: [实体名称]
- **描述**: [实体的业务含义和作用]
- **主要属性**: [关键属性列表]
- **业务规则**: [相关的业务约束和规则]
- **关系**: [与其他实体的关系]

## 3. 功能需求
### FR-001: [功能标题]
- **描述**: [功能的详细描述]
- **优先级**: [高/中/低]
- **验收标准**: [验收条件]

## 4. 非功能需求
### 性能要求
- [性能相关要求]

### 安全要求
- [安全相关要求]

## 5. 用户故事
### US-001: [用户故事标题]
- **故事**: 作为[用户角色]，我希望[功能描述]，以便[价值说明]
- **优先级**: [高/中/低]
}
```
"""

    def _enrich_business_analysis(self, data: Dict[str, Any], prd_content: str, quality_metrics: Optional[AnalysisQualityMetrics] = None) -> Dict[str, Any]:
        """Enrich the business analysis with additional insights and metadata."""
        enriched = data.copy()

        # Add document metadata
        enriched["document_metadata"] = {
            "content_length": len(prd_content),
            "analysis_timestamp": datetime.now().isoformat(),
            "complexity_score": self._calculate_complexity_score(data),
            "quality_metrics": quality_metrics.__dict__ if quality_metrics else None
        }

        # Add entity relationships map
        enriched["entity_relationships"] = self._extract_entity_relationships(data.get("core_entities", []))

        # Add priority matrix
        enriched["priority_matrix"] = self._create_priority_matrix(
            data.get("functional_requirements", []),
            data.get("user_stories", [])
        )

        # Add analysis insights
        enriched["analysis_insights"] = self._generate_analysis_insights(data, prd_content)

        # Add recommendations
        enriched["recommendations"] = self._generate_recommendations(data, quality_metrics)

        return enriched

    def _assess_analysis_quality(self, data: Dict[str, Any], prd_content: str) -> AnalysisQualityMetrics:
        """Assess the quality of the business analysis."""
        completeness = self._assess_completeness(data)
        consistency = self._assess_consistency(data)
        clarity = self._assess_clarity(data)
        coverage = self._assess_coverage(data, prd_content)
        confidence = self._assess_confidence(data)

        return AnalysisQualityMetrics(
            completeness_score=completeness,
            consistency_score=consistency,
            clarity_score=clarity,
            coverage_score=coverage,
            confidence_score=confidence
        )

    def _assess_completeness(self, data: Dict[str, Any]) -> float:
        """Assess completeness of the analysis."""
        required_sections = self.validation_rules["required_sections"]
        present_sections = sum(1 for section in required_sections if section in data and data[section])

        # Check minimum content requirements
        entities_ok = len(data.get("core_entities", [])) >= self.validation_rules["min_entities"]
        requirements_ok = len(data.get("functional_requirements", [])) >= self.validation_rules["min_requirements"]
        stories_ok = len(data.get("user_stories", [])) >= self.validation_rules["min_user_stories"]

        section_score = present_sections / len(required_sections)
        content_score = sum([entities_ok, requirements_ok, stories_ok]) / 3

        return (section_score + content_score) / 2

    def _assess_consistency(self, data: Dict[str, Any]) -> float:
        """Assess consistency across different sections."""
        consistency_score = 1.0

        # Check entity consistency
        entities = {entity.get("name", "") for entity in data.get("core_entities", [])}

        # Check if functional requirements reference valid entities
        for req in data.get("functional_requirements", []):
            description = req.get("description", "").lower()
            referenced_entities = sum(1 for entity in entities if entity.lower() in description)
            if referenced_entities == 0 and entities:
                consistency_score -= 0.1

        # Check user story consistency with requirements
        story_features = {story.get("story", "").lower() for story in data.get("user_stories", [])}
        req_features = {req.get("title", "").lower() for req in data.get("functional_requirements", [])}

        if story_features and req_features:
            overlap = len(story_features.intersection(req_features)) / max(len(story_features), len(req_features))
            consistency_score = min(consistency_score, overlap + 0.5)

        return max(0.0, min(1.0, consistency_score))

    def _assess_clarity(self, data: Dict[str, Any]) -> float:
        """Assess clarity and specificity of the analysis."""
        clarity_score = 1.0

        # Check for vague descriptions
        vague_terms = ["something", "various", "multiple", "some", "many", "several"]

        for section in ["core_entities", "functional_requirements", "user_stories"]:
            items = data.get(section, [])
            for item in items:
                description = str(item.get("description", "")).lower()
                vague_count = sum(1 for term in vague_terms if term in description)
                if vague_count > 0:
                    clarity_score -= 0.05

        return max(0.0, min(1.0, clarity_score))

    def _assess_coverage(self, data: Dict[str, Any], prd_content: str) -> float:
        """Assess how well the analysis covers the input content."""
        # Extract key terms from PRD
        prd_words = set(re.findall(r'\b\w+\b', prd_content.lower()))
        prd_words = {word for word in prd_words if len(word) > 3}  # Filter short words

        # Extract terms from analysis
        analysis_text = json.dumps(data).lower()
        analysis_words = set(re.findall(r'\b\w+\b', analysis_text))

        # Calculate coverage
        if prd_words:
            coverage = len(prd_words.intersection(analysis_words)) / len(prd_words)
            return min(1.0, coverage * 2)  # Scale up since we expect partial coverage

        return 0.5  # Default if no words found

    def _assess_confidence(self, data: Dict[str, Any]) -> float:
        """Assess confidence in the analysis based on various factors."""
        confidence = 0.8  # Base confidence

        # Boost confidence for detailed analysis
        total_items = (len(data.get("core_entities", [])) +
                      len(data.get("functional_requirements", [])) +
                      len(data.get("user_stories", [])))

        if total_items > 10:
            confidence += 0.1
        elif total_items < 5:
            confidence -= 0.2

        # Check for detailed descriptions
        detailed_count = 0
        for entity in data.get("core_entities", []):
            if len(entity.get("description", "")) > 50:
                detailed_count += 1

        if detailed_count > len(data.get("core_entities", [])) * 0.7:
            confidence += 0.1

        return max(0.0, min(1.0, confidence))

    def _improve_analysis_quality(self, prd_content: str, initial_data: Dict[str, Any],
                                quality_metrics: AnalysisQualityMetrics, context: AnalysisContext) -> AgentResult:
        """Attempt to improve analysis quality through targeted refinement."""
        try:
            # Identify specific areas for improvement
            improvement_areas = self._identify_improvement_areas(quality_metrics)

            # Create targeted improvement prompt
            improvement_prompt = self._create_improvement_prompt(initial_data, improvement_areas)

            user_prompt = f"""
基于之前的分析结果，请改进以下方面：

{improvement_areas}

原始分析结果：
{json.dumps(initial_data, ensure_ascii=False, indent=2)}

原始PRD内容：
{prd_content}

请提供改进后的完整分析结果。
"""

            messages = self._create_messages(improvement_prompt, user_prompt)
            response = self._execute_llm_call(messages)

            improved_data = self._process_markdown_response(response)

            return AgentResult(
                success=True,
                data=improved_data,
                metadata={"improvement_areas": improvement_areas, "raw_response": response, "analysis_passes": 2}
            )

        except Exception as e:
            self.logger.error(f"Quality improvement failed: {e}")
            return AgentResult(
                success=False,
                data=initial_data,
                errors=[f"Quality improvement failed: {str(e)}"]
            )

    def _identify_improvement_areas(self, quality_metrics: AnalysisQualityMetrics) -> List[str]:
        """Identify specific areas that need improvement."""
        areas = []

        if quality_metrics.completeness_score < 0.8:
            areas.append("增加缺失的业务实体和功能需求")

        if quality_metrics.consistency_score < 0.7:
            areas.append("确保各部分信息的逻辑一致性")

        if quality_metrics.clarity_score < 0.8:
            areas.append("提高描述的清晰度和具体性")

        if quality_metrics.coverage_score < 0.6:
            areas.append("更全面地覆盖原始文档内容")

        return areas

    def _create_improvement_prompt(self, initial_data: Dict[str, Any], improvement_areas: List[str]) -> str:
        """Create targeted prompt for quality improvement."""
        if "quality_improvement" not in self.prompts:
            raise Exception("Missing 'quality_improvement' template in prompts configuration")

        return self.prompts["quality_improvement"].format(
            improvement_areas=chr(10).join([f"- {area}" for area in improvement_areas]),
            initial_analysis=initial_data.get("raw_markdown", str(initial_data))
        )
    
    def _generate_analysis_insights(self, data: Dict[str, Any], prd_content: str) -> Dict[str, Any]:
        """Generate insights from the business analysis."""
        return {
            "domain_complexity": self._analyze_domain_complexity(data),
            "implementation_challenges": self._identify_implementation_challenges(data),
            "business_risks": self._identify_business_risks(data),
            "scalability_considerations": self._analyze_scalability_needs(data),
            "integration_points": self._identify_integration_points(data)
        }

    def _generate_recommendations(self, data: Dict[str, Any], quality_metrics: Optional[AnalysisQualityMetrics]) -> List[str]:
        """Generate recommendations based on the analysis."""
        recommendations = []

        # Quality-based recommendations
        if quality_metrics and quality_metrics.completeness_score < 0.8:
            recommendations.append("建议补充更多业务实体和功能需求的详细信息")

        # Content-based recommendations
        entities_count = len(data.get("core_entities", []))
        if entities_count > 10:
            recommendations.append("考虑将复杂的业务实体进行分组或分层管理")

        requirements_count = len(data.get("functional_requirements", []))
        if requirements_count > 20:
            recommendations.append("建议将功能需求按优先级和模块进行分阶段实现")

        # Check for missing non-functional requirements
        nfr_categories = {nfr.get("category", "") for nfr in data.get("non_functional_requirements", [])}
        missing_nfr = {"性能", "安全", "可用性", "扩展性"} - nfr_categories
        if missing_nfr:
            recommendations.append(f"建议补充以下非功能需求：{', '.join(missing_nfr)}")

        return recommendations

    def _analyze_domain_complexity(self, data: Dict[str, Any]) -> str:
        """Analyze the complexity of the business domain."""
        entities_count = len(data.get("core_entities", []))
        rules_count = len(data.get("business_rules", []))

        complexity_score = entities_count * 2 + rules_count * 3

        if complexity_score < 15:
            return "简单"
        elif complexity_score < 40:
            return "中等"
        else:
            return "复杂"

    def _identify_implementation_challenges(self, data: Dict[str, Any]) -> List[str]:
        """Identify potential implementation challenges."""
        challenges = []

        # Check for complex entity relationships
        entities = data.get("core_entities", [])
        for entity in entities:
            relationships = entity.get("relationships", [])
            if len(relationships) > 5:
                challenges.append(f"实体 {entity.get('name')} 具有复杂的关系网络")

        # Check for high-priority requirements
        high_priority_reqs = [req for req in data.get("functional_requirements", [])
                             if req.get("priority", "").lower() in ["高", "high"]]
        if len(high_priority_reqs) > 10:
            challenges.append("存在大量高优先级功能需求，需要合理安排开发顺序")

        return challenges

    def _identify_business_risks(self, data: Dict[str, Any]) -> List[str]:
        """Identify potential business risks."""
        risks = []

        # Check for missing user stories
        user_stories_count = len(data.get("user_stories", []))
        functional_reqs_count = len(data.get("functional_requirements", []))

        if user_stories_count < functional_reqs_count * 0.5:
            risks.append("用户故事覆盖不足，可能导致需求理解偏差")

        # Check for vague business rules
        business_rules = data.get("business_rules", [])
        if len(business_rules) < 3:
            risks.append("业务规则定义不足，可能导致实现不一致")

        return risks

    def _analyze_scalability_needs(self, data: Dict[str, Any]) -> Dict[str, str]:
        """Analyze scalability needs based on the business analysis."""
        return {
            "data_scalability": "需要考虑大量数据的存储和查询优化" if len(data.get("core_entities", [])) > 8 else "数据规模适中",
            "user_scalability": "需要考虑高并发用户访问" if len(data.get("user_stories", [])) > 15 else "用户规模适中",
            "feature_scalability": "需要模块化设计以支持功能扩展" if len(data.get("functional_requirements", [])) > 15 else "功能规模适中"
        }

    def _identify_integration_points(self, data: Dict[str, Any]) -> List[str]:
        """Identify potential integration points with external systems."""
        integration_points = []

        # Look for integration keywords in requirements
        integration_keywords = ["api", "接口", "集成", "第三方", "外部系统", "导入", "导出", "同步"]

        for req in data.get("functional_requirements", []):
            description = req.get("description", "").lower()
            for keyword in integration_keywords:
                if keyword in description:
                    integration_points.append(f"功能需求 {req.get('title')} 可能需要外部集成")
                    break

        return integration_points

    def _validate_analysis_result(self, data: Dict[str, Any]) -> List[str]:
        """Validate the analysis result and return any errors."""
        errors = []

        # For Markdown format, use the new validation method
        if data.get("content_type") == "markdown":
            return self._validate_markdown_content(data)

        # Legacy JSON validation (for backward compatibility)
        required_sections = self.validation_rules["required_sections"]
        for section in required_sections:
            if section not in data:
                errors.append(f"Missing required section: {section}")
            elif not data[section]:
                errors.append(f"Empty section: {section}")

        # Check minimum content requirements
        if len(data.get("core_entities", [])) < self.validation_rules["min_entities"]:
            errors.append(f"Insufficient core entities (minimum: {self.validation_rules['min_entities']})")

        if len(data.get("functional_requirements", [])) < self.validation_rules["min_requirements"]:
            errors.append(f"Insufficient functional requirements (minimum: {self.validation_rules['min_requirements']})")

        if len(data.get("user_stories", [])) < self.validation_rules["min_user_stories"]:
            errors.append(f"Insufficient user stories (minimum: {self.validation_rules['min_user_stories']})")

        return errors

    def _create_error_result(self, error_message: str, start_time: datetime) -> AgentResult:
        """Create a standardized error result."""
        execution_time = (datetime.now() - start_time).total_seconds()
        return AgentResult(
            success=False,
            data={},
            metadata={"agent_name": self.name},
            errors=[error_message],
            execution_time=execution_time,
            timestamp=datetime.now()
        )

    def _calculate_complexity_score(self, data: Dict[str, Any]) -> int:
        """Calculate complexity score based on analysis results."""
        score = 0

        # For Markdown format, calculate based on content length and sections
        if data.get("content_type") == "markdown":
            raw_markdown = data.get("raw_markdown", "")
            sections = data.get("sections", {})

            # Base score from content length
            score += min(len(raw_markdown) // 100, 30)  # Max 30 points from length

            # Score from number of sections
            score += len(sections) * 5  # 5 points per section

            # Bonus for comprehensive sections
            if "业务概览" in sections:
                score += 10
            if "核心实体分析" in sections:
                score += 15
            if "功能需求" in sections:
                score += 10
            if "用户故事" in sections:
                score += 5

            return min(score, 100)  # Cap at 100

        # Legacy JSON format calculation
        score += len(data.get("core_entities", [])) * 2
        score += len(data.get("functional_requirements", [])) * 1
        score += len(data.get("business_rules", [])) * 3
        score += len(data.get("user_stories", [])) * 1
        return min(score, 100)  # Cap at 100
    
    def _extract_entity_relationships(self, entities: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Extract relationships between entities."""
        relationships = {}
        for entity in entities:
            entity_name = entity.get("name", "")
            entity_relationships = entity.get("relationships", [])
            if entity_name:
                relationships[entity_name] = entity_relationships
        return relationships
    
    def _create_priority_matrix(self, requirements: List[Dict[str, Any]], user_stories: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Create priority matrix for requirements and user stories."""
        matrix = {"high": [], "medium": [], "low": []}
        
        # Process functional requirements
        for req in requirements:
            priority = req.get("priority", "medium").lower()
            if priority in matrix:
                matrix[priority].append(f"FR: {req.get('title', 'Unknown')}")
        
        # Process user stories
        for story in user_stories:
            priority = story.get("priority", "medium").lower()
            if priority in matrix:
                matrix[priority].append(f"US: {story.get('id', 'Unknown')}")
        
        return matrix

    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return '''
{
  "business_overview": {
    "project_name": "MCP Server Market Platform",
    "core_purpose": "Centralized marketplace for Model Context Protocol servers",
    "target_users": ["Developers", "Server Authors", "Platform Admins"],
    "business_value": "Enable efficient discovery and sharing of MCP servers",
    "key_success_metrics": ["Server adoption rate", "User engagement", "Platform growth"]
  },
  "core_entities": [
    {
      "name": "MCPServer",
      "description": "A Model Context Protocol server with metadata and functionality",
      "attributes": ["id", "name", "description", "version", "category", "author"],
      "relationships": ["belongs_to_author", "has_versions", "categorized_by"]
    },
    {
      "name": "User",
      "description": "Platform user who can discover or publish servers",
      "attributes": ["id", "username", "email", "profile", "role"],
      "relationships": ["owns_servers", "has_profile", "belongs_to_role"]
    },
    {
      "name": "Category",
      "description": "Classification for organizing servers",
      "attributes": ["id", "name", "description", "parent_category"],
      "relationships": ["contains_servers", "has_subcategories"]
    }
  ],
  "business_rules": [
    "Only authenticated users can submit servers",
    "Server metadata must be complete before publication",
    "Users can only modify their own servers",
    "Categories must be approved by administrators"
  ],
  "functional_requirements": [
    {
      "id": "FR-001",
      "title": "Server Discovery",
      "description": "Users can browse and search for MCP servers",
      "priority": "High",
      "acceptance_criteria": ["Search by keywords", "Filter by category", "View server details"]
    },
    {
      "id": "FR-002",
      "title": "Server Submission",
      "description": "Authors can submit new servers to the marketplace",
      "priority": "High",
      "acceptance_criteria": ["Upload server metadata", "Version management", "Publication workflow"]
    }
  ],
  "non_functional_requirements": [
    {
      "category": "Performance",
      "requirements": ["API response time < 200ms", "Support 1000+ concurrent users"]
    },
    {
      "category": "Security",
      "requirements": ["Secure authentication", "Data encryption", "Input validation"]
    }
  ],
  "user_stories": [
    {
      "id": "US-001",
      "title": "Discover MCP Servers",
      "story": "As a developer, I want to browse available MCP servers so that I can find tools that match my needs",
      "acceptance_criteria": [
        "Given I am on the marketplace homepage, when I browse servers, then I see a list of available servers"
      ],
      "priority": "High",
      "story_points": 5
    },
    {
      "id": "US-002",
      "title": "Submit New Server",
      "story": "As a server author, I want to submit my MCP server so that other developers can discover and use it",
      "acceptance_criteria": [
        "Given I am an authenticated user, when I submit server metadata, then my server is added to the marketplace"
      ],
      "priority": "High",
      "story_points": 8
    }
  ]
}
'''

    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with streaming support."""
        return self._execute_llm_call_with_streaming(messages, "业务需求分析")

    def _parse_json_response(self, response: str) -> Dict[str, Any]:
        """Parse JSON response from LLM, handling multiple JSON objects."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        import re
        import json

        # Try to find all JSON objects in code blocks
        json_matches = re.findall(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)

        if json_matches:
            # Parse all JSON objects and merge them
            merged_data = {}
            for json_text in json_matches:
                try:
                    parsed = json.loads(json_text)
                    if isinstance(parsed, dict):
                        merged_data.update(parsed)
                except json.JSONDecodeError:
                    continue

            if merged_data:
                return merged_data

        # Fallback: try to find all JSON objects in text
        json_objects = []
        pos = 0
        while True:
            brace_start = response.find('{', pos)
            if brace_start == -1:
                break

            brace_count = 0
            for i, char in enumerate(response[brace_start:], brace_start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        json_text = response[brace_start:i+1]
                        try:
                            parsed = json.loads(json_text)
                            if isinstance(parsed, dict):
                                json_objects.append(parsed)
                        except json.JSONDecodeError:
                            pass
                        pos = i + 1
                        break
            else:
                break

        # Merge all found JSON objects
        if json_objects:
            merged_data = {}
            for obj in json_objects:
                merged_data.update(obj)
            return merged_data

        # Last resort: try to parse the entire response as JSON
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Response content: {response[:500]}...")
            return {}

    def _process_markdown_response(self, response: str) -> Dict[str, Any]:
        """Process markdown response and extract structured data."""
        if not response or not response.strip():
            self.logger.error("Empty response from LLM")
            return {}

        # Store the raw markdown content
        processed_data = {
            "raw_markdown": response,
            "content_type": "markdown",
            "sections": self._extract_markdown_sections(response)
        }

        return processed_data

    def _extract_markdown_sections(self, markdown_content: str) -> Dict[str, str]:
        """Extract sections from markdown content."""
        import re

        sections = {}

        # Split by main headers (## level)
        section_pattern = r'^## (\d+\.\s*)?(.+?)$'
        lines = markdown_content.split('\n')

        current_section = None
        current_content = []

        for line in lines:
            header_match = re.match(section_pattern, line.strip())
            if header_match:
                # Save previous section
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()

                # Start new section
                current_section = header_match.group(2).strip()
                current_content = []
            else:
                if current_section:
                    current_content.append(line)

        # Save last section
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()

        return sections

    def _validate_markdown_content(self, data: Dict[str, Any]) -> List[str]:
        """Validate markdown content completeness."""
        errors = []

        if not data.get("raw_markdown"):
            errors.append("No markdown content found")
            return errors

        # Check content length (very lenient)
        total_content = data.get("raw_markdown", "")
        if len(total_content.strip()) < 50:
            errors.append("Content appears to be too short for analysis")

        # Very basic structure check - just ensure it's not empty
        if not total_content.strip():
            errors.append("Content is empty")

        return errors
