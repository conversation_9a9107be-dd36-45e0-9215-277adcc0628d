<!-- 
处理后的开发规则
生成时间: 2025-06-25 17:39:06
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: 基于FastAPI和领域驱动设计（DDD）的Python项目开发
- **主要改进**: 
  - 重构分层架构规范，消除冗余描述
  - 强化数据库设计约束的可操作性
  - 统一模块内代码组织标准
  - 明确测试分层策略和验证流程
  - 优化工程实践的操作指南

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计（DDD）为核心，业务逻辑集中在领域层
  - 严格分层架构（接口层→应用层→领域层←基础设施层）
  - 模块化组织，按业务功能划分独立模块
- **开发方法论**:
  - 测试驱动开发（TDD），代码变更必须伴随测试
  - 依赖倒置原则，高层模块不依赖低层实现
- **质量标准理念**:
  - 类型提示强制覆盖所有函数和变量
  - 文档与代码同步更新，英文文档标准
  - 代码即文档，通过命名表达业务语义

## 3. 技术规范
- **技术栈要求**:
  - Web框架: FastAPI
  - 数据建模: Pydantic
  - ORM: SQLAlchemy + Alembic迁移
- **框架使用规范**:
  - 接口层仅处理HTTP协议事务
  - 领域层禁止导入FastAPI/SQLAlchemy
  - 基础设施层实现领域层定义的抽象接口
- **编码标准**:
  - 严格遵循PEP 8规范
  - 所有函数签名必须包含类型提示
  - 禁止使用通用文件名（如models.py）

## 4. 架构约束
- **分层架构规则**:
  ```mermaid
  graph LR
    A[接口层] --> B[应用层]
    B --> C[领域层]
    D[基础设施层] --> C
  ```
- **模块组织方式**:
  - 顶层按业务模块划分（如auth/orders）
  - 模块内四层结构：
    - interfaces/: FastAPI路由和Schemas
    - application/: 服务用例和DTOs
    - domain/: 实体和仓库接口
    - infrastructure/: ORM和仓库实现
- **依赖关系约束**:
  - 模块内部单向依赖：接口层→应用层→领域层
  - 跨模块通信必须通过目标模块应用层服务
  - 通用代码（common/）不可反向依赖业务模块

## 5. 代码质量标准
- **代码风格规范**:
  - 所有文档字符串使用英文
  - 测试用例命名：`should_[预期行为]_when_[条件]`
  - 禁止在字段名添加技术后缀（如`_str`, `_json`）
- **测试要求**:
  - 单元测试：覆盖领域模型和业务服务
  - 集成测试：验证HTTP端点到数据库完整流程
  - 测试目录结构镜像业务模块结构
- **文档标准**:
  - API端点必须包含完整OpenAPI文档
  - 领域实体需包含业务规则说明
  - 数据库迁移脚本需描述变更原因

## 6. 工程实践
- **开发流程**:
  1. 领域层：定义实体和仓库接口
  2. 应用层：实现服务用例
  3. 基础设施层：提供仓库实现
  4. 接口层：创建路由和Schemas
- **版本控制**:
  - Git提交遵循约定式提交规范
  - 原子化提交，单次提交仅包含单一类型变更
  - Header格式：`<type>(<scope>): <subject>`
- **部署规范**:
  - 数据库迁移必须使用Alembic，禁止自动建表
  - 环境变量前缀：`AI4SE_MCP_HUB_`
  - 依赖变更同步更新requirements.txt

## 7. 最佳实践
- **开发技巧**:
  - 按业务子域前缀命名文件（如`user_models.py`）
  - 实体设计示例：
    ```python
    # 领域模型
    class User(BaseModel):
        id: UUID
        email: EmailStr
    
    # ORM模型
    class UserDB(Base):
        id = Column(PGUUID(as_uuid=True), primary_key=True)
    ```
- **问题解决方案**:
  - 模块间耦合：通过应用层服务接口通信
  - 技术污染领域层：依赖抽象接口隔离实现
  - 测试覆盖不足：Mock仓库接口保证单元测试隔离性
- **性能优化**:
  - 外键字段必须创建索引
  - 高频查询字段添加数据库索引
  - 批量操作使用SQLAlchemy批量处理API

## 8. 约束与限制
- **禁止的做法**:
  - 领域层导入框架相关包（FastAPI/SQLAlchemy）
  - 数据库字段名包含技术类型信息（如`email_str`）
  - 修改测试用例使测试通过而非修复生产代码
  - 使用深层嵌套目录组织业务子域
- **必须遵守的规则**:
  - 所有实体主键必须使用UUID类型
  - 新增功能必须创建对应测试用例
  - 环境变量命名需明确业务含义
  - 虚拟环境激活后才执行依赖安装
- **异常处理原则**:
  - HTTP错误在接口层统一处理
  - 领域异常需转换为应用层DTO
  - 基础设施异常需封装为领域可识别错误

> 本规范通过分层架构约束、明确技术实现标准和强化质量门禁，确保系统在扩展过程中保持高内聚低耦合特性，同时为AI代码生成提供可执行的标准化范式。