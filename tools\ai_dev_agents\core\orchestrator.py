"""
AI Development Workflow Orchestrator

Main orchestrator for coordinating all AI development agents.
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime
from pathlib import Path

from .base_agent import WorkflowContext, AgentResult
from ..agents.business_analyzer import BusinessAnalyzerAgent
from ..agents.domain_modeler import DomainModelerAgent
from ..agents.requirements_generator import RequirementsGeneratorAgent
from ..agents.prompt_builder import PromptBuilderAgent
from ..agents.rules_processor import RulesProcessorAgent
from ..utils.config_manager import ConfigManager
from ..utils.stream_displayer import StreamDisplayer


class Orchestrator:
    """Main orchestrator for AI development workflow."""
    
    def __init__(self, llm=None, verbose: bool = False, config_path: Optional[str] = None,
                 model_preset: Optional[str] = None, cli_args=None):
        """
        Initialize the orchestrator.

        Args:
            llm: Pre-configured LLM instance. If None, will create from config.
            verbose: Enable verbose logging.
            config_path: Path to configuration file.
            model_preset: Model preset to use from config.
            cli_args: CLI arguments for configuration overrides.
        """
        self.verbose = verbose
        self.cli_args = cli_args
        self.logger = logging.getLogger("orchestrator")

        # Initialize configuration manager
        self.config_manager = ConfigManager(config_path)

        # Setup logging from config with CLI overrides
        system_config = self.config_manager.get_system_config(cli_args)
        log_level = system_config.log_level
        if verbose or log_level == "DEBUG":
            logging.basicConfig(level=getattr(logging, log_level))

        # Initialize LLM
        if llm is None:
            self.llm = self.config_manager.create_llm(model_preset)
            if self.llm is None:
                self.logger.warning("Failed to create LLM from config. Using None (mock mode).")
        else:
            self.llm = llm

        # Initialize streaming displayer with CLI overrides
        streaming_config = self.config_manager.get_streaming_config(cli_args)
        self.stream_displayer = StreamDisplayer(
            enabled=streaming_config.get('enabled', True),
            show_thinking=streaming_config.get('show_thinking', True)
        )

        # Initialize agents with appropriate parameters
        self.agents = {
            "rules_processor": RulesProcessorAgent(
                self.llm, verbose, stream_displayer=self.stream_displayer
            ),
            "business_analyzer": BusinessAnalyzerAgent(
                self.llm, verbose  # BusinessAnalyzerAgent doesn't support stream_displayer
            ),
            "domain_modeler": DomainModelerAgent(
                self.llm, verbose, stream_displayer=self.stream_displayer
            ),
            "requirements_generator": RequirementsGeneratorAgent(
                self.llm, verbose, stream_displayer=self.stream_displayer
            ),
            "prompt_builder": PromptBuilderAgent(
                self.llm, verbose, stream_displayer=self.stream_displayer
            )
        }

        # Workflow results
        self.results: Dict[str, AgentResult] = {}
        self.context: Optional[WorkflowContext] = None

        # Store LLM configuration for display
        self.llm_config = self.config_manager.get_llm_config(model_preset)

    def get_llm_info(self) -> Dict[str, Any]:
        """Get LLM configuration information for display."""
        return {
            "provider": self.llm_config.provider,
            "model": self.llm_config.model,
            "temperature": self.llm_config.temperature,
            "max_tokens": self.llm_config.max_tokens,
            "streaming": self.llm_config.streaming,
            "timeout": self.llm_config.timeout
        }

    def create_context(self, project_root: str, project_rules: str = "") -> WorkflowContext:
        """Create workflow context with project information."""
        # Get project configuration
        project_config = self.config_manager.get_project_config()

        # Detect existing modules
        existing_modules = self._detect_existing_modules(project_root)

        # Merge with configured existing modules
        configured_modules = project_config.existing_modules
        all_modules = list(set(existing_modules + configured_modules))

        # Get tech stack from config
        tech_stack = project_config.tech_stack

        # Get architecture style from config
        architecture_style = project_config.architecture_style

        # Load project rules if not provided
        if not project_rules:
            project_rules = self._load_project_rules(project_root)

        return WorkflowContext(
            project_root=project_root,
            project_rules=project_rules,
            existing_modules=all_modules,
            tech_stack=tech_stack,
            architecture_style=architecture_style
        )
    
    def execute_full_workflow(
        self,
        prd_content: str,
        project_root: str,
        output_dir: str = "output",
        selected_modules: Optional[List[str]] = None,
        selected_rules: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Execute the complete AI development workflow."""
        try:
            # Create context
            self.context = self.create_context(project_root)

            # Create timestamped output directory
            output_path = self._create_timestamped_output_dir(output_dir)

            # Set log directory for all agents
            llm_log_dir = str(output_path / "llm_interactions")
            for agent in self.agents.values():
                agent.log_dir = llm_log_dir

            # Step 0: Rules Processing (if rules selected)
            processed_rules_file = None
            if selected_rules:
                print("[STEP 0] Processing selected rules...")
                rules_result = self._execute_rules_processing(selected_rules, str(output_path))
                if not rules_result.success:
                    return self._create_error_result("Rules processing failed", rules_result.errors)
                processed_rules_file = rules_result.data.get("output_file")
                print(f"[SUCCESS] Rules processed and saved to: {processed_rules_file}")

                # Add processed rules file to context
                self.context.additional_context["processed_rules_file"] = processed_rules_file

            # Step 1: Business Analysis
            print("[STEP 1] Analyzing business requirements...")
            business_result = self._execute_business_analysis(prd_content)
            if not business_result.success:
                return self._create_error_result("Business analysis failed", business_result.errors)

            # Store global business analysis in context
            self.context.set_global_business_analysis(business_result.data)

            # Step 2: Domain Modeling
            print("[STEP 2] Creating domain models...")
            domain_result = self._execute_domain_modeling(business_result.data)
            if not domain_result.success:
                return self._create_error_result("Domain modeling failed", domain_result.errors)

            # Store global domain model in context
            self.context.set_global_domain_model(domain_result.data)
            
            # Step 3: Identify modules to process
            available_modules = self._extract_modules_from_domain_model(domain_result.data)
            if selected_modules is None:
                selected_modules = self._interactive_module_selection(available_modules)
            
            # Step 4: Generate requirements and prompts for each module
            module_results = {}
            for module_name in selected_modules:
                print(f"[STEP 3] Generating requirements for {module_name}...")

                # Get module context for requirements generation
                module_context = self.context.get_module_context_for_step(
                    module_name, "requirements_generation"
                )

                # Generate requirements using module context
                req_result = self._execute_requirements_generation(module_context)
                if not req_result.success:
                    print(f"[WARNING] Requirements generation failed for {module_name}")
                    continue

                # Store requirements in module context
                module_ctx = self.context.get_or_create_module_context(module_name)
                module_ctx.requirements = req_result.data
                module_ctx.generated_files.extend(req_result.data.get("generated_files", []))

                # Save results (requirements generator already creates both requirements and prompts)
                module_results[module_name] = {
                    "requirements": req_result.data,
                    "files": self._copy_generated_files(
                        module_name, req_result.data, output_path / "results"
                    )
                }

                print(f"[SUCCESS] Module {module_name} completed successfully!")
            
            # Save workflow summary
            summary = self._create_workflow_summary(
                business_result, domain_result, module_results
            )
            summary_file = output_path / "workflow_summary.md"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(summary)

            print(f"\n[SUCCESS] Workflow completed! Results saved to {output_dir}/")
            print(f"[INFO] Summary: {summary_file}")

            return {
                "success": True,
                "business_analysis": business_result.data,
                "domain_model": domain_result.data,
                "modules": module_results,
                "summary_file": str(summary_file),
                "output_directory": output_dir
            }
            
        except Exception as e:
            return self._create_error_result("Workflow execution failed", [str(e)])

    def _create_timestamped_output_dir(self, base_output_dir: str) -> Path:
        """Create a timestamped output directory for this run."""
        # Create base output directory if it doesn't exist
        base_path = Path(base_output_dir)
        base_path.mkdir(exist_ok=True)

        # Create timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamped_path = base_path / f"run_{timestamp}"
        timestamped_path.mkdir(exist_ok=True)

        # Create subdirectories for different types of content
        (timestamped_path / "logs").mkdir(exist_ok=True)
        (timestamped_path / "results").mkdir(exist_ok=True)
        (timestamped_path / "llm_interactions").mkdir(exist_ok=True)

        return timestamped_path

    def _execute_rules_processing(self, selected_rules: List[str], output_dir: str) -> AgentResult:
        """Execute rules processing step."""
        try:
            agent = self.agents["rules_processor"]
            result = agent.process_rules(selected_rules, output_dir)
            self.logger.debug(f"Rules processing result type: {type(result)}")
            self.logger.debug(f"Rules processing result: {result}")
            return result
        except Exception as e:
            self.logger.error(f"Error in _execute_rules_processing: {e}")
            from .base_agent import AgentResult
            return AgentResult(
                success=False,
                data={},
                errors=[f"Rules processing failed: {str(e)}"]
            )

    def _execute_business_analysis(self, prd_content: str) -> AgentResult:
        """Execute business analysis step."""
        agent = self.agents["business_analyzer"]
        input_data = {"prd_content": prd_content}
        return agent.process(input_data, self.context)
    
    def _execute_domain_modeling(self, business_analysis: Dict[str, Any]) -> AgentResult:
        """Execute domain modeling step."""
        agent = self.agents["domain_modeler"]
        return agent.process(business_analysis, self.context)

    def _execute_requirements_generation(self, domain_model: Dict[str, Any]) -> AgentResult:
        """Execute requirements generation step."""
        agent = self.agents["requirements_generator"]

        # Pass business analysis data to requirements generator instead of domain model
        # The requirements generator now works with user stories from business analysis
        business_analysis = self.results.get("business_analyzer", {}).data if "business_analyzer" in self.results else {}

        # Add domain model to context for reference
        if hasattr(self.context, 'additional_context'):
            self.context.additional_context["domain_model"] = domain_model
        else:
            self.context.additional_context = {"domain_model": domain_model}

        return agent.process(business_analysis, self.context)

    def _execute_prompt_building(self, requirements: Dict[str, Any]) -> AgentResult:
        """Execute prompt building step."""
        agent = self.agents["prompt_builder"]
        return agent.process(requirements, self.context)
    
    def _detect_existing_modules(self, project_root: str) -> List[str]:
        """Detect existing modules in the project."""
        modules = []
        modules_path = Path(project_root) / "modules"
        
        if modules_path.exists():
            for item in modules_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    modules.append(item.name)
        
        return sorted(modules)
    
    def _load_project_rules(self, project_root: str) -> str:
        """Load project rules from .roo/rules/rules.md if exists."""
        rules_file = Path(project_root) / ".roo" / "rules" / "rules.md"
        
        if rules_file.exists():
            try:
                with open(rules_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception:
                pass
        
        # Return default rules if file not found
        return """
# Default Project Rules

- Use DDD 4-layer architecture
- UUID primary keys for all entities
- English documentation and comments
- PEP 8 code style
- Type hints required
- Test coverage > 80%
"""
    
    def _extract_modules_from_domain_model(self, domain_model: Dict[str, Any]) -> List[str]:
        """Extract module names from domain model."""
        modules = []

        # Check if this is markdown format
        if domain_model.get("content_type") == "markdown":
            return self._extract_modules_from_markdown(domain_model)

        # Legacy JSON format handling
        # Extract from bounded contexts
        for context in domain_model.get("bounded_contexts", []):
            context_name = context.get("name", "").lower().replace(" ", "_")
            if context_name and context_name not in modules:
                modules.append(context_name)

        # Extract from aggregates
        for aggregate in domain_model.get("aggregates", []):
            aggregate_name = aggregate.get("name", "").lower().replace(" ", "_")
            if aggregate_name and aggregate_name not in modules:
                modules.append(aggregate_name)

        return modules

    def _extract_modules_from_markdown(self, domain_model: Dict[str, Any]) -> List[str]:
        """Extract module names from markdown domain model."""
        import re
        modules = []

        sections = domain_model.get("sections", {})

        # Extract from bounded contexts section
        bounded_contexts_section = sections.get("边界上下文", "")
        if bounded_contexts_section:
            # Look for context names in format "### 上下文：[name]" or "### [name]"
            context_matches = re.findall(r'###\s*(?:上下文：)?(.+?)(?:\n|$)', bounded_contexts_section)
            for match in context_matches:
                context_name = match.strip().lower().replace(" ", "_").replace("：", "")
                if context_name and context_name not in modules:
                    modules.append(context_name)

        # Extract from aggregates section
        aggregates_section = sections.get("聚合设计", "")
        if aggregates_section:
            # Look for aggregate names in format "### 聚合：[name]" or "### [name]"
            aggregate_matches = re.findall(r'###\s*(?:聚合：)?(.+?)(?:\n|$)', aggregates_section)
            for match in aggregate_matches:
                aggregate_name = match.strip().lower().replace(" ", "_").replace("：", "")
                if aggregate_name and aggregate_name not in modules:
                    modules.append(aggregate_name)

        # If no modules found, try to extract from entity names
        if not modules:
            entities_section = sections.get("领域实体", "")
            if entities_section:
                entity_matches = re.findall(r'###\s*(?:实体：)?(.+?)(?:\n|$)', entities_section)
                for match in entity_matches:
                    entity_name = match.strip().lower().replace(" ", "_").replace("：", "")
                    if entity_name and entity_name not in modules:
                        modules.append(entity_name)

        return modules

    def _interactive_module_selection(self, available_modules: List[str]) -> List[str]:
        """Interactive module selection (fallback to all modules if no interaction)."""
        if not available_modules:
            return []
        
        print(f"\n[MODULES] Available modules: {', '.join(available_modules)}")
        print("[INFO] Tip: You can select specific modules or process all")

        # For non-interactive environments, return all modules
        return available_modules
    
    def _filter_domain_model_for_module(
        self, domain_model: Dict[str, Any], module_name: str
    ) -> Dict[str, Any]:
        """Filter domain model to include only relevant parts for a specific module."""
        filtered = {}
        
        # Filter bounded contexts
        filtered["bounded_contexts"] = [
            ctx for ctx in domain_model.get("bounded_contexts", [])
            if ctx.get("name", "").lower().replace(" ", "_") == module_name
        ]
        
        # Filter aggregates
        filtered["aggregates"] = [
            agg for agg in domain_model.get("aggregates", [])
            if agg.get("name", "").lower().replace(" ", "_") == module_name or
               agg.get("bounded_context", "").lower().replace(" ", "_") == module_name
        ]
        
        # Filter entities
        relevant_aggregates = {agg.get("name") for agg in filtered["aggregates"]}
        filtered["domain_entities"] = [
            entity for entity in domain_model.get("domain_entities", [])
            if entity.get("aggregate") in relevant_aggregates
        ]
        
        # Filter other components similarly
        for key in ["value_objects", "domain_services", "repositories", "domain_events"]:
            filtered[key] = domain_model.get(key, [])
        
        return filtered

    def _copy_generated_files(
        self,
        module_name: str,
        requirements_data: Dict[str, Any],
        output_path: Path
    ) -> Dict[str, str]:
        """Copy generated files from requirements generator to final output directory."""
        files = {}
        output_path.mkdir(parents=True, exist_ok=True)

        generated_files = requirements_data.get("generated_files", [])

        for file_path in generated_files:
            source_path = Path(file_path)
            if not source_path.exists():
                continue

            # Determine file type and create appropriate destination
            if source_path.name.endswith("_requirements.md"):
                dest_file = output_path / f"{module_name}_requirements.md"
                files["requirements"] = str(dest_file)
            elif source_path.name.startswith("prompt_"):
                dest_file = output_path / f"{module_name}_ai_prompt.md"
                files["prompt"] = str(dest_file)
            else:
                # Keep original filename for other files
                dest_file = output_path / source_path.name
                files[source_path.stem] = str(dest_file)

            # Copy file content
            try:
                import shutil
                shutil.copy2(source_path, dest_file)
            except Exception as e:
                self.logger.warning(f"Failed to copy {source_path} to {dest_file}: {e}")

        return files

    def _save_module_results(
        self,
        module_name: str,
        requirements: Dict[str, Any],
        prompt_data: Dict[str, Any],
        output_path: Path
    ) -> Dict[str, str]:
        """Save module results to files."""
        files = {}

        # Save requirements
        req_file = output_path / f"{module_name}_requirements.json"
        with open(req_file, 'w', encoding='utf-8') as f:
            json.dump(requirements, f, indent=2, ensure_ascii=False, default=str)
        files["requirements"] = str(req_file)

        # Save AI prompt - check if prompt_data contains file paths or content
        prompt_file = output_path / f"{module_name}_ai_prompt.md"
        prompt_content = ""

        # Check if prompt_data contains prompt_files (from PromptBuilderAgent)
        if "prompt_files" in prompt_data:
            # Find the prompt file for this module
            for file_path in prompt_data["prompt_files"]:
                if module_name in Path(file_path).name:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            prompt_content = f.read()
                        break
                    except Exception as e:
                        self.logger.warning(f"Failed to read prompt file {file_path}: {e}")
        else:
            # Fallback to prompt_content field
            prompt_content = prompt_data.get("prompt_content", "")

        with open(prompt_file, 'w', encoding='utf-8') as f:
            f.write(prompt_content)
        files["prompt"] = str(prompt_file)

        return files
    
    def _create_workflow_summary(
        self,
        business_result: AgentResult,
        domain_result: AgentResult,
        module_results: Dict[str, Any]
    ) -> str:
        """Create workflow execution summary in Markdown format."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        summary = f"""# AI开发工作流执行总结

## 执行信息
- **执行时间**: {timestamp}
- **工作流状态**: 已完成
- **处理模块数量**: {len(module_results)}

## 业务分析结果
- **执行状态**: {'成功' if business_result.success else '失败'}
- **识别实体数量**: {len(business_result.data.get("core_entities", []))}
- **功能需求数量**: {len(business_result.data.get("functional_requirements", []))}
- **用户故事数量**: {len(business_result.data.get("user_stories", []))}

## 领域建模结果
- **执行状态**: {'成功' if domain_result.success else '失败'}
- **边界上下文数量**: {len(domain_result.data.get("bounded_contexts", []))}
- **聚合根数量**: {len(domain_result.data.get("aggregates", []))}
- **领域实体数量**: {len(domain_result.data.get("domain_entities", []))}

## 模块处理结果
"""

        for module_name, result in module_results.items():
            files = result.get("files", {})
            summary += f"""
### {module_name} 模块
- **需求文档**: {files.get("requirements", "未生成")}
- **AI提示词**: {files.get("prompt", "未生成")}
"""

        summary += f"""
## 输出文件统计
- **总文件数**: {sum(len(result.get("files", {})) for result in module_results.values())}
- **模块列表**: {", ".join(module_results.keys())}

---
*此总结由AI开发工作流自动生成*
"""

        return summary
    
    def _create_error_result(self, message: str, errors: List[str]) -> Dict[str, Any]:
        """Create error result."""
        return {
            "success": False,
            "error": message,
            "details": errors,
            "timestamp": datetime.now().isoformat()
        }
