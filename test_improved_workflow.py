#!/usr/bin/env python3
"""
Test script for the improved AI development workflow.
"""

import sys
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.append('.')

def test_xml_parsing():
    """Test XML parsing functionality."""
    print("测试XML解析功能...")
    
    try:
        from tools.ai_dev_agents.core.xml_schemas import XMLParser, BusinessAnalysis, UserStory
        
        # Test XML content
        test_xml = """
        <business_analysis generated_at="2024-01-01T00:00:00">
            <project_info>
                <name>测试项目</name>
                <description>这是一个测试项目</description>
                <objectives>
                    <objective>目标1</objective>
                    <objective>目标2</objective>
                </objectives>
            </project_info>
            <functional_requirements>
                <requirement id="FR-001" priority="high">
                    <title>用户登录</title>
                    <description>用户可以通过用户名密码登录系统</description>
                    <acceptance_criteria>
                        <criterion>输入正确用户名密码可以登录</criterion>
                        <criterion>输入错误信息显示错误提示</criterion>
                    </acceptance_criteria>
                </requirement>
            </functional_requirements>
            <user_stories>
                <story id="US-001" domain_context="用户管理">
                    <title>用户登录</title>
                    <description>作为用户，我希望能够登录系统，以便访问个人功能</description>
                    <acceptance_criteria>
                        <criterion>可以输入用户名密码</criterion>
                        <criterion>登录成功后跳转到主页</criterion>
                    </acceptance_criteria>
                    <priority>high</priority>
                </story>
            </user_stories>
        </business_analysis>
        """
        
        # Parse XML
        business_analysis = XMLParser.parse_business_analysis(test_xml)
        print(f"✅ XML解析成功")
        print(f"   项目名称: {business_analysis.project_name}")
        print(f"   功能需求数量: {len(business_analysis.functional_requirements)}")
        print(f"   用户故事数量: {len(business_analysis.user_stories)}")
        
        # Test user story extraction
        user_stories = XMLParser.extract_user_stories_from_xml(test_xml)
        print(f"✅ 用户故事提取成功: {len(user_stories)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ XML解析测试失败: {e}")
        return False


def test_agent_creation():
    """Test agent creation."""
    print("\n测试Agent创建...")
    
    try:
        from tools.ai_dev_agents.agents.improved_business_analyzer import ImprovedBusinessAnalyzerAgent
        from tools.ai_dev_agents.agents.improved_requirements_analyzer import ImprovedRequirementsAnalyzerAgent
        from tools.ai_dev_agents.agents.technical_leader import TechnicalLeaderAgent
        from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
        
        # Create agents without LLM client for testing
        business_agent = ImprovedBusinessAnalyzerAgent(None, verbose=True)
        requirements_agent = ImprovedRequirementsAnalyzerAgent(None, verbose=True)
        technical_leader = TechnicalLeaderAgent(None, verbose=True)
        presentation_agent = PresentationGeneratorAgent(None, verbose=True)
        
        print(f"✅ Agent创建成功")
        print(f"   业务分析Agent: {business_agent.agent_name}")
        print(f"   需求分析Agent: {requirements_agent.agent_name}")
        print(f"   技术负责人Agent: {technical_leader.agent_name}")
        print(f"   展示生成Agent: {presentation_agent.agent_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent创建测试失败: {e}")
        return False


def test_orchestrator_creation():
    """Test orchestrator creation."""
    print("\n测试编排器创建...")
    
    try:
        from tools.ai_dev_agents.core.improved_orchestrator import ImprovedOrchestrator
        from tools.ai_dev_agents.agents.improved_business_analyzer import ImprovedBusinessAnalyzerAgent
        from tools.ai_dev_agents.agents.domain_modeler import DomainModelerAgent
        from tools.ai_dev_agents.agents.improved_requirements_analyzer import ImprovedRequirementsAnalyzerAgent
        from tools.ai_dev_agents.agents.technical_leader import TechnicalLeaderAgent
        from tools.ai_dev_agents.agents.result_generator import ResultGeneratorAgent
        from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
        
        # Create agents
        agents = {
            "business_analyzer": ImprovedBusinessAnalyzerAgent(None, verbose=True),
            "domain_modeler": DomainModelerAgent(None, verbose=True),
            "requirements_analyzer": ImprovedRequirementsAnalyzerAgent(None, verbose=True),
            "technical_leader": TechnicalLeaderAgent(None, verbose=True),
            "result_generator": ResultGeneratorAgent(None, verbose=True),
            "presentation_generator": PresentationGeneratorAgent(None, verbose=True)
        }
        
        # Create orchestrator (it will create its own context)
        orchestrator = ImprovedOrchestrator(agents, verbose=True)
        
        print(f"✅ 编排器创建成功")
        print(f"   包含Agent数量: {len(orchestrator.agents)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 编排器创建测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False


def test_html_generation():
    """Test HTML generation."""
    print("\n测试HTML生成...")
    
    try:
        from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
        from tools.ai_dev_agents.core.base_agent import WorkflowContext
        
        # Create test data
        test_workflow_results = {
            "business_analysis": {
                "project_name": "测试项目",
                "project_description": "这是一个测试项目的描述",
                "objectives": ["目标1", "目标2"],
                "functional_requirements": [
                    {
                        "id": "FR-001",
                        "title": "用户登录",
                        "description": "用户登录功能",
                        "priority": "high"
                    }
                ]
            },
            "requirements": {
                "domain_contexts": [
                    {
                        "name": "用户管理",
                        "description": "用户相关功能",
                        "stories": [
                            {
                                "id": "US-001",
                                "title": "用户登录",
                                "description": "作为用户，我希望能够登录",
                                "acceptance_criteria": ["可以输入用户名密码"],
                                "priority": "high"
                            }
                        ]
                    }
                ],
                "user_stories": [
                    {
                        "id": "US-001",
                        "title": "用户登录",
                        "description": "作为用户，我希望能够登录",
                        "acceptance_criteria": ["可以输入用户名密码"],
                        "priority": "high",
                        "domain_context": "用户管理"
                    }
                ]
            },
            "quality_review": {
                "approved": True,
                "overall_score": 8,
                "summary": "整体质量良好"
            }
        }
        
        test_execution_context = {
            "completed_steps": 6,
            "total_steps": 6,
            "start_time": "2024-01-01T00:00:00"
        }
        
        # Create presentation agent
        presentation_agent = PresentationGeneratorAgent(None, verbose=True)
        
        # Create context with required parameters
        from tools.ai_dev_agents.core.base_agent import WorkflowContext
        context = WorkflowContext(
            project_root="test_project",
            project_rules={},
            existing_modules=[],
            tech_stack=["FastAPI", "SQLAlchemy"],
            architecture_style="DDD"
        )
        context.output_path = Path("test_output")
        
        # Test HTML generation
        input_data = {
            "workflow_results": test_workflow_results,
            "execution_context": test_execution_context,
            "agent_results": {}
        }
        
        result = presentation_agent.process(input_data, context)
        
        if result.success:
            print(f"✅ HTML生成成功")
            print(f"   HTML文件: {result.data.get('html_file', 'N/A')}")
            print(f"   报告章节数: {result.data.get('report_sections', [])}")
        else:
            print(f"❌ HTML生成失败: {result.error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ HTML生成测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False


def main():
    """Run all tests."""
    print("开始测试改进的AI开发工作流系统")
    print("=" * 50)
    
    tests = [
        test_xml_parsing,
        test_agent_creation,
        test_orchestrator_creation,
        test_html_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新的工作流系统基础功能正常")
        
        # Check if we can run a real workflow test
        prd_file = Path("tools/ai_dev_agents/input/ai4se_mcp_hub_prd.md")
        if prd_file.exists():
            print(f"\n发现PRD文件: {prd_file}")
            print("可以运行以下命令测试完整工作流:")
            print(f"python tools/ai_dev_agents/improved_main.py {prd_file} --verbose")
        else:
            print(f"\n未找到PRD文件: {prd_file}")
            print("请准备PRD文档后运行完整工作流测试")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
