<!-- 
处理后的开发规则
生成时间: 2025-06-25 20:49:15
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发规范
- **主要改进**: 
  - 重构了规则组织结构，使其更加系统化
  - 消除了重复内容，如测试规范和环境管理
  - 补充了数据库设计和模块组织的详细规范
  - 增强了规则的可操作性和一致性

## 2. 核心原则
- **领域驱动设计(DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构
- **技术栈统一**: 使用FastAPI、Pydantic、SQLAlchemy等技术栈
- **代码质量**: 严格遵循PEP 8规范，强制类型提示
- **文档标准**: 所有文档和注释必须使用英文编写
- **测试驱动**: 所有代码变更必须伴随自动化测试

## 3. 技术规范
- **Web框架**: FastAPI
- **数据验证**: Pydantic
- **数据库**: SQLAlchemy + Alembic迁移
- **测试框架**: Pytest
- **代码风格**: 严格遵循PEP 8
- **类型提示**: 所有函数和变量必须包含类型提示

## 4. 架构约束
### 4.1 项目结构
```
.
├── modules/          # 业务模块
│   ├── auth/        # 认证模块
│   └── orders/      # 订单模块
├── common/          # 通用代码
├── tests/           # 测试代码
└── main.py          # 应用入口
```

### 4.2 分层架构
- **接口层(Interfaces)**: 处理HTTP请求和响应
- **应用层(Application)**: 编排业务用例
- **领域层(Domain)**: 核心业务逻辑
- **基础设施层(Infrastructure)**: 技术实现

### 4.3 依赖规则
1. 模块内部单向依赖: Interfaces → Application → Domain
2. 领域层纯粹性: 禁止引入外部框架依赖
3. 模块间通信: 必须通过目标模块的应用层服务接口

## 5. 代码质量标准
### 5.1 代码风格
- 严格遵循PEP 8规范
- 所有函数和变量必须包含类型提示
- 禁止使用魔法数字和字符串

### 5.2 测试要求
- **单元测试**: 覆盖领域模型和应用服务
- **集成测试**: 验证端到端流程
- **测试覆盖率**: 关键路径必须100%覆盖
- **测试命名**: 使用BDD风格命名

### 5.3 文档标准
- 所有API必须包含OpenAPI文档
- 复杂逻辑必须包含详细注释
- 变更必须同步更新文档

## 6. 工程实践
### 6.1 开发流程
1. 领域模型优先
2. 应用服务实现
3. 基础设施实现
4. 接口层开发
5. 测试开发

### 6.2 版本控制
- 遵循约定式提交规范
- 提交信息必须包含类型和范围
- 鼓励原子化提交

### 6.3 环境管理
- 使用虚拟环境隔离依赖
- 环境变量统一管理
- 依赖变更必须更新requirements.txt

## 7. 最佳实践
### 7.1 数据库设计
- 主键必须使用UUID类型
- 字段命名反映业务含义，禁止技术后缀
- 表名使用复数形式，避免缩写

### 7.2 模块组织
- 按业务子域组织代码
- 文件命名必须包含业务前缀
- 禁止过度嵌套目录结构

### 7.3 重构原则
- 创建新文件优先
- 完整上下文分析
- 测试保障变更安全

## 8. 约束与限制
### 8.1 严格禁止
- 领域层引入外部框架依赖
- 直接访问其他模块的领域层
- 自动建表(必须使用Alembic迁移)

### 8.2 必须遵守
- 所有API端点文档化
- 代码变更伴随测试
- 提交信息规范化

### 8.3 异常处理
- 业务异常在领域层定义
- 技术异常在基础设施层处理
- 接口层统一转换异常响应

## 质量要求
- **完整性**: 覆盖从架构设计到代码实现的完整开发流程
- **一致性**: 各层规范和约束相互协调
- **可操作性**: 提供具体示例和实施步骤
- **可维护性**: 模块化和分层设计便于扩展
- **专业性**: 使用准确的技术术语和表达