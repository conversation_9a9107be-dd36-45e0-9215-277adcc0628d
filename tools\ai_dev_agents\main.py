#!/usr/bin/env python3
"""
AI Development Agents - Main Entry Point

This is the main entry point for the AI Development Agents toolkit.
"""

import sys
from pathlib import Path

# Add the parent directory to Python path for absolute imports
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from ai_dev_agents.utils.cli import main as cli_main


def main():
    """Main entry point for the AI Development Agents toolkit."""
    # Delegate to CLI main function
    cli_main()


if __name__ == "__main__":
    main()
