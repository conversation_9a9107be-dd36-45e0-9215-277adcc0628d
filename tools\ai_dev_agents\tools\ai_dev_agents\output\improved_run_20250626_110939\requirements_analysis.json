{"domain_contexts": [{"name": "订单处理上下文", "description": "负责订单创建、状态管理和履约流程", "stories": [{"id": "US-001", "title": "创建新订单", "description": "作为顾客，我希望能够创建新订单，以便购买商品", "acceptance_criteria": ["订单必须包含至少一个明细项", "订单总金额必须等于各明细项金额之和", "订单初始状态应为\"待支付\""], "priority": "high", "domain_context": "订单处理上下文", "business_value": "实现核心购物流程", "technical_notes": "需要调用OrderProcessingService.place_order方法"}, {"id": "US-002", "title": "添加订单明细项", "description": "作为顾客，我希望能够向订单添加商品明细，以便选择购买的商品", "acceptance_criteria": ["明细项必须包含有效的商品ID", "数量必须大于0", "添加后订单总金额应自动重新计算"], "priority": "high", "domain_context": "订单处理上下文", "business_value": "支持灵活的商品选择", "technical_notes": "实现Order.add_item方法"}, {"id": "US-003", "title": "取消订单", "description": "作为顾客，我希望能够取消待支付的订单，以便放弃购买", "acceptance_criteria": ["只有待支付状态的订单可以取消", "取消后订单状态应变为\"已取消\"", "应记录取消原因"], "priority": "medium", "domain_context": "订单处理上下文", "business_value": "提供订单取消功能", "technical_notes": "实现Order.cancel方法"}, {"id": "US-004", "title": "生成支付请求", "description": "作为顾客，我希望能够生成支付请求，以便完成订单支付", "acceptance_criteria": ["只能为待支付状态的订单生成支付请求", "支付请求应包含订单总金额", "支付请求应包含可用的支付方式"], "priority": "high", "domain_context": "订单处理上下文", "business_value": "连接订单和支付流程", "technical_notes": "实现OrderProcessingService.checkout方法"}]}, {"name": "支付上下文", "description": "处理支付流程和财务对账", "stories": [{"id": "US-005", "title": "处理支付", "description": "作为顾客，我希望能够完成订单支付，以便确认购买", "acceptance_criteria": ["支付金额必须与订单金额一致", "支付成功后应生成支付流水号", "支付成功后应触发PaymentCompleted事件"], "priority": "high", "domain_context": "支付上下文", "business_value": "实现核心支付功能", "technical_notes": "实现PaymentService.process_payment方法"}, {"id": "US-006", "title": "处理退款", "description": "作为顾客，我希望能够申请退款，以便在取消订单后收回款项", "acceptance_criteria": ["只能为已支付的订单申请退款", "退款金额不应超过原支付金额", "退款成功后应更新订单状态"], "priority": "medium", "domain_context": "支付上下文", "business_value": "提供退款功能", "technical_notes": "实现PaymentService.refund方法"}]}], "user_stories": [{"id": "US-001", "title": "创建新订单", "description": "作为顾客，我希望能够创建新订单，以便购买商品", "acceptance_criteria": ["订单必须包含至少一个明细项", "订单总金额必须等于各明细项金额之和", "订单初始状态应为\"待支付\""], "priority": "high", "domain_context": "订单处理上下文", "business_value": "实现核心购物流程", "technical_notes": "需要调用OrderProcessingService.place_order方法"}, {"id": "US-002", "title": "添加订单明细项", "description": "作为顾客，我希望能够向订单添加商品明细，以便选择购买的商品", "acceptance_criteria": ["明细项必须包含有效的商品ID", "数量必须大于0", "添加后订单总金额应自动重新计算"], "priority": "high", "domain_context": "订单处理上下文", "business_value": "支持灵活的商品选择", "technical_notes": "实现Order.add_item方法"}, {"id": "US-003", "title": "取消订单", "description": "作为顾客，我希望能够取消待支付的订单，以便放弃购买", "acceptance_criteria": ["只有待支付状态的订单可以取消", "取消后订单状态应变为\"已取消\"", "应记录取消原因"], "priority": "medium", "domain_context": "订单处理上下文", "business_value": "提供订单取消功能", "technical_notes": "实现Order.cancel方法"}, {"id": "US-004", "title": "生成支付请求", "description": "作为顾客，我希望能够生成支付请求，以便完成订单支付", "acceptance_criteria": ["只能为待支付状态的订单生成支付请求", "支付请求应包含订单总金额", "支付请求应包含可用的支付方式"], "priority": "high", "domain_context": "订单处理上下文", "business_value": "连接订单和支付流程", "technical_notes": "实现OrderProcessingService.checkout方法"}, {"id": "US-005", "title": "处理支付", "description": "作为顾客，我希望能够完成订单支付，以便确认购买", "acceptance_criteria": ["支付金额必须与订单金额一致", "支付成功后应生成支付流水号", "支付成功后应触发PaymentCompleted事件"], "priority": "high", "domain_context": "支付上下文", "business_value": "实现核心支付功能", "technical_notes": "实现PaymentService.process_payment方法"}, {"id": "US-006", "title": "处理退款", "description": "作为顾客，我希望能够申请退款，以便在取消订单后收回款项", "acceptance_criteria": ["只能为已支付的订单申请退款", "退款金额不应超过原支付金额", "退款成功后应更新订单状态"], "priority": "medium", "domain_context": "支付上下文", "business_value": "提供退款功能", "technical_notes": "实现PaymentService.refund方法"}], "story_dependencies": [{"from": "US-001", "to": "US-002", "type": "prerequisite", "description": "必须先创建订单才能添加明细项"}, {"from": "US-001", "to": "US-003", "type": "prerequisite", "description": "必须先创建订单才能取消"}, {"from": "US-001", "to": "US-004", "type": "prerequisite", "description": "必须先创建订单才能生成支付请求"}, {"from": "US-004", "to": "US-005", "type": "prerequisite", "description": "必须先生成支付请求才能处理支付"}, {"from": "US-005", "to": "US-006", "type": "prerequisite", "description": "必须先完成支付才能处理退款"}], "generated_at": "2025-06-26T11:16:12.912703"}