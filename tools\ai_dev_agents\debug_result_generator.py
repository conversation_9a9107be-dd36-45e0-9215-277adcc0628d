#!/usr/bin/env python3
"""
Debug script to test data extraction logic directly
"""

import json
import sys
import os
from typing import Dict, Any, List

def extract_user_stories_from_requirements(requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract user stories from requirements data structure."""
    user_stories = []

    print(f"Requirements keys: {list(requirements.keys())}")
    print(f"Requirements type: {type(requirements)}")

    # Try domain_contexts structure
    if "domain_contexts" in requirements:
        print(f"Found domain_contexts: {len(requirements['domain_contexts'])} contexts")
        for context in requirements["domain_contexts"]:
            if isinstance(context, dict) and "stories" in context:
                stories_in_context = context["stories"]
                print(f"Context '{context.get('name', 'unknown')}' has {len(stories_in_context)} stories")
                user_stories.extend(stories_in_context)

    print(f"Total extracted user stories: {len(user_stories)}")
    return user_stories

def test_data_extraction():
    """Test the data extraction logic with real data"""

    # Load the latest requirements analysis data
    output_dir = "output/improved_run_20250626_103906"
    requirements_file = f"{output_dir}/requirements_analysis.json"

    if not os.path.exists(requirements_file):
        print(f"Requirements file not found: {requirements_file}")
        return

    # Load the data
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements_data = json.load(f)

    print(f"Loaded requirements data with keys: {list(requirements_data.keys())}")

    # Test user stories extraction
    print("Testing user stories extraction...")
    user_stories = extract_user_stories_from_requirements(requirements_data)
    print(f"Extracted {len(user_stories)} user stories")

    for i, story in enumerate(user_stories):
        print(f"Story {i+1}: {story.get('id', 'NO_ID')} - {story.get('title', 'NO_TITLE')}")

    # Print the structure for debugging
    if "domain_contexts" in requirements_data:
        print("\n=== DOMAIN CONTEXTS STRUCTURE ===")
        for i, context in enumerate(requirements_data["domain_contexts"]):
            print(f"Context {i}: {context.get('name', 'NO_NAME')}")
            if "stories" in context:
                print(f"  Stories count: {len(context['stories'])}")
                for j, story in enumerate(context["stories"][:2]):  # Show first 2 stories
                    print(f"    Story {j}: {story.get('id', 'NO_ID')} - {story.get('title', 'NO_TITLE')}")
            else:
                print("  No 'stories' key found")
    else:
        print("No 'domain_contexts' key found in requirements data")

if __name__ == "__main__":
    test_data_extraction()
