#!/usr/bin/env python3
"""
Simple workflow test with minimal PRD content
"""

import sys
import json
import tempfile
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def create_simple_prd():
    """Create a minimal PRD for testing"""
    prd_content = """
# 简单测试项目需求文档

## 项目概述
这是一个简单的用户管理系统，用于测试AI开发工作流。

## 核心功能
1. 用户注册和登录
2. 用户资料管理
3. 权限控制

## 用户角色
- 普通用户：可以注册、登录、管理个人资料
- 管理员：可以管理所有用户

## 功能需求
- FR-001: 用户注册功能
- FR-002: 用户登录功能
- FR-003: 用户资料编辑功能
- FR-004: 管理员用户管理功能

## 非功能需求
- 系统应支持100个并发用户
- 响应时间应小于2秒
- 数据必须加密存储

## 业务规则
- 用户名必须唯一
- 密码必须包含至少8个字符
- 管理员权限需要特殊授权
"""
    return prd_content

def create_simple_rules():
    """Create minimal rules for testing"""
    rules_content = """
# 开发规则

## 技术栈
- 使用 FastAPI 框架
- 使用 Pydantic 进行数据验证
- 使用 SQLAlchemy 作为 ORM

## 架构原则
- 遵循 DDD 分层架构
- 领域层不依赖外部框架
- 使用 UUID 作为主键

## 代码质量
- 必须包含类型提示
- 遵循 PEP 8 规范
- 必须编写测试用例
"""
    return rules_content

def test_simple_workflow():
    """Test workflow with simple content"""
    
    # Create temporary files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create input files
        prd_file = temp_path / "simple_prd.txt"
        rules_file = temp_path / "simple_rules.md"
        
        prd_file.write_text(create_simple_prd(), encoding='utf-8')
        rules_file.write_text(create_simple_rules(), encoding='utf-8')
        
        print(f"Created test files:")
        print(f"  PRD: {prd_file}")
        print(f"  Rules: {rules_file}")
        print(f"  PRD size: {len(prd_file.read_text())} characters")
        
        # Import and run workflow
        try:
            from tools.ai_dev_agents.main import main
            
            # Prepare arguments
            import sys
            original_argv = sys.argv
            sys.argv = [
                'main.py',
                'workflow',
                str(prd_file),
                '--rules', str(rules_file),
                '--output', str(temp_path / 'output')
            ]
            
            print("\n=== Running Simple Workflow ===")
            result = main()
            
            # Restore original argv
            sys.argv = original_argv
            
            print(f"Workflow result: {result}")
            
            # Check output files
            output_dir = temp_path / 'output'
            if output_dir.exists():
                print(f"\nOutput files created:")
                for file in output_dir.rglob('*'):
                    if file.is_file():
                        print(f"  {file.relative_to(output_dir)}: {file.stat().st_size} bytes")
            
            return True
            
        except Exception as e:
            print(f"Workflow failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_domain_modeler_only():
    """Test only the domain modeler step"""
    
    print("\n=== Testing Domain Modeler Only ===")
    
    try:
        # Mock input data (business analysis result)
        mock_business_analysis = {
            "content_type": "business_analysis",
            "business_overview": {
                "project_name": "用户管理系统",
                "description": "简单的用户管理系统"
            },
            "core_entities": [
                {
                    "name": "用户",
                    "description": "系统用户实体",
                    "attributes": ["用户名", "邮箱", "密码"]
                }
            ],
            "functional_requirements": [
                {
                    "id": "FR-001",
                    "name": "用户注册",
                    "description": "用户可以注册新账户"
                }
            ]
        }
        
        # Import domain modeler
        from tools.ai_dev_agents.agents.domain_modeler import DomainModelerAgent
        from tools.ai_dev_agents.core.base_agent import WorkflowContext
        
        # Create agent
        agent = DomainModelerAgent(verbose=True)
        
        # Create context
        context = WorkflowContext()
        context.add_data("processed_rules", "使用FastAPI和DDD架构")
        
        print("Testing domain modeler with mock data...")
        
        # This would normally call LLM, but we can test the structure
        print("Domain modeler initialized successfully")
        print("System prompt loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"Domain modeler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing simplified workflow...")
    print("=" * 50)
    
    # Test JSON parsing first
    print("1. Testing JSON parsing...")
    exec(open("test_json_conversion.py").read())
    
    print("\n" + "=" * 50)
    print("2. Testing domain modeler initialization...")
    domain_test_result = test_domain_modeler_only()
    
    print("\n" + "=" * 50)
    print("3. Testing simple workflow...")
    workflow_test_result = test_simple_workflow()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"  Domain Modeler: {'PASS' if domain_test_result else 'FAIL'}")
    print(f"  Simple Workflow: {'PASS' if workflow_test_result else 'FAIL'}")
    
    if domain_test_result and workflow_test_result:
        print("\n✅ All tests passed! Ready for full workflow testing.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
