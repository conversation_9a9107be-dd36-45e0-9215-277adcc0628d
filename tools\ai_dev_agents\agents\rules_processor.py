"""
Rules Processor Agent

This agent processes selected rules files using LLM to understand and regenerate
high-quality, standardized rule content for use in the development workflow.
"""

import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext


class RulesProcessorAgent(BaseAgent):
    """Agent for processing and standardizing rules files using LLM."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None):
        super().__init__(llm, verbose, stream_displayer)
        self.name = "RulesProcessor"
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for rules processing."""
        try:
            prompts_path = Path(__file__).parent.parent / "prompts" / "rules_processing.yaml"
            with open(prompts_path, 'r', encoding='utf-8') as f:
                prompts = yaml.safe_load(f)

            if 'system_prompt' not in prompts:
                raise Exception("Missing 'system_prompt' in rules processing prompts configuration")
            return prompts['system_prompt']

        except Exception as e:
            self.logger.error(f"Failed to load system prompt from YAML: {e}")
            raise Exception(f"Cannot load system prompt from YAML: {e}")
    

    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """
        Process method required by BaseAgent interface.

        Args:
            input_data: Should contain 'selected_rules' and 'output_dir'
            context: Workflow context

        Returns:
            AgentResult with processing results
        """
        selected_rules = input_data.get('selected_rules', [])
        output_dir = input_data.get('output_dir', 'output')
        return self.process_rules(selected_rules, output_dir)

    def process_rules(self, selected_rules: List[str], output_dir: str) -> AgentResult:
        """
        Process selected rules files and generate standardized rules content.
        
        Args:
            selected_rules: List of selected rules file paths
            output_dir: Output directory for processed rules
            
        Returns:
            AgentResult with processing results
        """
        try:
            if not selected_rules:
                return AgentResult(
                    success=True,
                    data={"message": "No rules files selected for processing"},
                    metadata={"processed_files": [], "output_file": None}
                )
            
            # Load and combine rules content
            combined_rules = self._load_and_combine_rules(selected_rules)
            
            # Generate system prompt
            system_prompt = self.get_system_prompt()
            
            # Create user prompt with rules content
            user_prompt = self._create_processing_prompt(selected_rules, combined_rules)
            
            # Execute LLM call
            messages = self._create_messages(system_prompt, user_prompt)
            response = self._execute_llm_call(messages)
            
            # Save processed rules to output directory
            output_file = self._save_processed_rules(response, output_dir, selected_rules)
            
            return AgentResult(
                success=True,
                data={
                    "processed_rules": response,
                    "output_file": output_file,
                    "source_files": selected_rules
                },
                metadata={
                    "processed_files": len(selected_rules),
                    "output_file": output_file,
                    "processing_timestamp": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error processing rules: {e}")
            return AgentResult(
                success=False,
                data={},
                errors=[f"Rules processing failed: {str(e)}"]
            )
    
    def _load_and_combine_rules(self, rules_files: List[str]) -> str:
        """Load and combine content from multiple rules files."""
        combined_content = []
        
        for rules_file in rules_files:
            try:
                with open(rules_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    file_name = Path(rules_file).name
                    combined_content.append(f"## 来源文件: {file_name}\n\n{content}\n\n")
            except Exception as e:
                self.logger.warning(f"Failed to load rules file {rules_file}: {e}")
                combined_content.append(f"## 来源文件: {Path(rules_file).name}\n\n[文件读取失败: {e}]\n\n")
        
        return "\n".join(combined_content)
    
    def _create_processing_prompt(self, selected_files: List[str], combined_rules: str) -> str:
        """Create user prompt for rules processing."""
        file_names = [Path(f).name for f in selected_files]
        
        return f"""请处理以下规则文件内容，生成标准化的开发规则文档：

**选中的规则文件**:
{', '.join(file_names)}

**规则文件内容**:
{combined_rules}

请分析这些规则的内容，理解其核心要求，然后重新组织生成一份高质量、标准化的规则文档。确保：

1. 消除重复和冗余内容
2. 解决可能存在的矛盾
3. 补充缺失的重要信息
4. 提高规则的可操作性
5. 保持逻辑结构清晰

请严格按照系统提示词中的格式要求输出处理后的规则。
"""
    
    def _save_processed_rules(self, processed_content: str, output_dir: str, source_files: List[str]) -> str:
        """Save processed rules to output directory."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Create output file
        output_file = output_path / "processed_rules.md"
        
        # Add metadata header
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        source_file_names = [Path(f).name for f in source_files]
        
        header = f"""<!-- 
处理后的开发规则
生成时间: {timestamp}
源文件: {', '.join(source_file_names)}
处理器: RulesProcessorAgent
-->

"""
        
        # Write processed content
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(header + processed_content)
        
        return str(output_file)
    
    def _create_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        """Create messages for LLM API call."""
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
    
    def _execute_llm_call(self, messages: List[Dict[str, str]]) -> str:
        """Execute LLM call with streaming support."""
        result = self._execute_llm_call_with_streaming(messages, "规则处理")
        if result.success:
            return result.data
        else:
            raise Exception(f"LLM call failed: {', '.join(result.errors)}")
    
    def _get_mock_response(self) -> str:
        """Get mock response for testing."""
        return """
# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2024-01-01 12:00:00
- **规则范围**: FastAPI + DDD 项目开发

## 2. 核心原则
- 严格遵循领域驱动设计(DDD)原则
- 使用FastAPI作为Web框架
- 强制要求类型提示
- 英文文档和注释

## 3. 技术规范
- Web框架: FastAPI
- 数据验证: Pydantic
- ORM: SQLAlchemy + Alembic
- 代码风格: PEP 8

## 4. 架构约束
- 四层DDD架构: Interfaces → Application → Domain → Infrastructure
- 模块化组织: 按业务功能划分模块
- 依赖倒置: 基础设施层实现领域层接口

## 5. 代码质量标准
- 100% 类型提示覆盖
- 全面的单元测试
- API文档完整性
- 错误处理规范

## 6. 最佳实践
- 使用包管理器管理依赖
- 原子化Git提交
- 交互式开发工作流
- 持续集成验证
"""
