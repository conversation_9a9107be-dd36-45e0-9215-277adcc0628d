<!-- 
处理后的开发规则
生成时间: 2025-06-25 19:04:51
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年10月27日
- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发
- **主要改进**:
    - 统一并标准化了规则的分类和结构。
    - 明确了各层职责和依赖关系。
    - 细化了命名规范和文件组织原则。
    - 强调了测试、工程实践和Git提交的重要性。
    - 补充了数据库设计和模块内代码组织的关键约束。

## 2. 核心原则
- **架构设计原则**:
    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。
    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。
    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。
    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。
- **开发方法论**:
    - **测试驱动**: 任何新功能或修改必须伴随自动化测试，所有测试必须通过。
    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。
- **质量标准理念**:
    - **代码风格**: 严格遵循 PEP 8 规范。
    - **类型提示**: 强制要求所有函数签名和变量声明包含明确的类型提示。
    - **文档语言**: 所有注释、文档字符串和文档内容必须使用英文编写。
    - **代码纯粹性**: 领域层禁止出现任何外部框架（如 `fastapi`, `sqlalchemy`）的导入。

## 3. 技术规范
- **Web 框架**: FastAPI
- **数据验证与建模**: Pydantic
- **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)
- **测试框架**: Pytest
- **HTTP 测试**: FastAPI TestClient
- **Mocking**: unittest.mock 或 pytest-mock

## 4. 架构约束
- **项目整体结构**:
    - 根目录包含 `modules/` (业务模块), `common/` (通用代码), `tests/` (测试), `main.py` (主应用入口)。
    - `modules/` 下按业务功能（如 `auth`, `orders`）划分模块。
- **模块内部分层结构**:
    - `interfaces/`: 接口层，处理 HTTP 请求、数据转换、调用应用层服务。
    - `application/`: 应用层，编排领域逻辑、协调用例、定义事务边界。
    - `domain/`: 领域层，包含核心业务逻辑、实体、值对象、仓库抽象接口。
    - `infrastructure/`: 基础设施层，实现仓库接口、ORM 模型、处理技术细节。
- **核心依赖规则**:
    - **模块化**: 新功能应在 `modules/` 目录下创建新的业务模块或在现有模块内扩展。
    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。
    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。
    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入 `fastapi`, `sqlalchemy` 或其他外部框架。
    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。
    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可被任何层级导入，但通用代码本身不能反向依赖任何业务模块。
- **禁止的组织方式**:
    - 禁止使用类似 `_management` 的功能性命名后缀。
    - 禁止过度嵌套目录结构。

## 5. 代码质量标准
- **代码风格规范**:
    - 严格遵循 PEP 8 规范。
    - 强制使用类型提示。
- **测试要求**:
    - **测试伴随**: 任何新功能或修改必须有相应的测试用例覆盖。
    - **测试通过**: 所有测试用例必须通过。
    - **优先修复代码**: 测试失败时，优先修复生产代码。
    - **禁止随意修改测试**: 仅在需求变更或测试用例设计缺陷时才允许修改测试。
    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致。
    - **测试类型与分层**:
        - **单元测试**: 针对单一模块或函数，模拟外部依赖。
        - **接口/集成测试**: 使用 `TestClient` 测试端到端流程，与测试数据库交互。
    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用英文。
- **文档标准**:
    - 所有注释、文档字符串和文档内容必须使用英文编写。
    - API 文档要求：所有端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码同步更新。

## 6. 工程实践
- **开发流程**:
    - **Domain First**: 新功能开发从领域层开始。
    - **案例流程**: 遵循“创建/定位模块 -> Domain -> Application -> Infrastructure -> Interface -> 集成到主应用”的顺序。
- **版本控制**:
    - **Git 提交规范**: 遵循约定式提交（Conventional Commits）规范。
        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。
        - **`type`**: `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert`。
        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。
        - **`subject`**: 简短描述，祈使句，现在时态，英文，不超过 50 字符，首字母小写，末尾无句号。
        - **`body`**: 详细描述，英文，祈使句，现在时态。
        - **`footer`**: 用于重大更改 (`BREAKING CHANGE:`) 或关闭 Issue (`Closes #123`)。
    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。
- **部署规范**:
    - **数据库迁移**: 强制使用 Alembic 迁移脚本管理数据库结构变更，禁止应用启动时自动建表。
- **依赖管理**:
    - **依赖检查**: 添加新库前检查 `requirements.txt`。
    - **依赖同步**: 添加新依赖后立即执行 `pip freeze > requirements.txt`。
- **环境变量管理**:
    - **优先复用**: 优先复用 `.env` 中已有变量。
    - **命名规范**: 使用 `AI4SE_MCP_HUB_` 作为前缀，命名具有清晰业务含义。
    - **同步更新**: 更新 `.env.example`。
- **验证流程**:
    - 代码生成或修改后，必须提醒用户运行自动化测试套件，并以所有测试通过作为成功标志。
- **虚拟环境管理**:
    - 在执行终端命令前，提醒用户激活项目 `venv` 虚拟环境，并提供激活指令。

## 7. 最佳实践
- **数据库设计约束**:
    - **主键设计**: 所有实体主键强制使用 UUID 类型，包括领域模型、ORM 模型、API Schema 和路由参数。外键也必须使用 UUID。
    - **字段命名**: 强制要求字段名只反映业务含义，严禁包含技术实现细节（如 `id_uuid`, `name_str`）。
    - **表命名**: 使用复数形式的英文单词，下划线分隔，避免缩写。
    - **索引设计**: 主键自动创建唯一索引，外键和常用查询字段应创建索引。
- **模块内代码组织规范**:
    - **业务子域分离**: 当模块内包含多个相关但职责不同的业务子域时，通过文件命名前缀区分，而非深层嵌套目录。
    - **文件命名规范**:
        - **通用约束**: 所有模块内文件必须使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。
        - **格式**: `{业务子域}_{层级类型}.py`。
        - **示例**: `user_models.py`, `credential_auth_service.py`。
    - **业务子域识别原则**: 基于数据模型、业务流程、外部依赖或生命周期差异进行划分。
    - **跨业务子域依赖处理**:
        - **同模块内**: 业务子域间通过应用层服务接口相互依赖。
        - **跨模块**: 必须通过目标模块的应用层服务接口。
        - **共享组件**: 模块内共享组件放于 `shared/`，跨模块共享组件放于根目录 `common/` 或 `shared/`。

## 8. 约束与限制
- **禁止的做法**:
    - 禁止在领域层导入任何外部框架。
    - 禁止模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。
    - 禁止在应用启动时自动创建数据库表。
    - 禁止在数据库字段名中包含技术实现细节。
    - 禁止随意修改测试用例以使其通过。
    - 禁止使用通用文件名（如 `models.py`）和过度嵌套目录。
    - 禁止使用类似 `_management` 的功能性命名后缀。
- **必须遵守的规则**:
    - 严格遵循 PEP 8 和类型提示。
    - 所有文档和代码注释必须使用英文。
    - 所有数据库主键和外键必须使用 UUID。
    - 任何代码变更必须伴随自动化测试。
    - 遵循约定式提交规范。
- **异常处理原则**:
    - 重构前需分析上下文，重构后必须运行自动化测试。对于无测试覆盖部分，应询问用户是否补充测试用例。
    - 重构时优先创建新文件，完成修改后再删除旧文件。