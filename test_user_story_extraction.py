#!/usr/bin/env python3
import sys
import os
import json

sys.path.append('.')

# 读取实际的业务分析数据
llm_file = 'tools/ai_dev_agents/output/run_20250626_093512/llm_interactions/business_analyzer_llm_interactions.jsonl'
if os.path.exists(llm_file):
    with open(llm_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        # 获取最后一个响应
        business_content = ''
        for line in reversed(lines):
            data = json.loads(line)
            if data.get('interaction_type') == 'response':
                business_content = data.get('content', '')
                break
    
    # 测试用户故事提取
    from tools.ai_dev_agents.agents.requirements_generator import RequirementsGeneratorAgent
    agent = RequirementsGeneratorAgent(None, verbose=True)
    
    # 模拟业务分析数据结构
    business_analysis = {'content': business_content}
    
    user_stories = agent._extract_user_stories_from_business_analysis(business_analysis)
    print(f'提取到 {len(user_stories)} 个用户故事:')
    for story in user_stories:
        title = story['title'][:80] + '...' if len(story['title']) > 80 else story['title']
        print(f'- {story["id"]}: {title}')
else:
    print('LLM交互文件不存在')
