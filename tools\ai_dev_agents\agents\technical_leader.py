"""
Technical Leader Agent for quality review and improvement suggestions.
"""

import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext
from ..core.xml_schemas import XMLParser, UserStory


class TechnicalLeaderAgent(BaseAgent):
    """Agent responsible for reviewing user stories and providing quality feedback."""
    
    def __init__(self, llm_client, verbose: bool = False):
        """Initialize the technical leader agent."""
        super().__init__("technical_leader", llm_client, verbose=verbose)
        self.agent_name = "technical_leader"
        
        # Load system prompt for technical review
        self.system_prompt = self._load_system_prompt()
    
    def _load_system_prompt(self) -> str:
        """Load system prompt for technical leader."""
        return """你是一位资深的技术负责人，负责审核AI开发工作流中生成的用户故事质量。

你的职责包括：
1. 检查用户故事的完整性和质量
2. 验证是否符合INVEST原则（Independent, Negotiable, Valuable, Estimable, Small, Testable）
3. 确保用户故事与业务需求和技术架构的一致性
4. 提供具体的改进建议
5. 决定是否批准当前版本或需要重新生成

审核标准：
- 用户故事格式正确（作为...我希望...以便...）
- 验收标准清晰具体且可测试
- 优先级设置合理
- 领域上下文分组正确
- 技术可行性合理
- 业务价值明确

请以XML格式输出审核结果：

<quality_review>
    <approved>true/false</approved>
    <overall_score>1-10</overall_score>
    <summary>总体评价</summary>
    <detailed_feedback>
        <story_feedback id="US-001">
            <score>1-10</score>
            <issues>
                <issue>具体问题描述</issue>
            </issues>
            <suggestions>
                <suggestion>改进建议</suggestion>
            </suggestions>
        </story_feedback>
    </detailed_feedback>
    <improvement_suggestions>
        <suggestion priority="high">高优先级改进建议</suggestion>
        <suggestion priority="medium">中优先级改进建议</suggestion>
    </improvement_suggestions>
    <approval_conditions>
        <condition>批准条件1</condition>
        <condition>批准条件2</condition>
    </approval_conditions>
</quality_review>

请确保审核严格但建设性，提供具体可操作的改进建议。"""
    
    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process user stories quality review."""
        try:
            self.logger.info("Starting technical leader quality review")
            
            # Extract user stories from input
            user_stories = self._extract_user_stories(input_data)
            if not user_stories:
                return AgentResult(
                    success=False,
                    errors=["No user stories found in input data"],
                    data={}
                )
            
            self.logger.info(f"Reviewing {len(user_stories)} user stories")
            
            # Prepare review context
            review_context = self._prepare_review_context(input_data, context)
            
            # Generate review prompt
            review_prompt = self._generate_review_prompt(user_stories, review_context)
            
            # Get LLM review
            review_response = self._get_llm_response(review_prompt)
            if not review_response:
                return AgentResult(
                    success=False,
                    errors=["Failed to get LLM response for quality review"],
                    data={}
                )
            
            # Parse review result
            review_result = self._parse_review_result(review_response)
            
            # Save review to file
            if hasattr(context, 'output_path'):
                self._save_review_result(review_result, context.output_path)
            
            self.logger.info(f"Quality review completed. Approved: {review_result.get('approved', False)}")
            
            return AgentResult(
                success=True,
                data=review_result,
                metadata={
                    "reviewed_stories_count": len(user_stories),
                    "overall_score": review_result.get("overall_score", 0),
                    "approved": review_result.get("approved", False)
                }
            )
            
        except Exception as e:
            self.logger.error(f"Technical leader review failed: {e}")
            return AgentResult(
                success=False,
                errors=[str(e)],
                data={}
            )
    
    def _extract_user_stories(self, input_data: Dict[str, Any]) -> List[UserStory]:
        """Extract user stories from input data."""
        user_stories = []
        
        # Try to extract from XML format
        if "xml_content" in input_data:
            try:
                user_stories = XMLParser.extract_user_stories_from_xml(input_data["xml_content"])
            except Exception as e:
                self.logger.warning(f"Failed to parse XML user stories: {e}")
        
        # Try to extract from structured data
        if not user_stories and "user_stories" in input_data:
            stories_data = input_data["user_stories"]
            if isinstance(stories_data, list):
                for story_data in stories_data:
                    if isinstance(story_data, dict):
                        user_story = UserStory(
                            id=story_data.get("id", ""),
                            title=story_data.get("title", ""),
                            description=story_data.get("description", ""),
                            acceptance_criteria=story_data.get("acceptance_criteria", []),
                            priority=story_data.get("priority", "medium"),
                            domain_context=story_data.get("domain_context", "")
                        )
                        user_stories.append(user_story)
        
        # Try to extract from original_requirements (for regeneration scenarios)
        if not user_stories and "original_requirements" in input_data:
            original_req = input_data["original_requirements"]
            if "user_stories" in original_req:
                return self._extract_user_stories({"user_stories": original_req["user_stories"]})
        
        return user_stories
    
    def _prepare_review_context(self, input_data: Dict[str, Any], context: WorkflowContext) -> Dict[str, Any]:
        """Prepare context information for review."""
        review_context = {
            "project_rules": getattr(context, 'rules_content', ''),
            "business_requirements": input_data.get("business_analysis", {}),
            "domain_model": input_data.get("domain_model", {}),
            "improvement_suggestions": input_data.get("improvement_suggestions", []),
            "is_regeneration": "original_requirements" in input_data
        }
        
        return review_context
    
    def _generate_review_prompt(self, user_stories: List[UserStory], review_context: Dict[str, Any]) -> str:
        """Generate prompt for LLM review."""
        # Convert user stories to readable format
        stories_text = ""
        for story in user_stories:
            stories_text += f"""
用户故事 {story.id} (领域上下文: {story.domain_context}):
标题: {story.title}
描述: {story.description}
验收标准:
"""
            for i, criterion in enumerate(story.acceptance_criteria, 1):
                stories_text += f"  {i}. {criterion}\n"
            stories_text += f"优先级: {story.priority}\n\n"
        
        # Prepare context information
        context_info = ""
        if review_context.get("is_regeneration"):
            context_info += "注意：这是基于之前反馈重新生成的用户故事，请重点关注改进情况。\n\n"
        
        if review_context.get("improvement_suggestions"):
            context_info += "之前的改进建议：\n"
            for suggestion in review_context["improvement_suggestions"]:
                context_info += f"- {suggestion}\n"
            context_info += "\n"
        
        prompt = f"""请审核以下用户故事的质量：

{context_info}

用户故事列表：
{stories_text}

项目规则约束：
{review_context.get('project_rules', '无特殊规则')}

请按照系统提示中的XML格式输出详细的审核结果。"""
        
        return prompt
    
    def _parse_review_result(self, review_response: str) -> Dict[str, Any]:
        """Parse LLM review response."""
        # Extract XML from response
        xml_content = XMLParser.extract_xml_from_text(review_response)
        if not xml_content:
            # Fallback: try to parse the entire response as XML
            xml_content = review_response.strip()
        
        try:
            root = ET.fromstring(xml_content)
            
            # Parse basic fields
            approved_elem = root.find("approved")
            approved = approved_elem.text.lower() == "true" if approved_elem is not None else False
            
            score_elem = root.find("overall_score")
            overall_score = int(score_elem.text) if score_elem is not None and score_elem.text.isdigit() else 5
            
            summary_elem = root.find("summary")
            summary = summary_elem.text if summary_elem is not None else ""
            
            # Parse detailed feedback
            detailed_feedback = {}
            feedback_elem = root.find("detailed_feedback")
            if feedback_elem is not None:
                for story_feedback in feedback_elem.findall("story_feedback"):
                    story_id = story_feedback.get("id", "")
                    if story_id:
                        feedback_data = {
                            "score": 5,
                            "issues": [],
                            "suggestions": []
                        }
                        
                        score_elem = story_feedback.find("score")
                        if score_elem is not None and score_elem.text.isdigit():
                            feedback_data["score"] = int(score_elem.text)
                        
                        issues_elem = story_feedback.find("issues")
                        if issues_elem is not None:
                            for issue in issues_elem.findall("issue"):
                                if issue.text:
                                    feedback_data["issues"].append(issue.text)
                        
                        suggestions_elem = story_feedback.find("suggestions")
                        if suggestions_elem is not None:
                            for suggestion in suggestions_elem.findall("suggestion"):
                                if suggestion.text:
                                    feedback_data["suggestions"].append(suggestion.text)
                        
                        detailed_feedback[story_id] = feedback_data
            
            # Parse improvement suggestions
            improvement_suggestions = []
            suggestions_elem = root.find("improvement_suggestions")
            if suggestions_elem is not None:
                for suggestion in suggestions_elem.findall("suggestion"):
                    if suggestion.text:
                        priority = suggestion.get("priority", "medium")
                        improvement_suggestions.append({
                            "text": suggestion.text,
                            "priority": priority
                        })
            
            # Parse approval conditions
            approval_conditions = []
            conditions_elem = root.find("approval_conditions")
            if conditions_elem is not None:
                for condition in conditions_elem.findall("condition"):
                    if condition.text:
                        approval_conditions.append(condition.text)
            
            return {
                "approved": approved,
                "overall_score": overall_score,
                "summary": summary,
                "detailed_feedback": detailed_feedback,
                "improvement_suggestions": improvement_suggestions,
                "approval_conditions": approval_conditions,
                "review_timestamp": datetime.now().isoformat()
            }
            
        except ET.ParseError as e:
            self.logger.warning(f"Failed to parse XML review result: {e}")
            # Fallback: create a basic review result
            return {
                "approved": False,
                "overall_score": 3,
                "summary": "审核结果解析失败，需要人工检查",
                "detailed_feedback": {},
                "improvement_suggestions": [{"text": "请检查LLM输出格式", "priority": "high"}],
                "approval_conditions": ["修复输出格式问题"],
                "review_timestamp": datetime.now().isoformat(),
                "parse_error": str(e)
            }
    
    def _save_review_result(self, review_result: Dict[str, Any], output_path: Path):
        """Save review result to file."""
        try:
            review_file = output_path / "quality_review.json"
            review_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(review_file, 'w', encoding='utf-8') as f:
                json.dump(review_result, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Quality review saved to {review_file}")
        except Exception as e:
            self.logger.error(f"Failed to save quality review: {e}")
    
    def _get_llm_response(self, prompt: str) -> Optional[str]:
        """Get response from LLM."""
        try:
            if not self.llm:
                self.logger.warning("No LLM client available")
                return None
            
            from langchain.schema import HumanMessage, SystemMessage
            messages = [SystemMessage(content=self.system_prompt), HumanMessage(content=prompt)]
            
            response = self.llm.invoke(messages)
            
            return response.content
            
        except Exception as e:
            self.logger.error(f"LLM request failed: {e}")
            return None
