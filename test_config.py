#!/usr/bin/env python3
"""
Test configuration loading
"""

import sys
from pathlib import Path

# Add the parent directory to Python path for absolute imports
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from tools.ai_dev_agents.utils.config_manager import Config<PERSON><PERSON><PERSON>

def test_config():
    """Test configuration loading."""
    config_path = "tools/ai_dev_agents/config.yaml"
    print(f"Testing config path: {config_path}")
    print(f"Config file exists: {Path(config_path).exists()}")
    
    try:
        config_manager = ConfigManager(config_path=config_path)
        print(f"Config loaded successfully")
        print(f"Config keys: {list(config_manager.config.keys())}")
        
        if 'llm' in config_manager.config:
            llm_config = config_manager.config['llm']
            print(f"LLM config: {llm_config}")
        
        # Try to create LLM
        llm_client = config_manager.create_llm()
        print(f"LLM client created: {llm_client}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config()
