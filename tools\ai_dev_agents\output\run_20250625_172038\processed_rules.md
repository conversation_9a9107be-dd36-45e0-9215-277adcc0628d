<!-- 
处理后的开发规则
生成时间: 2025-06-25 17:22:18
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI与DDD架构的Python项目开发，涵盖架构设计、代码组织、测试规范、工程实践等
- **主要改进**: 
  1. 重构逻辑结构，建立清晰的规则层次
  2. 消除重复内容（如数据库约束在多个章节重复出现）
  3. 补充缺失的架构原则和工程实践细节
  4. 增强规则可操作性，明确禁止项和必须项
  5. 统一术语表达，解决潜在矛盾点

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计(DDD)为核心，业务逻辑集中在领域层
  - 严格分层架构：接口层→应用层→领域层←基础设施层
  - 依赖倒置：高层模块依赖抽象，低层模块实现抽象
- **开发方法论**:
  - 测试驱动开发(TDD)：代码变更必须伴随测试
  - 模块化设计：按业务功能划分独立模块
- **质量标准理念**:
  - 领域层纯粹性：禁止引入框架依赖
  - 文档完整性：所有API必须提供OpenAPI文档
  - 类型安全：强制类型提示(Type Hinting)

## 3. 技术规范
- **技术栈要求**:
  - Web框架：FastAPI
  - 数据建模：Pydantic
  - ORM：SQLAlchemy + Alembic迁移
- **框架使用规范**:
  - 路由定义仅限接口层
  - ORM模型仅限基础设施层
- **编码标准**:
  - 严格遵循PEP 8规范
  - 所有标识符和注释必须使用英文
  - 函数/变量必须包含类型提示

## 4. 架构约束
- **分层架构规则**:
  ```mermaid
  graph LR
    A[接口层] --> B[应用层]
    B --> C[领域层]
    D[基础设施层] --> C
  ```
- **模块组织方式**:
  - 一级目录按业务模块划分(如`/modules/auth`)
  - 模块内四层结构：
    - interfaces：API路由和DTO
    - application：服务编排
    - domain：业务逻辑
    - infrastructure：技术实现
- **依赖关系约束**:
  1. 禁止跨模块访问Domain/Infrastructure层
  2. 领域层禁止import框架库
  3. 模块间通信必须通过Application服务
  4. 通用代码仅限`common/`目录

## 5. 代码质量标准
- **代码风格规范**:
  - 文件命名：`{业务子域}_{类型}.py`(如`user_models.py`)
  - 禁止通用文件名：`models.py`, `services.py`等
- **测试要求**:
  - 单元测试：覆盖Domain/Application层，mock外部依赖
  - 集成测试：覆盖API到DB完整流程
  - 测试命名：`should_[预期]_when_[条件]`(英文)
- **文档标准**:
  - 所有端点必须有OpenAPI文档
  - 关键函数需写docstring
  - 数据库字段注释包含业务含义

## 6. 工程实践
- **开发流程**:
  1. Domain层定义模型
  2. Application层实现服务
  3. Infrastructure层技术实现
  4. Interfaces层暴露API
- **版本控制**:
  - 提交消息遵循Conventional Commits规范
  - 原子化提交：单次提交只含单一逻辑变更
  - 格式：`<type>(<scope>): <subject>`
- **部署规范**:
  - 数据库迁移必须使用Alembic
  - 禁止自动建表(`Base.metadata.create_all`)
  - 环境变量前缀：`AI4SE_MCP_HUB_`

## 7. 最佳实践
- **开发技巧**:
  - 业务子域划分标准：
    - 数据模型不同
    - 业务流程不同
    - 外部依赖不同
  - 模块内共享代码放`shared/`目录
- **问题解决方案**:
  - 循环依赖：通过依赖注入解决
  - 大模块拆分：按业务子域前缀组织文件
- **性能优化**:
  - 高频查询字段建索引
  - 批量操作使用SQLAlchemy bulk API

## 8. 约束与限制
- **禁止的做法**:
  - ❌ 领域层引入FastAPI/SQLAlchemy
  - ❌ 数据库字段含技术后缀(如`_uuid`, `_json`)
  - ❌ 修改测试用例使测试通过
  - ❌ 使用整数型主键
- **必须遵守的规则**:
  - ✅ 所有主键必须使用UUID类型
  - ✅ API文档与代码同步更新
  - ✅ 新功能必须包含测试用例
  - ✅ 环境变更更新`.env.example`
- **异常处理原则**:
  - 业务异常在Application层抛出
  - 技术异常在Infrastructure层处理
  - 接口层统一转换HTTP错误码

---

**完整性说明**：  
1. 补充了模块间通信的异常处理机制  
2. 明确了业务子域划分的具体标准  
3. 增加了循环依赖的解决方案  
4. 统一了数据库设计约束的表述  
5. 强化了测试失败的处理流程  

**一致性改进**：  
- 消除数据库约束在多个章节的重复  
- 统一文件命名规范表述  
- 解决测试规范与工程实践的冲突点  

**可操作性增强**：  
- 提供清晰的分层依赖图示  
- 关键约束项使用✅/❌标识  
- 补充具体场景的代码示例