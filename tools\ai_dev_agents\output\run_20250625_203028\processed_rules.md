<!-- 
处理后的开发规则
生成时间: 2025-06-25 20:31:07
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发
- **主要改进**: 
  - 重新组织了规则结构，使其更加系统化
  - 消除了重复内容，特别是关于架构分层和模块组织的部分
  - 补充了测试规范和工程实践的细节
  - 增强了规则的可操作性和一致性

## 2. 核心原则
- **技术栈原则**:
  - 使用FastAPI作为Web框架
  - 采用Pydantic进行数据验证
  - 使用SQLAlchemy和Alembic进行数据库操作和迁移
- **开发方法论**:
  - 严格遵循领域驱动设计(DDD)原则
  - 采用分层架构，明确各层职责
- **质量标准**:
  - 强制类型提示(Type Hinting)
  - 严格遵循PEP 8代码风格
  - 所有文档和注释使用英文

## 3. 技术规范
- **语言要求**:
  - Python 3.8+
  - 强制类型提示
- **框架规范**:
  - FastAPI路由定义规范
  - SQLAlchemy ORM使用规范
- **工具链**:
  - Pytest测试框架
  - Alembic数据库迁移工具
  - Git版本控制

## 4. 架构约束
### 4.1 项目结构
```
.
├── modules/          # 业务模块
│   ├── auth/         # 认证模块
│   └── orders/       # 订单模块
├── common/           # 通用代码
├── tests/            # 测试代码
└── main.py           # 应用入口
```

### 4.2 分层架构
1. **接口层(Interfaces)**:
   - 处理HTTP请求和响应
   - 包含FastAPI路由和Pydantic模型
2. **应用层(Application)**:
   - 编排业务用例
   - 定义服务类和DTO
3. **领域层(Domain)**:
   - 核心业务逻辑
   - 包含实体、值对象和仓库接口
4. **基础设施层(Infrastructure)**:
   - 技术实现细节
   - 包含仓库实现和ORM模型

### 4.3 依赖规则
1. 严格单向依赖: Interfaces → Application → Domain
2. 领域层禁止依赖外部框架
3. 模块间通信必须通过应用层服务

## 5. 代码质量标准
### 5.1 代码风格
- 严格遵循PEP 8规范
- 所有函数和变量必须有类型提示
- 文档字符串使用英文

### 5.2 测试要求
- **测试覆盖率**:
  - 核心业务逻辑100%覆盖
  - 关键路径90%以上覆盖
- **测试分层**:
  - 单元测试: 测试独立单元
  - 集成测试: 测试模块集成
  - E2E测试: 测试完整流程

### 5.3 文档标准
- 所有API必须有OpenAPI文档
- 复杂逻辑必须有详细注释
- 重要设计决策必须有文档记录

## 6. 工程实践
### 6.1 开发流程
1. 领域驱动设计优先
2. 测试驱动开发(TDD)
3. 持续集成

### 6.2 版本控制
- 使用Git进行版本管理
- 遵循约定式提交规范
- 分支策略:
  - main: 生产环境
  - develop: 集成分支
  - feature/*: 功能开发

### 6.3 环境管理
- 使用虚拟环境隔离依赖
- 环境变量统一管理
- 依赖版本固化

## 7. 最佳实践
### 7.1 开发技巧
- 小步提交，频繁集成
- 代码审查前自检
- 及时更新文档

### 7.2 问题解决
- 优先修复生产代码
- 禁止为通过测试而修改测试
- 复杂问题先写测试再修复

### 7.3 性能优化
- 数据库查询优化
- 缓存策略
- 异步处理

## 8. 约束与限制
### 8.1 严格禁止
- 领域层引入框架依赖
- 直接跨模块访问领域层
- 使用通用文件名(如models.py)

### 8.2 必须遵守
- UUID作为主键类型
- 业务语义命名规范
- 原子化提交原则

### 8.3 异常处理
- 统一错误处理机制
- 业务异常与系统异常分离
- 详细的错误日志

## 质量要求
1. **完整性**: 覆盖从架构设计到代码提交的全流程
2. **一致性**: 各层规范相互协调，无冲突
3. **可操作性**: 每条规则都有明确的执行标准
4. **可维护性**: 模块化组织，便于更新
5. **专业性**: 使用准确的DDD和Python技术术语