#!/usr/bin/env python3
"""
Final test to verify the JSON conversion fix
"""

import tempfile
from pathlib import Path

def test_workflow_with_simple_content():
    """Test the actual workflow with very simple content"""
    
    # Create simple test content
    simple_prd = """
# Simple Test Project

## Overview
A basic user management system for testing.

## Features
- User registration
- User login
- Profile management

## Users
- Regular users can register and login
- <PERSON><PERSON> can manage users

## Requirements
- FR-001: User registration
- FR-002: User login
- FR-003: Profile editing
"""

    simple_rules = """
# Development Rules

## Tech Stack
- FastAPI framework
- Pydantic for validation
- SQLAlchemy ORM

## Architecture
- Follow DDD principles
- Use UUID for primary keys
- Domain layer should be pure

## Quality
- Include type hints
- Follow PEP 8
- Write tests
"""

    # Create temporary files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        prd_file = temp_path / "test_prd.txt"
        rules_file = temp_path / "test_rules.md"
        
        # Write files with explicit UTF-8 encoding
        prd_file.write_text(simple_prd, encoding='utf-8')
        rules_file.write_text(simple_rules, encoding='utf-8')
        
        print(f"Created test files:")
        print(f"  PRD: {prd_file} ({prd_file.stat().st_size} bytes)")
        print(f"  Rules: {rules_file} ({rules_file.stat().st_size} bytes)")
        
        # Test the workflow command
        import subprocess
        import sys
        
        cmd = [
            sys.executable, "main.py", "workflow", 
            str(prd_file), "--rules", str(rules_file),
            "--output", str(temp_path / "output")
        ]
        
        print(f"\nRunning command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=120,  # 2 minutes timeout
                cwd=Path(__file__).parent
            )
            
            print(f"Return code: {result.returncode}")
            print(f"STDOUT:\n{result.stdout}")
            if result.stderr:
                print(f"STDERR:\n{result.stderr}")
            
            # Check if output was created
            output_dir = temp_path / "output"
            if output_dir.exists():
                print(f"\nOutput directory created: {output_dir}")
                for file in output_dir.rglob("*"):
                    if file.is_file():
                        print(f"  {file.name}: {file.stat().st_size} bytes")
                        
                        # If it's a markdown file, show first few lines
                        if file.suffix == '.md':
                            try:
                                content = file.read_text(encoding='utf-8')
                                lines = content.split('\n')[:5]
                                print(f"    Preview: {lines[0][:50]}...")
                            except:
                                pass
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("Command timed out after 2 minutes")
            return False
        except Exception as e:
            print(f"Error running command: {e}")
            return False

def test_json_parsing_only():
    """Quick test of just the JSON parsing logic"""
    
    print("=== Testing JSON Parsing Logic ===")
    
    # Test the actual DomainModeler JSON processing
    test_json_response = '''
    Based on the analysis, here is the domain model:
    
    ```json
    {
        "content_type": "domain_model",
        "concept_analysis": {
            "similar_concepts": [],
            "modeling_decisions": []
        },
        "bounded_contexts": [
            {
                "name": "用户管理上下文",
                "description": "负责用户账户和权限管理"
            }
        ],
        "aggregates": [
            {
                "name": "用户聚合",
                "aggregate_root": "User",
                "entities": ["User"]
            }
        ],
        "domain_entities": [
            {
                "name": "User",
                "attributes": [
                    {"name": "id", "type": "UUID"},
                    {"name": "username", "type": "String"}
                ]
            }
        ],
        "value_objects": [],
        "domain_services": [],
        "repositories": [],
        "domain_events": []
    }
    ```
    
    This completes the domain modeling.
    '''
    
    # Import and test the actual method
    import sys
    import json
    import re
    
    def process_json_response(response: str):
        """Copy of the actual JSON processing logic"""
        if not response or not response.strip():
            return {}

        # Try multiple strategies to extract JSON
        json_content = None
        
        # Strategy 1: Look for JSON code blocks
        json_matches = re.findall(r'```(?:json)?\s*(.*?)\s*```', response, re.DOTALL)
        if json_matches:
            json_content = json_matches[0].strip()
        
        # Strategy 2: Look for content between { and } (outermost braces)
        if not json_content:
            brace_match = re.search(r'\{.*\}', response, re.DOTALL)
            if brace_match:
                json_content = brace_match.group(0).strip()
        
        # Strategy 3: Use entire response as fallback
        if not json_content:
            json_content = response.strip()

        try:
            # Parse JSON content
            parsed_data = json.loads(json_content)
            if isinstance(parsed_data, dict):
                if "content_type" not in parsed_data:
                    parsed_data["content_type"] = "domain_model"
                return parsed_data
            else:
                return {"content_type": "domain_model", "parsing_error": "Non-dict result"}
        except json.JSONDecodeError as e:
            return {"content_type": "domain_model", "parsing_error": str(e)}
    
    result = process_json_response(test_json_response)
    
    print(f"JSON parsing result:")
    print(f"  Success: {'parsing_error' not in result}")
    print(f"  Content type: {result.get('content_type')}")
    print(f"  Bounded contexts: {len(result.get('bounded_contexts', []))}")
    print(f"  Domain entities: {len(result.get('domain_entities', []))}")
    
    if 'parsing_error' in result:
        print(f"  Error: {result['parsing_error']}")
    
    return 'parsing_error' not in result

if __name__ == "__main__":
    print("Final Testing of JSON Conversion Fix")
    print("=" * 50)
    
    # Test 1: JSON parsing logic
    json_test_passed = test_json_parsing_only()
    
    print("\n" + "=" * 50)
    
    # Test 2: Full workflow with simple content
    print("=== Testing Full Workflow ===")
    workflow_test_passed = test_workflow_with_simple_content()
    
    print("\n" + "=" * 50)
    print("Final Test Results:")
    print(f"  JSON Parsing: {'✅ PASS' if json_test_passed else '❌ FAIL'}")
    print(f"  Full Workflow: {'✅ PASS' if workflow_test_passed else '❌ FAIL'}")
    
    if json_test_passed and workflow_test_passed:
        print("\n🎉 All tests passed! The JSON conversion fix is working correctly.")
        print("You can now run the full workflow with confidence.")
    elif json_test_passed:
        print("\n⚠️  JSON parsing works, but workflow has issues. Check the output above.")
    else:
        print("\n❌ JSON parsing failed. The fix needs more work.")
