{"timestamp": "2025-06-25T22:30:46.653427", "agent_name": "business_analyzer", "interaction_id": 1, "task_description": "业务需求分析", "interaction_type": "request", "content": [{"role": "system", "content": "**重要：请务必使用中文回答所有问题，所有输出内容必须是中文。**\n\n你是一位资深的业务分析专家，拥有丰富的产品需求分析经验，特别擅长分析AI和协议相关的技术产品。你的任务是对PRD文档进行深度、全面的业务分析。\n\n## 特别关注：MCP (Model Context Protocol) 生态系统\n如果PRD涉及MCP相关内容，请特别关注以下核心概念：\n- **MCP服务器**: 提供特定功能的轻量级程序，通过标准化协议暴露功能\n- **MCP客户端**: 与MCP服务器连接的协议客户端\n- **MCP宿主**: 需要通过MCP访问数据的应用程序（如Claude Desktop、IDE等）\n- **资源**: 客户端可以读取的文件式数据（API响应、文件内容等）\n- **工具**: 经用户批准后可由LLM调用的函数\n- **提示**: 帮助用户完成特定任务的预编写模板\n- **服务器发现**: 用户查找、评估和集成MCP服务器的过程\n- **质量指标**: 如Asecurity、Alicense、Aquality等安全性和质量评估\n- **开发者生态**: 服务器提交、审核、社区反馈等协作机制\n\n## 分析目标\n深度理解业务需求，提取结构化信息，识别隐含的业务逻辑和约束，为后续的系统设计提供坚实的业务基础。特别要识别和分析MCP生态系统中的关键业务实体和流程。\n\n## 分析维度\n1. **业务概览分析** - 理解项目背景、目标和价值主张\n2. **核心业务实体识别** - 识别关键的业务对象和它们的属性\n3. **功能需求梳理** - 提取和分类功能性需求\n4. **非功能需求识别** - 识别性能、安全、可用性等要求\n5. **业务规则提取** - 识别业务约束和规则\n6. **用户故事构建** - 从用户角度描述功能需求\n7. **业务流程分析** - 理解关键业务流程和交互\n\n## 输出要求\n请以Markdown格式输出分析结果，包含以下结构：\n\n# 业务需求分析报告\n\n## 1. 业务概览\n- **项目名称**: [项目名称]\n- **项目描述**: [项目的详细描述]\n- **核心目标**: [主要目标列表]\n- **目标用户**: [用户群体描述]\n- **价值主张**: [项目带来的业务价值]\n\n## 2. 核心实体分析\n### 实体1: [实体名称]\n- **描述**: [实体的业务含义和作用]\n- **主要属性**: [关键属性列表]\n- **业务规则**: [相关的业务约束和规则]\n- **关系**: [与其他实体的关系]\n- **MCP相关性**: [如果是MCP相关实体，说明其在MCP生态中的作用]\n\n### 实体2: [实体名称]\n[重复上述结构]\n\n### 特别关注的MCP核心实体（如适用）\n- **MCP服务器**: 服务器的元数据、功能、版本管理等\n- **工具定义**: 服务器提供的具体函数和接口\n- **用户角色**: 开发者、最终用户、管理员等不同角色\n- **质量评估**: 安全性、许可证、质量等评估体系\n- **社区互动**: 评论、评分、反馈等社区功能\n\n## 3. 功能需求\n### 高优先级需求\n- **FR-001**: [需求标题] - [需求详细描述]\n  - 验收标准: [验收标准列表]\n  - 功能分类: [分类]\n\n- **FR-002**: [需求标题] - [需求详细描述]\n  - 验收标准: [验收标准列表]\n  - 功能分类: [分类]\n\n### 中优先级需求\n- **FR-003**: [需求标题] - [需求详细描述]\n\n## 4. 非功能需求\n### 性能要求\n- **NFR-002**: [安全要求]\n- **NFR-003**: [可用性要求]\n\n### 安全要求\n- **NFR-004**: [安全相关要求]\n\n### 可扩展性要求\n- **NFR-005**: [扩展性相关要求]\n\n## 5. 业务规则\n1. **BR-001**: [业务规则描述]\n   - 触发条件: [条件描述]\n   - 执行动作: [动作描述]\n\n2. **BR-002**: [业务规则描述]\n   - 触发条件: [条件描述]\n   - 执行动作: [动作描述]\n\n## 6. 用户故事\n### 核心用户故事\n- **US-001**: 作为[用户角色]，我希望[功能描述]，以便[业务价值]\n  - 验收标准: [验收标准列表]\n\n- **US-002**: 作为[用户角色]，我希望[功能描述]，以便[业务价值]\n  - 验收标准: [验收标准列表]\n\n## 7. 关键业务流程\n### 流程1: [流程名称]\n- **描述**: [流程描述]\n- **参与者**: [参与者列表]\n- **主要步骤**:\n  1. [步骤1]\n  2. [步骤2]\n  3. [步骤3]\n- **输入**: [输入内容]\n- **输出**: [输出内容]\n\n### 流程2: [流程名称]\n[重复上述结构]\n\n## 分析原则\n- 保持客观和准确，基于文档内容进行分析\n- 识别隐含的业务逻辑和约束\n- 确保分析的完整性和一致性\n- 为每个需求提供清晰的验收标准\n- 考虑不同用户角色的需求和场景\n- **特别重要**: 如果涉及MCP，必须深入理解MCP协议的技术特性和生态系统需求\n- 识别MCP服务器的生命周期管理需求（提交、审核、发布、维护）\n- 分析MCP工具和资源的发现、集成和使用流程\n- 考虑MCP生态系统中的信任、安全和质量保证需求\n\n请对以下PRD文档进行分析：\n"}, {"role": "user", "content": "\n请分析以下PRD文档并提取业务需求信息：\n\n=== 文档内容 ===\n\n\n# **构建 MCP 服务器市场功能报告**\n\n## **1\\. 执行摘要**\n\n本报告旨在为开发一个 Model Context Protocol (MCP) 服务器市场平台提供详细的功能需求说明书、领域驱动设计（DDD）模型以及敏捷用户故事。该平台的目标是促进 MCP 服务器的发现、评估和集成，并借鉴现有平台（如 Glama.ai）的能力，同时加以增强。\n\n分析发现，MCP 生态系统是一个复杂的互联网络，需要强大的搜索、过滤、详细的服务器信息展示以及社区贡献功能。核心的 MCP 服务器 实体具有丰富的属性，这使得采用成熟的领域驱动设计方法变得至关重要。\n\n基于这些发现，建议采用分阶段的敏捷开发方法，利用详细的用户故事指导 AI Agent 进行代码生成。初期应重点关注核心服务器列表和发现功能，随后再逐步引入服务器提交和社区互动功能。\n\n## **2\\. Model Context Protocol (MCP) 生态系统介绍**\n\n### **2.1. MCP 的目的与核心概念**\n\nModel Context Protocol (MCP) 是一种开放协议，旨在标准化应用程序如何向大型语言模型 (LLM) 提供上下文信息 1。它类似于 AI 应用程序的 USB-C 端口，提供了一种标准化的方法来连接 AI 模型与各种数据源和工具。\n\nMCP 的核心目的是促进在 LLM 之上构建 AI 代理和复杂工作流。它解决了 LLM 频繁需要与数据和工具集成的问题，通过提供：不断增长的预构建集成列表，LLM 可以直接插入；在不同 LLM 提供商和供应商之间切换的灵活性；以及在现有基础设施内保护数据的最佳实践 1。\n\n这种协议的出现，反映了 LLM 越来越需要超越其静态训练数据，与实时、动态的信息和外部系统进行交互。MCP 旨在解决 LLM 的“上下文问题”，使其能够访问和利用外部数据与工具来执行更复杂的任务。这种标准化促进了一个生态系统的形成，其中各种应用程序和数据源可以轻松地“插入”并增强 LLM 的能力，从而加速了复杂 AI 代理和工作流的开发。它还有助于减少 LLM 供应商的锁定效应，并促进互操作性。\n\n### **2.2. 关键组件与通用架构**\n\nMCP 的核心在于其客户端-服务器架构，允许一个宿主应用程序连接到多个服务器 1。该架构的关键组件包括：\n\n* **MCP 宿主 (MCP Hosts)**：这些是需要通过 MCP 访问数据的程序或应用程序，例如 Claude Desktop、集成开发环境 (IDE) 或其他 AI 工具 1。  \n* **MCP 客户端 (MCP Clients)**：这些是协议客户端，与 MCP 服务器保持一对一连接 1。  \n* **MCP 服务器 (MCP Servers)**：这些是轻量级程序，每个程序都旨在通过标准化的 Model Context Protocol 暴露特定功能 1。  \n  * MCP 服务器提供的功能主要分为三类：**资源**（客户端可以读取的文件式数据，如 API 响应或文件内容）、**工具**（经用户批准后可由 LLM 调用的函数）和**提示**（旨在帮助用户完成特定任务的预编写模板） 2。例如，一个天气服务器可以暴露  \n    get-alerts（获取美国某个州的天气警报）和 get-forecast（获取特定经纬度的天气预报）这两个工具 2。  \n  * 在开发层面，MCP 服务器支持**自动工具定义**。使用 Python 的 FastMCP 等 SDK，工具定义可以从 Python 类型提示和文档字符串中自动生成，从而简化了 MCP 工具的创建和维护。同样，Java 的 Spring AI 中的 @Tool 注解可以自动将服务注册为工具 2。  \n  * MCP 服务器与客户端之间的**通信传输**主要使用 stdio（标准输入/输出）2。  \n  * **集成** MCP 服务器通常通过将服务器的命令和参数添加到客户端的配置文件（例如 claude\\_desktop\\_config.json）中来完成 2。  \n  * **调试**对于 MCP 服务器的连接和故障排除至关重要，Claude Desktop 会将 MCP 相关的日志写入 mcp.log 和 mcp-server-SERVERNAME.log 文件 2。  \n* **本地数据源 (Local Data Sources)**：指用户计算机上存在的数据，包括文件、数据库和服务，MCP 服务器可以安全地访问这些数据 1。  \n* **远程服务 (Remote Services)**：指通过互联网可访问的外部系统，通常通过 API，MCP 服务器可以连接到这些系统 1。\n\n这种 MCP 生态系统本质上是分布式的，每个服务器都提供特定的功能。为了使这个生态系统蓬勃发展，用户（包括开发者、AI 代理和最终用户）必须有一种高效的方式来发现可用的服务器，了解它们的功能，并学习如何集成它们。MCP 服务器市场网站正是扮演了这一角色，它充当了分散式 MCP 服务器网络的中央注册和发现门户。如果没有这样一个平台，服务器的发现能力将受到严重限制，从而阻碍 MCP 的采用和实用性。这表明，该市场不仅仅是一个静态列表，它是一个连接到不断发展和演变的协议的动态接口。\n\n### **2.3. MCP 服务器市场概念概述**\n\nMCP 服务器市场是一个集中式目录，用于展示“开源 MCP 服务器”，这些服务器通过文件访问、数据库连接、API 集成以及其他上下文服务来扩展 AI 能力 3。它为用户提供了一个平台，以查找、评估并可能安装或集成这些服务器。\n\n鉴于 MCP 服务器可以访问本地数据和远程服务，并且旨在集成到关键的 AI 工作流中，信任和质量变得至关重要。市场平台通过提供“质量/安全/许可证指标”（例如“Asecurity”、“Alicense”、“Aquality”）4 和反馈机制（例如“这有用吗？是/否”按钮）3，隐含地承担了对这些服务器进行策展和验证的角色。这表明该平台不仅仅是一个列出服务，更是一个把关者和信任建立者，这对于开放协议的广泛采用至关重要，因为在开放协议中任何人都可以贡献。此功能对于降低将第三方代码集成到敏感 AI 应用程序中的风险至关重要。\n\n## **3\\. 现有 MCP 服务器市场 (Glama.ai) 功能分析**\n\n### **3.1. 首页功能与用户体验**\n\n尽管提供的资料没有直接详细说明 Glama.ai 首页的具体功能或显示方式，但其服务器列表页面的存在 3 表明，首页很可能作为核心功能的明确入口点。首页可能主要用于引导用户进入服务器列表，并可能突出显示特色服务器、最新更新或关键类别。\n\n### **3.2. MCP 服务器列表与发现**\n\n#### **3.2.1. 显示元素与信息**\n\n服务器列表页面显示了“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）以及“上次更新”的时间戳 3。每个服务器条目都包含丰富的详细信息，以帮助用户快速了解其特性：\n\n* **服务器名称**：通常指示其主要功能，并作为可点击的链接 5。  \n* **官方标签**：许多条目标有“官方”标签，表明它们由相关组织正式支持或开发 5。  \n* **开发者/组织**：开发或维护服务器的实体名称，也是可点击的链接 5。  \n* **质量/安全/许可证指标**：每个服务器都有“Asecurity”、“Alicense”和“Aquality”的指标，提供有关服务器安全性、许可和整体质量的信息 5。  \n* **描述**：简要说明 MCP 服务器的功能以及它提供的数据或功能类型 5。  \n* **上次更新**：指示服务器上次更新的时间戳 5。  \n* **编程语言**：指定服务器所使用的编程语言，例如“TypeScript”、“Python”5。  \n* **许可证类型**：服务器发布的软件许可证类型，例如“MIT 许可证”、“Apache 2.0”5。  \n* **操作系统兼容性**：显示与不同操作系统兼容的图标或文本，例如“Apple”和“Linux”5。\n\n每个服务器条目中包含的丰富元数据（如语言、许可证、操作系统兼容性、质量指标）远不止简单的命名。这些广泛的元数据对于用户快速评估服务器的相关性、可信度和技术适用性至关重要，而无需点击进入每个详细页面。对于生成代码的 AI Agent 而言，这种结构化的元数据在根据预定义条件（例如，“查找具有 MIT 许可证且支持 Linux 并具有 Asecurity 评级的 Python 服务器”）过滤和选择适当服务器时具有不可估量的价值。这表明，市场的价值与其服务器元数据的完整性和准确性密切相关。\n\n#### **3.2.2. 筛选、排序与分类机制**\n\n服务器列表页面提供了多种排序选项，包括：搜索相关性（默认排序选项）、添加日期、更新日期、每周下载量、GitHub 星数以及最近的 GitHub 星数 3。\n\n此外，页面还列出了众多类别，每个类别旁边都显示有计数，这些类别可作为过滤选项。例如，包括 Python (2,436)、远程 (2,425)、TypeScript (1,988)、开发工具 (1,703)、数据库 (715)、RAG 系统 (637)、代码执行 (577)、代理编排 (549)、自治代理 (546)、官方 (454)、网页抓取 (304)、云平台 (298)、通信 (250)、浏览器自动化 (241)、内容管理系统 (230)、项目管理 (216)、知识与记忆 (213)、金融 (208)、文档访问 (207)、文件系统 (206)、区块链 (200)、图像与视频处理 (167)、API 测试 (163)、代码分析 (158)、安全 (155) 等 3。\n\n这些类别被组织成导航菜单，并有特定的类别页面（例如 /categories/search、/categories/databases）显示与该类别相关的服务器 6。在特定类别内，服务器以连续可滚动的列表形式呈现，没有进一步的子分类或过滤选项 6。\n\n这种广泛的类别列表（超过 30 个）表明存在一个高度细粒度的分类系统。虽然这允许进行精确过滤，但它也给用户导航和发现带来了挑战。平台必须在为特定需求（例如“区块链”、“RAG 系统”）提供足够详细信息与确保用户不会感到信息过载之间取得平衡。这可能需要分层分类或强大的标签系统，以允许进行广泛和狭窄的搜索。类别中较高的计数也意味着服务器数量庞大，这进一步强调了高效过滤的必要性。\n\n#### **3.2.3. 搜索功能与结果呈现**\n\n页面上提供了“深度搜索”和“新搜索”按钮，这表明除了简单的关键词匹配之外，还存在高级搜索功能 3。搜索结果页面（例如\n\n/categories/search）显示了“用于搜索的 MCP 服务器”列表，每个条目都包含标准的服务器详细信息 5。\n\n该页面隐含的搜索功能是“使用自然语言访问和查询各种来源的数据”的能力 5。搜索类别中服务器的描述突出了其多样化的功能，例如：按维度过滤和检索指标（Clarity Data Export MCP Server）、实时公共网络数据访问（Bright Data MCP）、特定领域搜索（Tavily MCP Server）、SEO 数据访问（DataForSEO MCP Server）、区块链数据查询（Satstream MCP Server）、经济数据访问（MCP-FREDAPI）等 5。\n\n该平台提供了以人为中心的搜索和过滤功能（关键词、类别、排序），但同时也列出了其主要目的在于使 AI Agent 执行自然语言搜索的服务器。这形成了一种有趣的二元性：市场平台本身可供人类搜索，而市场上的产品（MCP 服务器）则使 AI 能够搜索其他数据。这意味着平台自身的搜索功能必须足够强大，以允许用户找到“正确的 AI 搜索工具”，从而使元搜索功能成为一项关键特性。这也表明，该平台最终可能会为其自身的服务器目录集成 AI 驱动的搜索功能。\n\n### **3.3. 单个 MCP 服务器详情视图**\n\n单个服务器的详细页面（例如 ChainGPT MCP）提供了服务器的全面概述，包括其目的、功能、设置说明、安装方法、故障排除提示、可用工具和相关资源 4。\n\n页面上突出显示的信息包括：\n\n* **服务器标识**：服务器标题（“ChainGPT MCP”）和作者（“by kohasummons”）4。  \n* **元数据**：与服务器相关的标签/关键词（GitHub、Blockchain、Search、Cryptocurrency、TypeScript）、许可证（MIT 许可证）以及兼容性/支持平台（Apple）4。  \n* **导航链接**：提供了一组内部导航链接，可快速访问页面的不同部分：概述、InspectNew、Schema、相关服务器、评论、评分和需要帮助？4。  \n* **开发链接**：提供“查看源代码”（GitHub）和“报告问题”的链接 4。  \n* **服务器功能**（“您可以使用此服务器做什么？”）：详细说明了 ChainGPT MCP 服务器的核心功能，例如与 ChainGPT AI 交互、访问加密新闻、跟踪加密市场、分析区块链数据以及检索聊天历史记录 4。  \n* **可用集成**（“此服务器有哪些集成可用？”）：概述了技术要求和集成点，例如支持 macOS 系统上的配置以与 Claude Desktop 应用程序集成，需要 Node.js 环境 (v18 或更高版本)，并支持使用 npm/npx 命令进行安装和执行 4。  \n* **设置**：设置服务器的先决条件，例如获取 ChainGPT 密钥、Node.js 环境和兼容的 MCP 客户端 4。  \n* **安装**：详细的安装说明，包括通过 Smithery 或手动安装的命令行指令，以及配置 Claude Desktop 以识别 ChainGPT MCP 服务器的步骤和 JSON 配置片段 4。  \n* **故障排除**：专门用于常见问题及其解决方案的部分，例如服务器未找到、API 密钥问题、连接问题、Node.js 版本要求和工具调用超时 4。  \n* **工具**：一个表格列出了可用的工具，包括它们的名称、描述和示例提示，例如 chaingpt\\_invoke\\_chat 和 chaingpt\\_get\\_news 4。  \n* **贡献**：一个鼓励拉取请求和问题讨论的标准部分 4。  \n* **质量指标**：显示“Asecurity – 无已知漏洞”、“Alicense – 宽松许可证”和“Aquality – 确认可用”，并提供指向其计算方法的链接 4。  \n* **技术细节**：指示服务器是否“远程可用”，并提供 HTTP 连接 URL 4。  \n* **相关资源**：指向外部讨论的链接，例如“关于此服务器的 Reddit 讨论”4。  \n* **相关 MCP 服务器**：列出其他相关的 MCP 服务器，包括其作者、安全、许可证、质量、描述、上次更新日期、技术和兼容平台 4。  \n* **新 MCP 服务器**：列出最近添加的 MCP 服务器 4。  \n* **MCP 目录 API**：提供通过 MCP API 访问服务器信息的信息，包括 curl 命令示例和 Discord 服务器链接 4。  \n* **用户反馈**：“这有用吗？是/否”按钮 4。  \n* **行动呼吁**：“安装服务器”按钮 4。\n\n需要注意的是，inspect、schema 和 related-servers 子页面无法访问 7。然而，与模式（通过工具）和相关服务器相关的信息已在主详细页面上呈现。\n\n服务器详细页面（4）上信息的数量和特异性表明，它不仅是一个产品页面，而且是每个服务器的迷你文档门户。这对于需要精确集成、设置和故障排除说明的开发人员和 AI Agent 至关重要。包含工具的示例提示对于 LLM 和 AI Agent 尤其有价值。这意味着平台必须支持富文本编辑、代码片段和结构化数据输入，以供服务器提交，从而本质上成为一个开发人员文档的内容管理系统。\n\n### **3.4. 用户交互与贡献功能**\n\n服务器列表页面上存在“添加服务器”按钮 3，以及服务器详细页面上的“报告问题”和“贡献”部分 4，都表明平台鼓励用户参与。此外，在列表和详细页面上都提供了“这有用吗？是/否”的反馈机制 3。为了支持社区，平台还提供了指向 Reddit 和 Discord 社区的社交媒体链接，并支持新闻通讯注册 3。\n\n“添加服务器”、“报告问题”、“贡献”和反馈机制的存在，表明平台旨在构建一个社区驱动的生态系统。对于像 MCP 这样的开放协议，社区贡献对于其增长和多样化至关重要。市场不仅需要促进发现，还需要积极鼓励和简化通过用户参与进行服务器提交、维护和改进的过程。这意味着需要强大的用户身份验证、内容审核，以及可能超越简单有用性反馈的评论/评分系统。\n\n### **3.5. MCP 服务器关键属性与能力总结**\n\n下表总结了从现有平台分析中识别出的 MCP 服务器的关键属性和能力。这张表格为开发人员和 AI Agent 提供了一个简洁的参考，突出了定义 MCP 服务器在市场上的关键元数据点。这种结构化的视图对于人类理解和机器处理（例如，AI Agent 需要理解服务器属性以生成代码）都至关重要。\n\n**表：MCP 服务器关键属性与能力**\n\n| 属性/能力 | 描述 | 来源片段 | 重要性/目的 |\n| :---- | :---- | :---- | :---- |\n| **服务器名称** | 服务器的唯一标识符，通常指示其功能。 | 3 | 发现、识别 |\n| **作者/组织** | 服务器的创建者或维护者。 | 4 | 归属、可信度 |\n| **简要描述** | 服务器功能的简洁概述。 | 5 | 快速理解 |\n| **详细描述** | 服务器目的和功能的全面说明。 | 4 | 深入理解 |\n| **标签/关键词** | 与服务器相关的分类词，用于过滤和搜索。 | 4 | 发现、分类 |\n| **许可证类型** | 服务器软件的许可协议（例如 MIT 许可证）。 | 4 | 法律合规性、使用限制 |\n| **编程语言** | 服务器的实现语言（例如 Python, TypeScript）。 | 2 | 技术栈兼容性、开发人员偏好 |\n| **操作系统兼容性** | 服务器支持的操作系统（例如 Apple, Linux）。 | 4 | 部署环境兼容性 |\n| **质量评分 (Asecurity, Alicense, Aquality)** | 指示服务器的安全性、许可证合规性和整体质量。 | 4 | 信任、可靠性评估 |\n| **远程能力** | 指示服务器是否可以远程托管和运行。 | 4 | 部署灵活性 |\n| **HTTP 连接 URL** | 用于直接连接服务器的 URL。 | 4 | 直接集成 |\n| **提供的能力 (资源, 工具, 提示)** | 服务器暴露给客户端的核心功能类型。 | 2 | 功能分类、LLM 交互类型 |\n| **工具定义 (名称, 描述, 示例提示, 模式)** | 服务器提供的具体函数，包括其输入/输出结构。 | 2 | LLM 调用、功能理解、代码生成 |\n| **设置说明** | 运行服务器所需的先决条件（例如 API 密钥）。 | 4 | 部署准备 |\n| **安装方法** | 详细的安装步骤和命令（例如 npm/npx, pnpm）。 | 4 | 部署指南 |\n| **故障排除提示** | 常见问题及其解决方案。 | 4 | 用户支持、问题解决 |\n| **源代码 URL** | 指向服务器代码仓库的链接。 | 4 | 透明度、贡献、审查 |\n| **问题跟踪器 URL** | 用于报告和跟踪服务器问题的链接。 | 4 | 错误报告、维护 |\n| **上次更新日期** | 服务器信息或代码上次更新的时间。 | 3 | 活跃度、新近度 |\n| **每周下载量/GitHub 星数** | 服务器的受欢迎程度指标。 | 3 | 流行度、社区认可 |\n| **相关服务器** | 与当前服务器功能相似的其他服务器列表。 | 4 | 发现替代方案、扩展探索 |\n| **类别** | 服务器所属的功能类别（例如 数据库、搜索）。 | 3 | 分类浏览、过滤 |\n\n## **4\\. 新平台功能需求**\n\n为了构建一个功能丰富且用户友好的 MCP 服务器市场，以下是根据现有平台分析和未来发展需求提出的功能需求。\n\n### **4.1. 用户管理与认证**\n\n用户必须能够注册、登录和管理他们的个人资料。虽然现有资料没有明确详细说明用户账户，但“添加服务器”按钮 3 和服务器作者的显示（例如“by kohasummons”）4 表明，用户账户对于归属和贡献是必需的。这是任何允许用户生成内容的平台的基础要求。\n\n* **需求**：用户必须能够注册、登录和管理他们的个人资料。  \n* **详细信息**：  \n  * 提供安全的认证机制（例如，OAuth、电子邮件/密码）。  \n  * 允许用户编辑个人资料（显示名称、联系信息）。  \n  * 支持密码管理功能。\n\n### **4.2. MCP 服务器提交与生命周期管理**\n\n开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。\n\n* **需求**：开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。  \n* **详细信息**：  \n  * **服务器提交表单**：捕获所有在 4 和 5 中识别的属性（服务器标题、作者、描述、标签、许可证、兼容性、功能概述、详细功能、集成、设置说明、安装方法、故障排除、工具（包括名称、描述、示例提示）、源代码 URL、问题跟踪器 URL、HTTP 连接 URL）。  \n  * **工具定义输入**：为单个工具提供结构化输入，包括其 OpenAPI/JSON Schema 定义（4 中的“Schema”导航链接和 2 中的“Tools”表格暗示）。服务器提交过程不仅仅是简单的文本输入。它是一个复杂的、结构化的数据摄取流程，需要以对开发者友好且对 AI Agent 机器可读的方式捕获结构化信息（例如，工具模式、安装步骤、故障排除点）。这需要支持富文本编辑器、代码块，并可能在提交过程中进行模式验证，以确保数据质量。  \n  * **质量指标输入/生成**：提供提交或生成 Asecurity、Alicense、Aquality 评分的机制。这可能涉及自动化检查或人工审核 4。  \n  * **草稿/发布工作流**：支持保存草稿、预览以及发布/取消发布服务器的功能。  \n  * **编辑/更新**：允许作者更新服务器信息。  \n  * **版本控制**：支持服务器的不同版本（3 和 5 中的“上次更新”暗示）。  \n  * **审核**：对新提交和更新进行管理审核流程。\n\n### **4.3. 高级服务器发现与探索**\n\n用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。\n\n* **需求**：用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。  \n* **详细信息**：  \n  * **关键词搜索**：支持对服务器名称、描述、功能和标签进行全文搜索。支持“深度搜索”和“新搜索”功能 3。  \n  * **分类**：显示并按预定义类别（例如 Python、远程、数据库、RAG 系统、区块链等）进行过滤，并显示每个类别的服务器计数 3。  \n  * **高级过滤**：按编程语言、许可证类型、操作系统兼容性、“官方”状态、质量评分（Asecurity、Alicense、Aquality）进行过滤 5。  \n  * **排序**：按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数进行排序 3。  \n  * **分页/无限滚动**：高效加载大量服务器列表，支持“加载更多”按钮 3。\n\n详细的过滤和排序选项不仅适用于人类用户，对于 AI Agent 也至关重要。一个 AI Agent 可以通过编程方式查询市场 API（4 中“MCP 目录 API”的提示）来查找符合特定条件的服务器（例如，“查找所有具有 Asecurity 评级的基于 Python 的 RAG 系统服务器”）。这意味着底层的搜索和过滤逻辑必须健壮、高性能，并且最好通过 API 暴露以供编程访问，从而使该平台成为自动化 AI 开发工作流的宝贵资源。\n\n### **4.4. 详细服务器信息显示与交互**\n\n为每个 MCP 服务器提供一个全面且结构良好的详细页面。\n\n* **需求**：为每个 MCP 服务器提供一个全面且结构良好的详细页面。  \n* **详细信息**：  \n  * 显示 4 中指定的所有属性和功能（服务器标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。  \n  * 提供交互式“安装服务器”按钮（可能根据客户端动态显示说明）4。  \n  * 提供“查看源代码”和“报告问题”的可点击链接 4。  \n  * 清晰呈现“工具”部分，包括描述和示例提示 4。  \n  * 显示“相关 MCP 服务器”和“新 MCP 服务器”以增强发现能力 4。  \n  * 与外部资源（例如 Reddit 讨论）集成 4。  \n  * 提供 HTTP 连接 URL 以便直接集成 4。\n\n“安装服务器”按钮 4 是一个关键的行动呼吁。它的有效性取决于提供针对各种 MCP 客户端（例如 Claude Desktop、IDE）量身定制的清晰、可操作的说明。这表明需要根据检测到的客户端或用户偏好进行动态内容生成或条件显示安装步骤。此功能直接将市场的发现功能与服务器在 AI 应用程序中的实际集成连接起来。\n\n### **4.5. 社区功能**\n\n使用户能够提供反馈并与平台和服务器开发者互动。\n\n* **需求**：使用户能够提供反馈并与平台和服务器开发者互动。  \n* **详细信息**：  \n  * 提供页面“这有用吗？是/否”的反馈机制 3。  \n  * 支持对单个服务器进行评论和评分（4 中“评论”和“评分”导航链接暗示）。  \n  * 提供讨论论坛或与现有社区平台（例如 3 和 4 中提到的 Discord、Reddit 链接）集成。  \n  * 支持新闻通讯注册以获取更新 3。\n\n反馈机制（有用性、评论、问题报告）对于 MCP 生态系统的健康和发展至关重要。它们为服务器开发者提供改进的宝贵信号，并为其他用户评估服务器质量和可靠性提供依据。这形成了一个良性循环：良好的反馈促成更好的服务器，从而带来更多的采用，进一步丰富了市场。平台必须有效地捕获、聚合和显示这些反馈。\n\n### **4.6. 平台管理与监控**\n\n提供工具供管理员管理内容、用户并监控平台健康状况。\n\n* **需求**：提供工具供管理员管理内容、用户并监控平台健康状况。  \n* **详细信息**：  \n  * 用户账户管理（暂停、删除）。  \n  * 服务器提交和评论的内容审核。  \n  * 类别管理（添加、编辑、删除类别）。  \n  * 分析仪表板（服务器下载量、视图、搜索查询）。  \n  * 错误日志和监控（例如，用于处理服务器 API 访问问题）。\n\n这些功能对于维护平台完整性、质量和可扩展性至关重要，尤其是在处理用户生成内容时。\n\n## **5\\. 领域驱动设计 (DDD) 核心实体模型**\n\n为了有效构建 MCP 服务器市场，采用领域驱动设计（DDD）方法至关重要，它有助于将复杂的业务逻辑分解为可管理的部分。\n\n### **5.1. 边界上下文识别**\n\n系统被划分为以下边界上下文，每个上下文都围绕特定的领域功能和一致性边界：\n\n* **服务器目录上下文 (Server Catalog Context)**：负责管理 MCP 服务器列表、发现、搜索和详细信息。这是市场的核心公共面向部分。  \n* **服务器提交与管理上下文 (Server Submission & Management Context)**：处理服务器提交、更新和开发者特定交互的生命周期。  \n* **用户与身份上下文 (User & Identity Context)**：管理用户注册、身份验证、个人资料和角色。  \n* **社区与反馈上下文 (Community & Feedback Context)**：管理评论、评分和通用平台反馈。  \n* **管理上下文 (Administration Context)**：处理平台级别的配置、审核和分析。\n\n将系统分解为不同的边界上下文是一项战略性的 DDD 决策。它避免了单体设计，降低了开发团队的认知负担，并允许系统不同部分的独立演进和扩展。例如，服务器目录 可以针对读取性能进行高度优化，而 服务器提交 则专注于数据完整性和工作流。这种分离对于需要满足不同用户角色和功能的复杂系统至关重要。\n\n### **5.2. 聚合根、实体与值对象详细建模**\n\n以下是核心边界上下文中的聚合根、实体和值对象的详细模型：\n\n#### **5.2.1. 服务器目录上下文**\n\n* **聚合根: MCP\\_Server**  \n  * **标识**: serverId (UUID)  \n  * **属性 (值对象/基本类型)**:  \n    * ServerIdentity: name (字符串), author (字符串), slug (字符串, 唯一的 URL 标识符)。  \n    * Description: shortDescription (字符串), longDescription (富文本)。  \n    * Metadata: licenseType (枚举/字符串, 例如 \"MIT License\"), programmingLanguage (枚举/字符串, 例如 \"Python\"), supportedOperatingSystems (枚举集合, 例如 \"Apple\", \"Linux\")。  \n    * QualityMetrics: securityScore (枚举/字符串, 例如 \"Asecurity\"), licenseScore (枚举/字符串, 例如 \"Alicense\"), qualityScore (枚举/字符串, 例如 \"Aquality\")。  \n    * IntegrationDetails: httpConnectionUrl (URL), remoteCapable (布尔值)。  \n    * Timestamps: dateAdded (DateTime), lastUpdated (DateTime)。  \n    * PopularityMetrics: weeklyDownloads (整数), gitHubStars (整数), recentGitHubStars (整数)。  \n  * **聚合内的实体**:  \n    * ServerCapability: 代表服务器可以做什么。  \n      * type (枚举: RESOURCE, TOOL, PROMPT)  \n      * description (字符串)  \n      * details (富文本, 例如 \"与 ChainGPT AI 交互\")  \n    * ToolDefinition: 代表服务器暴露的特定函数。  \n      * toolName (字符串, 例如 chaingpt\\_invoke\\_chat)  \n      * toolDescription (字符串)  \n      * examplePrompts (字符串列表)  \n      * schema (JSON/OpenAPI 模式, 代表输入/输出结构)  \n    * IntegrationStep: 集成服务器的详细说明。  \n      * platform (字符串, 例如 \"Claude Desktop\", \"Smithery\")  \n      * method (字符串, 例如 \"手动安装\", \"通过 Smithery\")  \n      * instructions (包含代码片段的富文本)  \n    * TroubleshootingTip: 常见问题和解决方案。  \n      * issue (字符串)  \n      * solution (富文本)  \n  * **值对象**:  \n    * Category: name (字符串, 例如 \"数据库\", \"搜索\"), count (整数)。  \n    * Tag: name (字符串, 例如 \"区块链\", \"TypeScript\")。  \n    * ExternalLink: url (URL), type (枚举: GITHUB\\_SOURCE, ISSUE\\_TRACKER, REDDIT\\_DISCUSSION)。  \n* **聚合根: Category**  \n  * **标识**: categoryId (UUID)  \n  * **属性**: name (字符串, 唯一), slug (字符串), description (字符串), serverCount (整数)。  \n  * **关系**: MCP\\_Server 与 Category 之间存在多对多关系（通过 Tag 或直接关联）。\n\n#### **5.2.2. 用户与身份上下文**\n\n* **聚合根: User**  \n  * **标识**: userId (UUID)  \n  * **属性**: username (字符串), email (字符串), passwordHash (字符串), profileName (字符串), roles (枚举集合: DEVELOPER, USER, ADMIN)。  \n  * **聚合内的实体**: UserProfile (例如, 个人简介, 头像)。\n\n#### **5.2.3. 社区与反馈上下文**\n\n* **聚合根: Review**  \n  * **标识**: reviewId (UUID)  \n  * **属性**: rating (整数), comment (字符串), timestamp (DateTime)。  \n  * **关系**: Review 属于 User (评论者) 和 MCP\\_Server (被评论的服务器)。  \n* **实体: Feedback**  \n  * **标识**: feedbackId (UUID)  \n  * **属性**: pageUrl (URL), helpful (布尔值), comment (可选字符串), timestamp (DateTime)。  \n  * **关系**: Feedback 可以与 User 关联。\n\n### **5.3. 关键关系与领域不变性**\n\n* 一个 MCP\\_Server 必须有一个唯一的 slug。  \n* MCP\\_Server 内的 ToolDefinition 必须有唯一的 toolName。  \n* QualityMetrics 应该一致地应用，并可能基于外部数据（例如，GitHub 仓库分析）进行计算。  \n* 一个 Review 必须链接到一个现有的 User 和 MCP\\_Server。  \n* Category 必须有唯一的名称。\n\n**表：DDD 模型 \\- 核心实体、属性与关系**\n\n下表提供了所识别领域模型的清晰、结构化概述，可作为数据库模式设计和后端服务开发的蓝图。它明确定义了聚合、实体和值对象，以及它们的关键属性和关系，这对于 AI Agent 生成数据模型和 ORM 代码至关重要。\n\n| 边界上下文 | 领域对象类型 | 名称 | 关键属性 | 关系 | 关键不变性/规则 |\n| :---- | :---- | :---- | :---- | :---- | :---- |\n| **服务器目录** | 聚合根 | MCP\\_Server | serverId, ServerIdentity, Description, Metadata, QualityMetrics, IntegrationDetails, Timestamps, PopularityMetrics | 包含 ServerCapability, ToolDefinition, IntegrationStep, TroubleshootingTip 实体；与 Category 多对多 | slug 唯一；ToolDefinition.toolName 在 MCP\\_Server 内唯一 |\n|  | 实体 | ServerCapability | type, description, details | 属于 MCP\\_Server | \\- |\n|  | 实体 | ToolDefinition | toolName, toolDescription, examplePrompts, schema | 属于 MCP\\_Server | \\- |\n|  | 实体 | IntegrationStep | platform, method, instructions | 属于 MCP\\_Server | \\- |\n|  | 实体 | TroubleshootingTip | issue, solution | 属于 MCP\\_Server | \\- |\n|  | 值对象 | Category | name, count | \\- | \\- |\n|  | 值对象 | Tag | name | \\- | \\- |\n|  | 值对象 | ExternalLink | url, type | \\- | \\- |\n| **服务器目录** | 聚合根 | Category | categoryId, name, slug, description, serverCount | 与 MCP\\_Server 多对多 | name 唯一 |\n| **用户与身份** | 聚合根 | User | userId, username, email, passwordHash, profileName, roles | 包含 UserProfile 实体 | username, email 唯一 |\n|  | 实体 | UserProfile | bio, avatar | 属于 User | \\- |\n| **社区与反馈** | 聚合根 | Review | reviewId, rating, comment, timestamp | 属于 User，关联 MCP\\_Server | 必须链接到现有 User 和 MCP\\_Server |\n|  | 实体 | Feedback | feedbackId, pageUrl, helpful, comment, timestamp | 可关联 User | \\- |\n\n## **6\\. 敏捷用户故事**\n\n以下用户故事遵循“作为 \\[用户角色\\]，我想要 \\[行动\\]，以便 \\[益处\\]”的格式，并确保符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。每个故事都包含详细的验收标准，为 AI Agent 进行相关的代码生成提供清晰、明确的步骤。\n\n### **6.1. 用户故事通用原则**\n\n* 遵循“作为 \\[用户角色\\]，我想要 \\[行动\\]，以便 \\[益处\\]”的格式。  \n* 确保故事符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。  \n* 每个故事都有详细的验收标准，提供清晰、明确的测试步骤和 AI Agent 代码生成指导。  \n* 重点关注具体的数据点和交互，以提高 AI Agent 的清晰度。\n\n### **6.2. 服务器发现与列表用户故事**\n\n* **US-001: 查看所有 MCP 服务器**  \n  * **描述**：作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。  \n  * **验收标准**：  \n    * 当我导航到服务器列表页面 (/mcp/servers) 时。  \n    * 我看到“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）3。  \n    * 我看到整个列表的“上次更新”时间戳 3。  \n    * 我看到一个分页/可滚动的 MCP 服务器条目列表。  \n    * 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）5。  \n    * 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目 3。  \n* **US-002: 按类别筛选服务器**  \n  * **描述**：作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面 (/mcp/servers) 时。  \n    * 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。  \n    * URL 更新以反映该类别（例如，/mcp/servers/categories/python）6。  \n    * 显示的列表仅显示标记为“Python”的服务器 3。  \n    * “Python”的类别计数准确（例如，“2,436”）3。  \n    * 每个显示的服务器条目都符合 US-001 的标准。  \n* **US-003: 按受欢迎程度/新近度排序服务器**  \n  * **描述**：作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面时。  \n    * 当我从排序下拉菜单中选择“每周下载量”时。  \n    * 服务器列表重新排序，以最高每周下载量的服务器优先显示 3。  \n    * 当我从排序下拉菜单中选择“更新日期”时。  \n    * 服务器列表重新排序，以最近更新的服务器优先显示 3。  \n    * 排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数 3。  \n* **US-004: 按关键词搜索服务器**  \n  * **描述**：作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面时。  \n    * 当我在搜索栏中输入“区块链”并按 Enter 键时。  \n    * 页面显示名称、描述或标签中包含“区块链”的服务器列表 5。  \n    * “深度搜索”和“新搜索”按钮可用 3。  \n    * 每个显示的服务器条目都符合 US-001 的标准。  \n* **US-005: 查看单个服务器详情**  \n  * **描述**：作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。  \n  * **验收标准**：  \n    * 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。  \n    * 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）4。  \n    * 页面突出显示：服务器标题、作者、标签、许可证、兼容性 4。  \n    * 页面包含导航链接：概述、Schema、相关服务器、评论、评分 4。  \n    * 页面提供“查看源代码”和“报告问题”链接 4。  \n    * “您可以使用此服务器做什么？”部分详细说明了核心功能 4。  \n    * “此服务器有哪些集成可用？”部分概述了技术要求和集成点 4。  \n    * “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）4。  \n    * “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）4。  \n    * “故障排除”部分列出了常见问题和解决方案 4。  \n    * “工具”表格列出了每个工具的名称、描述和示例提示 4。  \n    * 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接 4。  \n    * “安装服务器”按钮和 HTTP 连接 URL 存在 4。  \n    * “相关 MCP 服务器”部分显示了类似服务器的列表 4。  \n    * “新 MCP 服务器”部分显示了最近添加的服务器 4。  \n    * “这有用吗？是/否”反馈按钮可用 4。\n\n### **6.3. 服务器提交与管理用户故事**\n\n* **US-006: 提交新的 MCP 服务器**  \n  * **描述**：作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。  \n  * **验收标准**：  \n    * 当我以开发者身份登录时。  \n    * 当我点击“添加服务器”按钮时 3。  \n    * 我看到一个多步骤表单，用于输入服务器详细信息。  \n    * 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框 2。  \n    * 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述 2。  \n    * 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段 2。  \n    * 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）4。  \n    * 我可以添加多个“故障排除提示”，包括问题和解决方案 4。  \n    * 我可以选择相关的“类别”并添加“标签”4。  \n    * 当我提交表单时，服务器进入待审核状态。  \n    * 我收到确认消息。  \n* **US-007: 编辑我提交的 MCP 服务器**  \n  * **描述**：作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。  \n  * **验收标准**：  \n    * 当我以已提交服务器的作者身份登录时。  \n    * 当我导航到我的“我的服务器”仪表板时。  \n    * 我选择一个现有服务器进行编辑。  \n    * 我看到提交表单预填充了服务器的当前数据 4。  \n    * 我可以修改任何字段并重新提交以供审核。  \n    * 当我重新提交时，服务器的“上次更新”时间戳在批准后更新 3。\n\n### **6.4. 社区与反馈用户故事**\n\n* **US-008: 提供有用性反馈**  \n  * **描述**：作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。  \n  * **验收标准**：  \n    * 当我位于任何服务器列表或详情页面时。  \n    * 当我点击“这有用吗？”提示中的“是”或“否”时 3。  \n    * 我的反馈被记录。  \n    * 反馈提示消失或改变以表明我的提交。  \n* **US-009: 为 MCP 服务器留下评论**  \n  * **描述**：作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。  \n  * **验收标准**：  \n    * 当我登录并位于 MCP 服务器的详情页面时。  \n    * 当我导航到“评论”部分时 4。  \n    * 我可以看到现有评论和一个提交新评论的表单。  \n    * 我可以选择一个评分（例如，1-5 星）并输入评论。  \n    * 当我提交评论时，它在审核后（如果适用）出现在页面上。  \n    * 服务器的整体评分更新 4。\n\n**表：用户故事目录**\n\n下表提供了结构化、可操作的开发任务列表，直接映射到功能需求。其详细的验收标准旨在足够精确，以便 AI Agent 能够解释并生成相关的代码，涵盖前端 UI 和后端逻辑。这种详细程度确保了开发过程的完整性并减少了歧义。\n\n| 故事 ID | 标题 | 描述 | 验收标准 | 优先级 |\n| :---- | :---- | :---- | :---- | :---- |\n| US-001 | 查看所有 MCP 服务器 | 作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。 | 当我导航到服务器列表页面 (/mcp/servers) 时，我看到“开源 MCP 服务器”的总数和“上次更新”时间戳。我看到一个分页/可滚动的 MCP 服务器条目列表，每个条目显示名称、官方标签、开发者、质量指标、描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标。当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。 | 高 |\n| US-002 | 按类别筛选服务器 | 作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域相关的服务器。 | 当我位于服务器列表页面时，我从侧边栏/筛选选项中选择一个类别（例如，“Python”）。URL 更新以反映该类别，显示的列表仅显示标记为该类别的服务器，且类别计数准确。每个显示的服务器条目都符合 US-001 的标准。 | 高 |\n| US-003 | 按受欢迎程度/新近度排序服务器 | 作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。 | 当我位于服务器列表页面时，我从排序下拉菜单中选择“每周下载量”或“更新日期”。服务器列表重新排序，以最高每周下载量或最近更新的服务器优先显示。排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数。 | 高 |\n| US-004 | 按关键词搜索服务器 | 作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。 | 当我位于服务器列表页面时，我在搜索栏中输入关键词并按 Enter 键。页面显示名称、描述或标签中包含关键词的服务器列表。“深度搜索”和“新搜索”按钮可用。每个显示的服务器条目都符合 US-001 的标准。 | 高 |\n| US-005 | 查看单个服务器详情 | 作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。 | 当我点击服务器列表中的一个服务器条目时，我被导航到服务器的详情页面。页面突出显示服务器标题、作者、标签、许可证、兼容性，并包含导航链接、开发链接、详细的功能、集成、设置、安装、故障排除、工具、质量指标、安装按钮、HTTP 连接 URL、相关/新服务器列表和有用性反馈按钮。 | 高 |\n| US-006 | 提交新的 MCP 服务器 | 作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。 | 当我以开发者身份登录并点击“添加服务器”按钮时，我看到一个多步骤表单，用于输入服务器的详细信息。表单包含所有必要的字段，包括结构化的功能、工具、集成步骤和故障排除提示。提交后，服务器进入待审核状态，并收到确认消息。 | 中 |\n| US-007 | 编辑我提交的 MCP 服务器 | 作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。 | 当我以已提交服务器的作者身份登录并导航到“我的服务器”仪表板时，我选择一个现有服务器进行编辑。我看到提交表单预填充了服务器的当前数据，可以修改任何字段并重新提交以供审核。重新提交后，服务器的“上次更新”时间戳在批准后更新。 | 中 |\n| US-008 | 提供有用性反馈 | 作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。 | 当我位于任何服务器列表或详情页面时，我点击“这有用吗？”提示中的“是”或“否”。我的反馈被记录，反馈提示消失或改变以表明我的提交。 | 低 |\n| US-009 | 为 MCP 服务器留下评论 | 作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。 | 当我登录并位于 MCP 服务器的详情页面时，我导航到“评论”部分。我可以看到现有评论和一个提交新评论的表单。我可以选择一个评分并输入评论。提交后，评论在审核后（如果适用）出现在页面上，服务器的整体评分更新。 | 低 |\n\n## **7\\. 结论与下一步**\n\n### **7.1. 拟议架构与开发方法总结**\n\n本报告概述了一个基于领域驱动设计的健壮、模块化架构，强调通过边界上下文实现清晰的关注点分离。这种分解有助于避免单体设计，降低开发复杂性，并允许系统不同部分独立演进和扩展。详细的功能需求和敏捷用户故事为迭代开发提供了清晰的路线图，由于其特异性和完整性，非常适合利用 AI Agent 进行代码生成。这种方法将确保开发过程的高效性和产出的高质量。\n\n### **7.2. 实施与未来增强建议**\n\n为了成功实施和持续发展 MCP 服务器市场，建议采取以下步骤和考虑未来的增强功能：\n\n* **分阶段开发**：建议优先开发核心服务器列表和发现功能（US-001 至 US-005），其次是服务器提交和管理功能（US-006，US-007），最后是社区功能（US-008，US-009）。这种迭代方法将确保核心价值的快速交付和用户反馈的早期整合。  \n* **API 优先方法**：开发一个全面的市场 API（如 4 中“MCP 目录 API”所暗示），以实现 AI Agent 和其他应用程序的编程访问，而不仅仅是基于 Web 的用户界面。这将极大地扩展平台的实用性和集成潜力。  \n* **自动化质量检查**：探索集成自动化工具，在服务器提交过程中生成/验证 Asecurity、Alicense 和 Aquality 评分。这将提高数据质量和用户对服务器信息的信任。  \n* **AI 驱动的搜索**：研究使用 LLM 增强市场自身的搜索能力，允许用户使用自然语言查询来查找相关的 MCP 服务器。这将提升用户体验并进一步利用 AI 技术。  \n* **监控与分析**：实施强大的日志记录和分析系统，以跟踪服务器使用情况、热门类别和用户行为，从而为未来的开发提供信息并优化平台。  \n* **可扩展性考虑**：在设计数据模型和服务时，应充分考虑可扩展性，以应对服务器数量和用户流量的增长。确保基础设施能够支持未来的扩展需求。\n\n#### **Works cited**\n\n1. Model Context Protocol: Introduction, accessed June 20, 2025, [https://modelcontextprotocol.io/introduction](https://modelcontextprotocol.io/introduction)  \n2. For Server Developers \\- Model Context Protocol, accessed June 20, 2025, [https://modelcontextprotocol.io/quickstart/server](https://modelcontextprotocol.io/quickstart/server)  \n3. MCP servers | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers](https://glama.ai/mcp/servers)  \n4. ChainGPT MCP | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp)  \n5. Search | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/categories/search](https://glama.ai/mcp/servers/categories/search)  \n6. Databases | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/categories/databases](https://glama.ai/mcp/servers/categories/databases)  \n7. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/inspect](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/inspect)  \n8. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/schema](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/schema)  \n9. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/related-servers](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/related-servers)\n\n=== 分析要求 ===\n请确保输出的Markdown格式正确且信息完整。特别注意：\n1. 深度理解业务语义，不要只是简单提取文字\n2. 识别隐含的业务规则和约束\n3. 考虑用户的真实使用场景和痛点\n4. 确保提取的信息具有可操作性\n"}]}
{"timestamp": "2025-06-25T22:31:31.021273", "agent_name": "business_analyzer", "interaction_id": 2, "task_description": "业务需求分析", "interaction_type": "response", "content": "# 处理后的开发规则\n\n## 1. 规则概述\n- **规则来源**: rules.md\n- **处理时间**: 2023年10月27日\n- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发\n- **主要改进**:\n    - 统一并标准化了规则的分类和结构。\n    - 明确了各层职责和依赖关系。\n    - 细化了命名规范和文件组织原则。\n    - 强调了测试、工程实践和Git提交的重要性。\n    - 补充了数据库设计和模块内代码组织的关键约束。\n\n## 2. 核心原则\n- **架构设计原则**:\n    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。\n    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。\n    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。\n    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。\n- **开发方法论**:\n    - **测试驱动**: 任何新功能或修改必须伴随自动化测试。\n    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。\n- **质量标准理念**:\n    - **代码风格**: 严格遵循 PEP 8 规范。\n    - **类型提示**: 强制要求所有函数签名和变量声明包含明确类型提示。\n    - **文档语言**: 所有注释、文档字符串和文档内容必须使用**英文**编写。\n    - **数据库设计**: 强制使用 UUID 作为主键，字段命名只反映业务含义，禁止技术后缀。\n\n## 3. 技术规范\n- **技术栈要求**:\n    - **Web 框架**: FastAPI\n    - **数据验证与建模**: Pydantic\n    - **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)\n    - **测试框架**: Pytest\n    - **HTTP 测试**: FastAPI TestClient\n    - **Mocking**: unittest.mock 或 pytest-mock\n- **框架使用规范**:\n    - FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型。\n    - SQLAlchemy ORM 模型使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`。\n    - 禁止在应用启动时自动创建数据库表，所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n- **编码标准**:\n    - 严格遵循 PEP 8 规范。\n    - 强制使用类型提示。\n    - 所有注释和文档使用英文。\n\n## 4. 架构约束\n- **分层架构规则**:\n    - **Interfaces (接口层)**: 处理 HTTP 请求、数据转换，通过依赖注入调用应用层服务。\n    - **Application (应用层)**: 编排领域逻辑，协调用例，定义事务边界。\n    - **Domain (领域层)**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\n    - **Infrastructure (基础设施层)**: 实现与外部世界（数据库、文件系统、第三方 API）交互的技术细节，负责领域模型与 ORM 模型之间的转换。\n- **模块组织方式**:\n    - 项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。\n    - 通用代码放置在根目录的 `common/` 文件夹下。\n    - 模块内当包含多个相关但职责不同的业务子域时，通过文件命名前缀来区分，而不是创建深层嵌套的目录结构。\n- **依赖关系约束**:\n    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。\n    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。\n    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入任何 `fastapi`, `sqlalchemy` 或其他外部框架。\n    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可以被任何层级导入，但通用代码本身不能反向依赖任何业务模块。\n    - **跨业务子域依赖**: 同模块内业务子域之间可以相互依赖，但必须通过应用层服务接口。跨模块则必须通过目标模块的应用层服务接口。\n\n## 5. 代码质量标准\n- **代码风格规范**:\n    - 严格遵循 PEP 8。\n    - 强制使用类型提示。\n    - 所有代码、注释、文档字符串和文档内容必须使用**英文**编写。\n- **测试要求**:\n    - **测试框架**: Pytest。\n    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致，按业务模块组织。\n    - **测试类型与分层**:\n        - **单元测试**: 针对单一模块或函数，`Domain Layer` 测试实体业务方法，`Application Layer` 测试服务业务流程时需模拟（Mock）外部依赖。\n        - **接口/集成测试**: 使用 `TestClient` 测试从 HTTP 端点到数据库的完整流程，验证各层集成。\n    - **核心测试原则**:\n        - 任何新功能或修改**必须**有相应的测试用例覆盖。\n        - 所有测试用例**必须通过**。\n        - 测试失败时，优先修复生产代码。\n        - **严禁**随意修改测试用例，除非需求变更或测试用例设计缺陷。\n    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用**英文**描述。\n- **文档标准**:\n    - 所有 API 端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码变更同步更新。\n    - 所有注释、文档字符串和文档内容必须使用**英文**编写。\n\n## 6. 工程实践\n- **开发流程**:\n    - **代码生成约束与流程**: 遵循“Domain First”原则，从领域层开始，逐步实现应用层、基础设施层和接口层，最后集成到主应用。\n    - **重构约束**:\n        - 优先创建新文件进行修改，最后删除旧文件。\n        - 重构前需分析完整上下文。\n        - 重构后必须运行自动化测试，无测试覆盖部分应补充测试。\n- **版本控制**:\n    - **Git 提交规范**: 强制遵循约定式提交（Conventional Commits）规范。\n        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。\n        - **`type`**: 必须是 `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert` 之一。\n        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。\n        - **`subject`**: 简短描述，不超过 50 字符，祈使句，现在时态，首字母小写，不加句号，**英文**。\n        - **`body`**: 详细描述，祈使句，现在时态，**英文**。\n        - **`footer`**: 用于 `BREAKING CHANGE:` 或 `Closes #ISSUE_ID`。\n    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。\n- **部署规范**:\n    - **依赖管理**:\n        - 生成代码前检查 `requirements.txt`。\n        - 添加新依赖后，立即执行 `pip freeze > requirements.txt`。\n    - **环境变量管理**:\n        - 优先复用现有环境变量。\n        - 新增环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - **虚拟环境管理**:\n        - 生成终端命令前提醒用户激活 `venv` 虚拟环境。\n        - 提供激活命令参考。\n    - **数据库迁移**:\n        - **禁止**在应用启动时自动创建数据库表。\n        - 所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n\n## 7. 最佳实践\n- **开发技巧**:\n    - **模块内代码组织**:\n        - 当模块内包含多个业务子域时，通过文件命名前缀区分，避免深层嵌套目录。\n        - **强制要求**所有模块内的文件使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。\n        - 命名格式：`{业务子域}_{层级类型}.py`。\n        - 业务子域识别原则：基于数据模型、业务流程、外部依赖或生命周期差异。\n        - 禁止使用类似 `_management` 的功能性命名后缀。\n        - 禁止过度嵌套目录。\n    - **共享组件处理**:\n        - 模块内共享组件放置在 `shared/` 目录。\n        - 跨模块共享组件放置在根目录的 `common/` 或 `shared/` 目录。\n- **常见问题解决方案**:\n    - **测试失败**: 优先修复生产代码，而非修改测试。\n- **性能优化建议**:\n    - 数据库索引设计：主键自动创建唯一索引，外键字段和常用查询条件字段应创建索引。\n\n## 8. 约束与限制\n- **禁止的做法**:\n    - 在 `Domain` 层导入任何外部框架（如 `fastapi`, `sqlalchemy`）。\n    - 模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - 随意修改测试用例以通过测试。\n    - 在应用启动时自动创建数据库表。\n    - 数据库字段名包含技术实现细节（如 `id_uuid`, `name_str`）。\n    - 使用通用文件名（如 `models.py`, `services.py`）而不带业务子域前缀。\n    - 过度嵌套目录结构。\n    - 使用类似 `_management` 的功能性命名后缀。\n- **必须遵守的规则**:\n    - 所有实体主键必须使用 UUID 类型。\n    - 所有涉及实体 ID 的 Pydantic 模型字段和 FastAPI 路由参数必须声明为 `UUID` 类型。\n    - 数据库字段名必须只反映业务含义。\n    - 数据库表名使用复数形式的英文单词，下划线分隔，避免缩写。\n    - 任何新功能或修改必须有相应的测试用例覆盖。\n    - 所有测试用例必须通过。\n    - 所有 Git 提交必须遵循约定式提交规范。\n    - 任何代码生成或修改后，必须提醒用户运行相关自动化测试。\n    - 任何需要新库的代码生成前，必须检查 `requirements.txt`。\n    - 添加新依赖后，必须立即执行 `pip freeze > requirements.txt`。\n    - 添加新的环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - 生成终端命令前必须提醒用户激活虚拟环境。\n- **异常处理原则**:\n    - 接口层负责处理 HTTP 错误。\n    - 应用层定义事务边界。# 业务需求分析报告\n\n## 1. 业务概览\n- **项目名称**: MCP 服务器市场平台\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\n- **核心目标**:\n    1. 促进 MCP 服务器的发现、评估和集成。\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\n    5. 确保平台的可信度、安全性和质量。\n- **目标用户**:\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\n- **价值主张**:\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\n\n## 2. 核心实体分析\n\n### 实体1: MCP 服务器 (MCP_Server)\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\n- **主要属性**:\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\n    - **分类**: 标签/关键词、所属类别。\n- **业务规则**:\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\n    - 服务器内的每个工具定义必须有唯一的工具名称。\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\n- **关系**:\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\n\n### 实体2: 用户 (User)\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\n- **主要属性**:\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\n    - 个人简介、头像（UserProfile）。\n- **业务规则**:\n    - 用户名和电子邮件必须唯一。\n    - 密码必须加密存储。\n    - 不同角色拥有不同的操作权限。\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\n- **关系**:\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\n    - 与“反馈”：用户可以提供有用性反馈。\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\n\n### 实体3: 类别 (Category)\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\n- **主要属性**:\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\n- **业务规则**:\n    - 类别名称必须唯一。\n    - 类别计数应实时反映该类别下的服务器数量。\n- **关系**:\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\n\n### 实体4: 评论 (Review)\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\n- **主要属性**:\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\n- **业务规则**:\n    - 评论必须关联到一个用户和一个 MCP 服务器。\n    - 评论内容可能需要审核。\n    - 评分应影响 MCP 服务器的整体评分。\n- **关系**:\n    - 与“用户”：一个评论由一个用户发布。\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\n\n### 实体5: 反馈 (Feedback)\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\n- **主要属性**:\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\n- **业务规则**:\n    - 反馈可以匿名，也可以关联到用户。\n    - 反馈应被记录和聚合，用于平台改进。\n- **关系**:\n    - 可与“用户”关联。\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\n\n### 特别关注的MCP核心实体\n\n- **MCP服务器**:\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\n    - **版本管理**: 虽然文档未明确提及，但“上次更新”和“编辑/更新”功能暗示了服务器信息的版本概念，可能需要支持服务器代码和元数据的版本控制。\n- **工具定义**:\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\n- **用户角色**:\n    - **开发者**: 提交、管理和更新 MCP 服务器。\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\n    - **管理员**: 审核内容、管理用户、监控平台。\n- **质量评估**:\n    - **Asecurity**: 无已知漏洞。\n    - **Alicense**: 宽松许可证。\n    - **Aquality**: 确认可用。\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\n- **开发者生态**:\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\n\n## 3. 功能需求\n\n### 高优先级需求\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\n  - 验收标准:\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\n    - 列表分页/无限滚动，每个条目显示指定元数据。\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\n  - 功能分类: 发现与浏览\n\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\n  - 验收标准:\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\n    - 支持“深度搜索”和“新搜索”功能。\n  - 功能分类: 发现与浏览\n\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\n  - 验收标准:\n    - 排序下拉菜单提供所有排序选项。\n    - 选择不同排序选项时，服务器列表按预期重新排序。\n  - 功能分类: 发现与浏览\n\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\n  - 验收标准:\n    - 点击服务器列表中的条目，导航到其详情页面。\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\n    - “工具”部分清晰呈现名称、描述和示例提示。\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\n    - 显示 HTTP 连接 URL。\n  - 功能分类: 详情查看\n\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\n  - 验收标准:\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\n    - 提交后，服务器进入待审核状态，并收到确认消息。\n  - 功能分类: 内容管理\n\n### 中优先级需求\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\n  - 验收标准:\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\n  - 功能分类: 内容管理\n\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\n  - 验收标准:\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\n    - 用户能够编辑显示名称、联系信息。\n    - 支持密码找回/重置功能。\n  - 功能分类: 用户管理\n\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\n  - 验收标准:\n    - 页面上显示“这有用吗？是/否”按钮。\n    - 点击后，反馈被记录，按钮状态改变或消失。\n  - 功能分类: 社区互动\n\n### 低优先级需求\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\n  - 验收标准:\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\n    - 用户可以选择评分（1-5星）并输入评论。\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\n  - 功能分类: 社区互动\n\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\n  - 验收标准:\n    - 管理员登录后，访问管理仪表板。\n    - 能够暂停/删除用户账户。\n    - 能够审核和批准/拒绝服务器提交和评论。\n    - 能够添加/编辑/删除类别。\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\n  - 功能分类: 平台管理\n\n## 4. 非功能需求\n\n### 性能要求\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\n\n### 安全要求\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\n\n### 可用性要求\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\n\n### 可扩展性要求\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\n\n## 5. 业务规则\n\n1. **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\n   - 触发条件: 开发者提交新的 MCP 服务器。\n   - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\n\n2. **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\n   - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\n   - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\n\n3. **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\n   - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\n   - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\n\n4. **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\n   - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\n   - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\n\n5. **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\n   - 触发条件: 用户提交评论。\n   - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\n\n6. **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\n   - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\n   - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\n\n7. **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\n   - 触发条件: 用户尝试访问服务器提交/编辑功能。\n   - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\n\n## 6. 用户故事\n\n### 核心用户故事\n- **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\n  - 验收标准:\n    - 当我导航到服务器列表页面 (/mcp/servers) 时。\n    - 我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\n    - 我看到一个分页/可滚动的 MCP 服务器条目列表。\n    - 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\n    - 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\n\n- **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\n  - 验收标准:\n    - 当我位于服务器列表页面 (/mcp/servers) 时。\n    - 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\n    - URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\n    - 显示的列表仅显示标记为“Python”的服务器。\n    - “Python”的类别计数准确（例如，“2,436”）。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\n  - 验收标准:\n    - 当我位于服务器列表页面时。\n    - 当我在搜索栏中输入“区块链”并按 Enter 键时。\n    - 页面显示名称、描述或标签中包含“区块链”的服务器列表。\n    - “深度搜索”和“新搜索”按钮可用。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\n  - 验收标准:\n    - 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\n    - 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\n    - 页面突出显示：服务器标题、作者、标签、许可证、兼容性。\n    - 页面包含导航链接：概述、Schema、相关服务器、评论、评分。\n    - 页面提供“查看源代码”和“报告问题”链接。\n    - “您可以使用此服务器做什么？”部分详细说明了核心功能。\n    - “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\n    - “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\n    - “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\n    - “故障排除”部分列出了常见问题和解决方案。\n    - “工具”表格列出了每个工具的名称、描述和示例提示。\n    - 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\n    - “安装服务器”按钮和 HTTP 连接 URL 存在。\n    - “相关 MCP 服务器”部分显示了类似服务器的列表。\n    - “新 MCP 服务器”部分显示了最近添加的服务器。\n    - “这有用吗？是/否”反馈按钮可用。\n\n- **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\n  - 验收标准:\n    - 当我以开发者身份登录时。\n    - 当我点击“添加服务器”按钮时。\n    - 我看到一个多步骤表单，用于输入服务器详细信息。\n    - 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框。\n    - 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述。\n    - 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段。\n    - 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）。\n    - 我可以添加多个“故障排除提示”，包括问题和解决方案。\n    - 我可以选择相关的“类别”并添加“标签”。\n    - 当我提交表单时，服务器进入待审核状态。\n    - 我收到确认消息。\n\n## 7. 关键业务流程\n\n### 流程1: MCP 服务器发现与浏览\n- **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\n- **参与者**: 用户（包括最终用户、AI 代理）。\n- **主要步骤**:\n  1. **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\n  2. **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\n  3. **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\n  4. **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\n  5. **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\n  6. **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\n  7. **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\n- **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\n- **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\n\n### 流程2: MCP 服务器提交与发布\n- **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者使用其账户登录市场平台。\n  2. **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\n  3. **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\n  4. **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\n  5. **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\n  6. **审核结果反馈**:\n     - **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\n     - **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n  7. **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\n- **输入**: 开发者提交的服务器详细信息。\n- **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\n\n### 流程3: MCP 服务器信息更新\n- **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者登录市场平台。\n  2. **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\n  3. **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\n  4. **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\n  5. **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\n  6. **管理员审核更新**: 平台管理员对更新内容进行审查。\n  7. **更新结果反馈**:\n     - **审核通过**: 管理员批准更新，服务器信息在市场中更新。开发者收到更新成功的通知。\n     - **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n- **输入**: 开发者修改后的服务器详细信息。\n- **输出**: 更新后的 MCP 服务器信息。\n\n### 流程4: 用户反馈与社区互动\n- **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\n- **参与者**: 用户、MCP 服务器开发者、平台管理员。\n- **主要步骤**:\n  1. **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\n  2. **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\n  3. **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\n  4. **提交评论**: 用户提交评论。\n  5. **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\n  6. **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\n  7. **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\n- **输入**: 用户有用性选择、评论内容、评分。\n- **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。"}
{"timestamp": "2025-06-25T22:31:31.027275", "agent_name": "business_analyzer", "interaction_id": 3, "task_description": "业务需求分析", "interaction_type": "request", "content": [{"role": "system", "content": "**重要：请务必使用中文回答所有问题，所有输出内容必须是中文。**\n\n你是一位资深的业务分析专家，需要改进之前的分析结果。\n\n重点改进以下方面：\n- 增加缺失的业务实体和功能需求\n- 更全面地覆盖原始文档内容\n\n请确保：\n1. 补充缺失的信息\n2. 提高分析的准确性\n3. 增强逻辑一致性\n4. 完善细节描述\n\n原始分析结果：\n# 处理后的开发规则\n\n## 1. 规则概述\n- **规则来源**: rules.md\n- **处理时间**: 2023年10月27日\n- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发\n- **主要改进**:\n    - 统一并标准化了规则的分类和结构。\n    - 明确了各层职责和依赖关系。\n    - 细化了命名规范和文件组织原则。\n    - 强调了测试、工程实践和Git提交的重要性。\n    - 补充了数据库设计和模块内代码组织的关键约束。\n\n## 2. 核心原则\n- **架构设计原则**:\n    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。\n    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。\n    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。\n    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。\n- **开发方法论**:\n    - **测试驱动**: 任何新功能或修改必须伴随自动化测试。\n    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。\n- **质量标准理念**:\n    - **代码风格**: 严格遵循 PEP 8 规范。\n    - **类型提示**: 强制要求所有函数签名和变量声明包含明确类型提示。\n    - **文档语言**: 所有注释、文档字符串和文档内容必须使用**英文**编写。\n    - **数据库设计**: 强制使用 UUID 作为主键，字段命名只反映业务含义，禁止技术后缀。\n\n## 3. 技术规范\n- **技术栈要求**:\n    - **Web 框架**: FastAPI\n    - **数据验证与建模**: Pydantic\n    - **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)\n    - **测试框架**: Pytest\n    - **HTTP 测试**: FastAPI TestClient\n    - **Mocking**: unittest.mock 或 pytest-mock\n- **框架使用规范**:\n    - FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型。\n    - SQLAlchemy ORM 模型使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`。\n    - 禁止在应用启动时自动创建数据库表，所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n- **编码标准**:\n    - 严格遵循 PEP 8 规范。\n    - 强制使用类型提示。\n    - 所有注释和文档使用英文。\n\n## 4. 架构约束\n- **分层架构规则**:\n    - **Interfaces (接口层)**: 处理 HTTP 请求、数据转换，通过依赖注入调用应用层服务。\n    - **Application (应用层)**: 编排领域逻辑，协调用例，定义事务边界。\n    - **Domain (领域层)**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\n    - **Infrastructure (基础设施层)**: 实现与外部世界（数据库、文件系统、第三方 API）交互的技术细节，负责领域模型与 ORM 模型之间的转换。\n- **模块组织方式**:\n    - 项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。\n    - 通用代码放置在根目录的 `common/` 文件夹下。\n    - 模块内当包含多个相关但职责不同的业务子域时，通过文件命名前缀来区分，而不是创建深层嵌套的目录结构。\n- **依赖关系约束**:\n    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。\n    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。\n    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入任何 `fastapi`, `sqlalchemy` 或其他外部框架。\n    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可以被任何层级导入，但通用代码本身不能反向依赖任何业务模块。\n    - **跨业务子域依赖**: 同模块内业务子域之间可以相互依赖，但必须通过应用层服务接口。跨模块则必须通过目标模块的应用层服务接口。\n\n## 5. 代码质量标准\n- **代码风格规范**:\n    - 严格遵循 PEP 8。\n    - 强制使用类型提示。\n    - 所有代码、注释、文档字符串和文档内容必须使用**英文**编写。\n- **测试要求**:\n    - **测试框架**: Pytest。\n    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致，按业务模块组织。\n    - **测试类型与分层**:\n        - **单元测试**: 针对单一模块或函数，`Domain Layer` 测试实体业务方法，`Application Layer` 测试服务业务流程时需模拟（Mock）外部依赖。\n        - **接口/集成测试**: 使用 `TestClient` 测试从 HTTP 端点到数据库的完整流程，验证各层集成。\n    - **核心测试原则**:\n        - 任何新功能或修改**必须**有相应的测试用例覆盖。\n        - 所有测试用例**必须通过**。\n        - 测试失败时，优先修复生产代码。\n        - **严禁**随意修改测试用例，除非需求变更或测试用例设计缺陷。\n    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用**英文**描述。\n- **文档标准**:\n    - 所有 API 端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码变更同步更新。\n    - 所有注释、文档字符串和文档内容必须使用**英文**编写。\n\n## 6. 工程实践\n- **开发流程**:\n    - **代码生成约束与流程**: 遵循“Domain First”原则，从领域层开始，逐步实现应用层、基础设施层和接口层，最后集成到主应用。\n    - **重构约束**:\n        - 优先创建新文件进行修改，最后删除旧文件。\n        - 重构前需分析完整上下文。\n        - 重构后必须运行自动化测试，无测试覆盖部分应补充测试。\n- **版本控制**:\n    - **Git 提交规范**: 强制遵循约定式提交（Conventional Commits）规范。\n        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。\n        - **`type`**: 必须是 `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert` 之一。\n        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。\n        - **`subject`**: 简短描述，不超过 50 字符，祈使句，现在时态，首字母小写，不加句号，**英文**。\n        - **`body`**: 详细描述，祈使句，现在时态，**英文**。\n        - **`footer`**: 用于 `BREAKING CHANGE:` 或 `Closes #ISSUE_ID`。\n    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。\n- **部署规范**:\n    - **依赖管理**:\n        - 生成代码前检查 `requirements.txt`。\n        - 添加新依赖后，立即执行 `pip freeze > requirements.txt`。\n    - **环境变量管理**:\n        - 优先复用现有环境变量。\n        - 新增环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - **虚拟环境管理**:\n        - 生成终端命令前提醒用户激活 `venv` 虚拟环境。\n        - 提供激活命令参考。\n    - **数据库迁移**:\n        - **禁止**在应用启动时自动创建数据库表。\n        - 所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n\n## 7. 最佳实践\n- **开发技巧**:\n    - **模块内代码组织**:\n        - 当模块内包含多个业务子域时，通过文件命名前缀区分，避免深层嵌套目录。\n        - **强制要求**所有模块内的文件使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。\n        - 命名格式：`{业务子域}_{层级类型}.py`。\n        - 业务子域识别原则：基于数据模型、业务流程、外部依赖或生命周期差异。\n        - 禁止使用类似 `_management` 的功能性命名后缀。\n        - 禁止过度嵌套目录。\n    - **共享组件处理**:\n        - 模块内共享组件放置在 `shared/` 目录。\n        - 跨模块共享组件放置在根目录的 `common/` 或 `shared/` 目录。\n- **常见问题解决方案**:\n    - **测试失败**: 优先修复生产代码，而非修改测试。\n- **性能优化建议**:\n    - 数据库索引设计：主键自动创建唯一索引，外键字段和常用查询条件字段应创建索引。\n\n## 8. 约束与限制\n- **禁止的做法**:\n    - 在 `Domain` 层导入任何外部框架（如 `fastapi`, `sqlalchemy`）。\n    - 模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - 随意修改测试用例以通过测试。\n    - 在应用启动时自动创建数据库表。\n    - 数据库字段名包含技术实现细节（如 `id_uuid`, `name_str`）。\n    - 使用通用文件名（如 `models.py`, `services.py`）而不带业务子域前缀。\n    - 过度嵌套目录结构。\n    - 使用类似 `_management` 的功能性命名后缀。\n- **必须遵守的规则**:\n    - 所有实体主键必须使用 UUID 类型。\n    - 所有涉及实体 ID 的 Pydantic 模型字段和 FastAPI 路由参数必须声明为 `UUID` 类型。\n    - 数据库字段名必须只反映业务含义。\n    - 数据库表名使用复数形式的英文单词，下划线分隔，避免缩写。\n    - 任何新功能或修改必须有相应的测试用例覆盖。\n    - 所有测试用例必须通过。\n    - 所有 Git 提交必须遵循约定式提交规范。\n    - 任何代码生成或修改后，必须提醒用户运行相关自动化测试。\n    - 任何需要新库的代码生成前，必须检查 `requirements.txt`。\n    - 添加新依赖后，必须立即执行 `pip freeze > requirements.txt`。\n    - 添加新的环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - 生成终端命令前必须提醒用户激活虚拟环境。\n- **异常处理原则**:\n    - 接口层负责处理 HTTP 错误。\n    - 应用层定义事务边界。# 业务需求分析报告\n\n## 1. 业务概览\n- **项目名称**: MCP 服务器市场平台\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\n- **核心目标**:\n    1. 促进 MCP 服务器的发现、评估和集成。\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\n    5. 确保平台的可信度、安全性和质量。\n- **目标用户**:\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\n- **价值主张**:\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\n\n## 2. 核心实体分析\n\n### 实体1: MCP 服务器 (MCP_Server)\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\n- **主要属性**:\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\n    - **分类**: 标签/关键词、所属类别。\n- **业务规则**:\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\n    - 服务器内的每个工具定义必须有唯一的工具名称。\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\n- **关系**:\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\n\n### 实体2: 用户 (User)\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\n- **主要属性**:\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\n    - 个人简介、头像（UserProfile）。\n- **业务规则**:\n    - 用户名和电子邮件必须唯一。\n    - 密码必须加密存储。\n    - 不同角色拥有不同的操作权限。\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\n- **关系**:\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\n    - 与“反馈”：用户可以提供有用性反馈。\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\n\n### 实体3: 类别 (Category)\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\n- **主要属性**:\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\n- **业务规则**:\n    - 类别名称必须唯一。\n    - 类别计数应实时反映该类别下的服务器数量。\n- **关系**:\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\n\n### 实体4: 评论 (Review)\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\n- **主要属性**:\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\n- **业务规则**:\n    - 评论必须关联到一个用户和一个 MCP 服务器。\n    - 评论内容可能需要审核。\n    - 评分应影响 MCP 服务器的整体评分。\n- **关系**:\n    - 与“用户”：一个评论由一个用户发布。\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\n\n### 实体5: 反馈 (Feedback)\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\n- **主要属性**:\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\n- **业务规则**:\n    - 反馈可以匿名，也可以关联到用户。\n    - 反馈应被记录和聚合，用于平台改进。\n- **关系**:\n    - 可与“用户”关联。\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\n\n### 特别关注的MCP核心实体\n\n- **MCP服务器**:\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\n    - **版本管理**: 虽然文档未明确提及，但“上次更新”和“编辑/更新”功能暗示了服务器信息的版本概念，可能需要支持服务器代码和元数据的版本控制。\n- **工具定义**:\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\n- **用户角色**:\n    - **开发者**: 提交、管理和更新 MCP 服务器。\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\n    - **管理员**: 审核内容、管理用户、监控平台。\n- **质量评估**:\n    - **Asecurity**: 无已知漏洞。\n    - **Alicense**: 宽松许可证。\n    - **Aquality**: 确认可用。\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\n- **开发者生态**:\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\n\n## 3. 功能需求\n\n### 高优先级需求\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\n  - 验收标准:\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\n    - 列表分页/无限滚动，每个条目显示指定元数据。\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\n  - 功能分类: 发现与浏览\n\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\n  - 验收标准:\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\n    - 支持“深度搜索”和“新搜索”功能。\n  - 功能分类: 发现与浏览\n\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\n  - 验收标准:\n    - 排序下拉菜单提供所有排序选项。\n    - 选择不同排序选项时，服务器列表按预期重新排序。\n  - 功能分类: 发现与浏览\n\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\n  - 验收标准:\n    - 点击服务器列表中的条目，导航到其详情页面。\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\n    - “工具”部分清晰呈现名称、描述和示例提示。\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\n    - 显示 HTTP 连接 URL。\n  - 功能分类: 详情查看\n\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\n  - 验收标准:\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\n    - 提交后，服务器进入待审核状态，并收到确认消息。\n  - 功能分类: 内容管理\n\n### 中优先级需求\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\n  - 验收标准:\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\n  - 功能分类: 内容管理\n\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\n  - 验收标准:\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\n    - 用户能够编辑显示名称、联系信息。\n    - 支持密码找回/重置功能。\n  - 功能分类: 用户管理\n\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\n  - 验收标准:\n    - 页面上显示“这有用吗？是/否”按钮。\n    - 点击后，反馈被记录，按钮状态改变或消失。\n  - 功能分类: 社区互动\n\n### 低优先级需求\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\n  - 验收标准:\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\n    - 用户可以选择评分（1-5星）并输入评论。\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\n  - 功能分类: 社区互动\n\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\n  - 验收标准:\n    - 管理员登录后，访问管理仪表板。\n    - 能够暂停/删除用户账户。\n    - 能够审核和批准/拒绝服务器提交和评论。\n    - 能够添加/编辑/删除类别。\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\n  - 功能分类: 平台管理\n\n## 4. 非功能需求\n\n### 性能要求\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\n\n### 安全要求\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\n\n### 可用性要求\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\n\n### 可扩展性要求\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\n\n## 5. 业务规则\n\n1. **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\n   - 触发条件: 开发者提交新的 MCP 服务器。\n   - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\n\n2. **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\n   - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\n   - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\n\n3. **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\n   - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\n   - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\n\n4. **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\n   - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\n   - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\n\n5. **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\n   - 触发条件: 用户提交评论。\n   - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\n\n6. **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\n   - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\n   - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\n\n7. **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\n   - 触发条件: 用户尝试访问服务器提交/编辑功能。\n   - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\n\n## 6. 用户故事\n\n### 核心用户故事\n- **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\n  - 验收标准:\n    - 当我导航到服务器列表页面 (/mcp/servers) 时。\n    - 我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\n    - 我看到一个分页/可滚动的 MCP 服务器条目列表。\n    - 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\n    - 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\n\n- **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\n  - 验收标准:\n    - 当我位于服务器列表页面 (/mcp/servers) 时。\n    - 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\n    - URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\n    - 显示的列表仅显示标记为“Python”的服务器。\n    - “Python”的类别计数准确（例如，“2,436”）。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\n  - 验收标准:\n    - 当我位于服务器列表页面时。\n    - 当我在搜索栏中输入“区块链”并按 Enter 键时。\n    - 页面显示名称、描述或标签中包含“区块链”的服务器列表。\n    - “深度搜索”和“新搜索”按钮可用。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\n  - 验收标准:\n    - 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\n    - 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\n    - 页面突出显示：服务器标题、作者、标签、许可证、兼容性。\n    - 页面包含导航链接：概述、Schema、相关服务器、评论、评分。\n    - 页面提供“查看源代码”和“报告问题”链接。\n    - “您可以使用此服务器做什么？”部分详细说明了核心功能。\n    - “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\n    - “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\n    - “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\n    - “故障排除”部分列出了常见问题和解决方案。\n    - “工具”表格列出了每个工具的名称、描述和示例提示。\n    - 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\n    - “安装服务器”按钮和 HTTP 连接 URL 存在。\n    - “相关 MCP 服务器”部分显示了类似服务器的列表。\n    - “新 MCP 服务器”部分显示了最近添加的服务器。\n    - “这有用吗？是/否”反馈按钮可用。\n\n- **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\n  - 验收标准:\n    - 当我以开发者身份登录时。\n    - 当我点击“添加服务器”按钮时。\n    - 我看到一个多步骤表单，用于输入服务器详细信息。\n    - 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框。\n    - 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述。\n    - 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段。\n    - 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）。\n    - 我可以添加多个“故障排除提示”，包括问题和解决方案。\n    - 我可以选择相关的“类别”并添加“标签”。\n    - 当我提交表单时，服务器进入待审核状态。\n    - 我收到确认消息。\n\n## 7. 关键业务流程\n\n### 流程1: MCP 服务器发现与浏览\n- **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\n- **参与者**: 用户（包括最终用户、AI 代理）。\n- **主要步骤**:\n  1. **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\n  2. **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\n  3. **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\n  4. **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\n  5. **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\n  6. **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\n  7. **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\n- **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\n- **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\n\n### 流程2: MCP 服务器提交与发布\n- **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者使用其账户登录市场平台。\n  2. **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\n  3. **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\n  4. **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\n  5. **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\n  6. **审核结果反馈**:\n     - **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\n     - **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n  7. **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\n- **输入**: 开发者提交的服务器详细信息。\n- **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\n\n### 流程3: MCP 服务器信息更新\n- **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者登录市场平台。\n  2. **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\n  3. **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\n  4. **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\n  5. **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\n  6. **管理员审核更新**: 平台管理员对更新内容进行审查。\n  7. **更新结果反馈**:\n     - **审核通过**: 管理员批准更新，服务器信息在市场中更新。开发者收到更新成功的通知。\n     - **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n- **输入**: 开发者修改后的服务器详细信息。\n- **输出**: 更新后的 MCP 服务器信息。\n\n### 流程4: 用户反馈与社区互动\n- **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\n- **参与者**: 用户、MCP 服务器开发者、平台管理员。\n- **主要步骤**:\n  1. **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\n  2. **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\n  3. **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\n  4. **提交评论**: 用户提交评论。\n  5. **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\n  6. **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\n  7. **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\n- **输入**: 用户有用性选择、评论内容、评分。\n- **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。\n\n## 输出格式\n请以Markdown格式输出改进后的完整分析结果：\n\n# 改进后的业务分析报告\n"}, {"role": "user", "content": "\n基于之前的分析结果，请改进以下方面：\n\n['增加缺失的业务实体和功能需求', '更全面地覆盖原始文档内容']\n\n原始分析结果：\n{\n  \"raw_markdown\": \"# 处理后的开发规则\\n\\n## 1. 规则概述\\n- **规则来源**: rules.md\\n- **处理时间**: 2023年10月27日\\n- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发\\n- **主要改进**:\\n    - 统一并标准化了规则的分类和结构。\\n    - 明确了各层职责和依赖关系。\\n    - 细化了命名规范和文件组织原则。\\n    - 强调了测试、工程实践和Git提交的重要性。\\n    - 补充了数据库设计和模块内代码组织的关键约束。\\n\\n## 2. 核心原则\\n- **架构设计原则**:\\n    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。\\n    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。\\n    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。\\n    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。\\n- **开发方法论**:\\n    - **测试驱动**: 任何新功能或修改必须伴随自动化测试。\\n    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。\\n- **质量标准理念**:\\n    - **代码风格**: 严格遵循 PEP 8 规范。\\n    - **类型提示**: 强制要求所有函数签名和变量声明包含明确类型提示。\\n    - **文档语言**: 所有注释、文档字符串和文档内容必须使用**英文**编写。\\n    - **数据库设计**: 强制使用 UUID 作为主键，字段命名只反映业务含义，禁止技术后缀。\\n\\n## 3. 技术规范\\n- **技术栈要求**:\\n    - **Web 框架**: FastAPI\\n    - **数据验证与建模**: Pydantic\\n    - **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)\\n    - **测试框架**: Pytest\\n    - **HTTP 测试**: FastAPI TestClient\\n    - **Mocking**: unittest.mock 或 pytest-mock\\n- **框架使用规范**:\\n    - FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型。\\n    - SQLAlchemy ORM 模型使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`。\\n    - 禁止在应用启动时自动创建数据库表，所有数据库结构变更必须通过 Alembic 迁移脚本完成。\\n- **编码标准**:\\n    - 严格遵循 PEP 8 规范。\\n    - 强制使用类型提示。\\n    - 所有注释和文档使用英文。\\n\\n## 4. 架构约束\\n- **分层架构规则**:\\n    - **Interfaces (接口层)**: 处理 HTTP 请求、数据转换，通过依赖注入调用应用层服务。\\n    - **Application (应用层)**: 编排领域逻辑，协调用例，定义事务边界。\\n    - **Domain (领域层)**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\\n    - **Infrastructure (基础设施层)**: 实现与外部世界（数据库、文件系统、第三方 API）交互的技术细节，负责领域模型与 ORM 模型之间的转换。\\n- **模块组织方式**:\\n    - 项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。\\n    - 通用代码放置在根目录的 `common/` 文件夹下。\\n    - 模块内当包含多个相关但职责不同的业务子域时，通过文件命名前缀来区分，而不是创建深层嵌套的目录结构。\\n- **依赖关系约束**:\\n    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。\\n    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。\\n    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入任何 `fastapi`, `sqlalchemy` 或其他外部框架。\\n    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\\n    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可以被任何层级导入，但通用代码本身不能反向依赖任何业务模块。\\n    - **跨业务子域依赖**: 同模块内业务子域之间可以相互依赖，但必须通过应用层服务接口。跨模块则必须通过目标模块的应用层服务接口。\\n\\n## 5. 代码质量标准\\n- **代码风格规范**:\\n    - 严格遵循 PEP 8。\\n    - 强制使用类型提示。\\n    - 所有代码、注释、文档字符串和文档内容必须使用**英文**编写。\\n- **测试要求**:\\n    - **测试框架**: Pytest。\\n    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致，按业务模块组织。\\n    - **测试类型与分层**:\\n        - **单元测试**: 针对单一模块或函数，`Domain Layer` 测试实体业务方法，`Application Layer` 测试服务业务流程时需模拟（Mock）外部依赖。\\n        - **接口/集成测试**: 使用 `TestClient` 测试从 HTTP 端点到数据库的完整流程，验证各层集成。\\n    - **核心测试原则**:\\n        - 任何新功能或修改**必须**有相应的测试用例覆盖。\\n        - 所有测试用例**必须通过**。\\n        - 测试失败时，优先修复生产代码。\\n        - **严禁**随意修改测试用例，除非需求变更或测试用例设计缺陷。\\n    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用**英文**描述。\\n- **文档标准**:\\n    - 所有 API 端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码变更同步更新。\\n    - 所有注释、文档字符串和文档内容必须使用**英文**编写。\\n\\n## 6. 工程实践\\n- **开发流程**:\\n    - **代码生成约束与流程**: 遵循“Domain First”原则，从领域层开始，逐步实现应用层、基础设施层和接口层，最后集成到主应用。\\n    - **重构约束**:\\n        - 优先创建新文件进行修改，最后删除旧文件。\\n        - 重构前需分析完整上下文。\\n        - 重构后必须运行自动化测试，无测试覆盖部分应补充测试。\\n- **版本控制**:\\n    - **Git 提交规范**: 强制遵循约定式提交（Conventional Commits）规范。\\n        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。\\n        - **`type`**: 必须是 `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert` 之一。\\n        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。\\n        - **`subject`**: 简短描述，不超过 50 字符，祈使句，现在时态，首字母小写，不加句号，**英文**。\\n        - **`body`**: 详细描述，祈使句，现在时态，**英文**。\\n        - **`footer`**: 用于 `BREAKING CHANGE:` 或 `Closes #ISSUE_ID`。\\n    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。\\n- **部署规范**:\\n    - **依赖管理**:\\n        - 生成代码前检查 `requirements.txt`。\\n        - 添加新依赖后，立即执行 `pip freeze > requirements.txt`。\\n    - **环境变量管理**:\\n        - 优先复用现有环境变量。\\n        - 新增环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\\n    - **虚拟环境管理**:\\n        - 生成终端命令前提醒用户激活 `venv` 虚拟环境。\\n        - 提供激活命令参考。\\n    - **数据库迁移**:\\n        - **禁止**在应用启动时自动创建数据库表。\\n        - 所有数据库结构变更必须通过 Alembic 迁移脚本完成。\\n\\n## 7. 最佳实践\\n- **开发技巧**:\\n    - **模块内代码组织**:\\n        - 当模块内包含多个业务子域时，通过文件命名前缀区分，避免深层嵌套目录。\\n        - **强制要求**所有模块内的文件使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。\\n        - 命名格式：`{业务子域}_{层级类型}.py`。\\n        - 业务子域识别原则：基于数据模型、业务流程、外部依赖或生命周期差异。\\n        - 禁止使用类似 `_management` 的功能性命名后缀。\\n        - 禁止过度嵌套目录。\\n    - **共享组件处理**:\\n        - 模块内共享组件放置在 `shared/` 目录。\\n        - 跨模块共享组件放置在根目录的 `common/` 或 `shared/` 目录。\\n- **常见问题解决方案**:\\n    - **测试失败**: 优先修复生产代码，而非修改测试。\\n- **性能优化建议**:\\n    - 数据库索引设计：主键自动创建唯一索引，外键字段和常用查询条件字段应创建索引。\\n\\n## 8. 约束与限制\\n- **禁止的做法**:\\n    - 在 `Domain` 层导入任何外部框架（如 `fastapi`, `sqlalchemy`）。\\n    - 模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\\n    - 随意修改测试用例以通过测试。\\n    - 在应用启动时自动创建数据库表。\\n    - 数据库字段名包含技术实现细节（如 `id_uuid`, `name_str`）。\\n    - 使用通用文件名（如 `models.py`, `services.py`）而不带业务子域前缀。\\n    - 过度嵌套目录结构。\\n    - 使用类似 `_management` 的功能性命名后缀。\\n- **必须遵守的规则**:\\n    - 所有实体主键必须使用 UUID 类型。\\n    - 所有涉及实体 ID 的 Pydantic 模型字段和 FastAPI 路由参数必须声明为 `UUID` 类型。\\n    - 数据库字段名必须只反映业务含义。\\n    - 数据库表名使用复数形式的英文单词，下划线分隔，避免缩写。\\n    - 任何新功能或修改必须有相应的测试用例覆盖。\\n    - 所有测试用例必须通过。\\n    - 所有 Git 提交必须遵循约定式提交规范。\\n    - 任何代码生成或修改后，必须提醒用户运行相关自动化测试。\\n    - 任何需要新库的代码生成前，必须检查 `requirements.txt`。\\n    - 添加新依赖后，必须立即执行 `pip freeze > requirements.txt`。\\n    - 添加新的环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\\n    - 生成终端命令前必须提醒用户激活虚拟环境。\\n- **异常处理原则**:\\n    - 接口层负责处理 HTTP 错误。\\n    - 应用层定义事务边界。# 业务需求分析报告\\n\\n## 1. 业务概览\\n- **项目名称**: MCP 服务器市场平台\\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\\n- **核心目标**:\\n    1. 促进 MCP 服务器的发现、评估和集成。\\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\\n    5. 确保平台的可信度、安全性和质量。\\n- **目标用户**:\\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\\n- **价值主张**:\\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\\n\\n## 2. 核心实体分析\\n\\n### 实体1: MCP 服务器 (MCP_Server)\\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\\n- **主要属性**:\\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\\n    - **分类**: 标签/关键词、所属类别。\\n- **业务规则**:\\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\\n    - 服务器内的每个工具定义必须有唯一的工具名称。\\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\\n- **关系**:\\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\\n\\n### 实体2: 用户 (User)\\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\\n- **主要属性**:\\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\\n    - 个人简介、头像（UserProfile）。\\n- **业务规则**:\\n    - 用户名和电子邮件必须唯一。\\n    - 密码必须加密存储。\\n    - 不同角色拥有不同的操作权限。\\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\\n- **关系**:\\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\\n    - 与“反馈”：用户可以提供有用性反馈。\\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\\n\\n### 实体3: 类别 (Category)\\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\\n- **主要属性**:\\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\\n- **业务规则**:\\n    - 类别名称必须唯一。\\n    - 类别计数应实时反映该类别下的服务器数量。\\n- **关系**:\\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\\n\\n### 实体4: 评论 (Review)\\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\\n- **主要属性**:\\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\\n- **业务规则**:\\n    - 评论必须关联到一个用户和一个 MCP 服务器。\\n    - 评论内容可能需要审核。\\n    - 评分应影响 MCP 服务器的整体评分。\\n- **关系**:\\n    - 与“用户”：一个评论由一个用户发布。\\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\\n\\n### 实体5: 反馈 (Feedback)\\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\\n- **主要属性**:\\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\\n- **业务规则**:\\n    - 反馈可以匿名，也可以关联到用户。\\n    - 反馈应被记录和聚合，用于平台改进。\\n- **关系**:\\n    - 可与“用户”关联。\\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\\n\\n### 特别关注的MCP核心实体\\n\\n- **MCP服务器**:\\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\\n    - **版本管理**: 虽然文档未明确提及，但“上次更新”和“编辑/更新”功能暗示了服务器信息的版本概念，可能需要支持服务器代码和元数据的版本控制。\\n- **工具定义**:\\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\\n- **用户角色**:\\n    - **开发者**: 提交、管理和更新 MCP 服务器。\\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\\n    - **管理员**: 审核内容、管理用户、监控平台。\\n- **质量评估**:\\n    - **Asecurity**: 无已知漏洞。\\n    - **Alicense**: 宽松许可证。\\n    - **Aquality**: 确认可用。\\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\\n- **开发者生态**:\\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\\n\\n## 3. 功能需求\\n\\n### 高优先级需求\\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\\n  - 验收标准:\\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\\n    - 列表分页/无限滚动，每个条目显示指定元数据。\\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\\n  - 验收标准:\\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\\n    - 支持“深度搜索”和“新搜索”功能。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\\n  - 验收标准:\\n    - 排序下拉菜单提供所有排序选项。\\n    - 选择不同排序选项时，服务器列表按预期重新排序。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\\n  - 验收标准:\\n    - 点击服务器列表中的条目，导航到其详情页面。\\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\\n    - “工具”部分清晰呈现名称、描述和示例提示。\\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\\n    - 显示 HTTP 连接 URL。\\n  - 功能分类: 详情查看\\n\\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\\n  - 验收标准:\\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\\n    - 提交后，服务器进入待审核状态，并收到确认消息。\\n  - 功能分类: 内容管理\\n\\n### 中优先级需求\\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\\n  - 验收标准:\\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\\n  - 功能分类: 内容管理\\n\\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\\n  - 验收标准:\\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\\n    - 用户能够编辑显示名称、联系信息。\\n    - 支持密码找回/重置功能。\\n  - 功能分类: 用户管理\\n\\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\\n  - 验收标准:\\n    - 页面上显示“这有用吗？是/否”按钮。\\n    - 点击后，反馈被记录，按钮状态改变或消失。\\n  - 功能分类: 社区互动\\n\\n### 低优先级需求\\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\\n  - 验收标准:\\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\\n    - 用户可以选择评分（1-5星）并输入评论。\\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\\n  - 功能分类: 社区互动\\n\\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\\n  - 验收标准:\\n    - 管理员登录后，访问管理仪表板。\\n    - 能够暂停/删除用户账户。\\n    - 能够审核和批准/拒绝服务器提交和评论。\\n    - 能够添加/编辑/删除类别。\\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\\n  - 功能分类: 平台管理\\n\\n## 4. 非功能需求\\n\\n### 性能要求\\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\\n\\n### 安全要求\\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\\n\\n### 可用性要求\\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\\n\\n### 可扩展性要求\\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\\n\\n## 5. 业务规则\\n\\n1. **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\\n   - 触发条件: 开发者提交新的 MCP 服务器。\\n   - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\\n\\n2. **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\\n   - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\\n   - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\\n\\n3. **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\\n   - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\\n   - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\\n\\n4. **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\\n   - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\\n   - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\\n\\n5. **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\\n   - 触发条件: 用户提交评论。\\n   - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\\n\\n6. **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\\n   - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\\n   - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\\n\\n7. **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\\n   - 触发条件: 用户尝试访问服务器提交/编辑功能。\\n   - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\\n\\n## 6. 用户故事\\n\\n### 核心用户故事\\n- **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\\n  - 验收标准:\\n    - 当我导航到服务器列表页面 (/mcp/servers) 时。\\n    - 我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\\n    - 我看到一个分页/可滚动的 MCP 服务器条目列表。\\n    - 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\\n    - 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\\n\\n- **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\\n  - 验收标准:\\n    - 当我位于服务器列表页面 (/mcp/servers) 时。\\n    - 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\\n    - URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\\n    - 显示的列表仅显示标记为“Python”的服务器。\\n    - “Python”的类别计数准确（例如，“2,436”）。\\n    - 每个显示的服务器条目都符合 US-001 的标准。\\n\\n- **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\\n  - 验收标准:\\n    - 当我位于服务器列表页面时。\\n    - 当我在搜索栏中输入“区块链”并按 Enter 键时。\\n    - 页面显示名称、描述或标签中包含“区块链”的服务器列表。\\n    - “深度搜索”和“新搜索”按钮可用。\\n    - 每个显示的服务器条目都符合 US-001 的标准。\\n\\n- **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\\n  - 验收标准:\\n    - 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\\n    - 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\\n    - 页面突出显示：服务器标题、作者、标签、许可证、兼容性。\\n    - 页面包含导航链接：概述、Schema、相关服务器、评论、评分。\\n    - 页面提供“查看源代码”和“报告问题”链接。\\n    - “您可以使用此服务器做什么？”部分详细说明了核心功能。\\n    - “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\\n    - “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\\n    - “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\\n    - “故障排除”部分列出了常见问题和解决方案。\\n    - “工具”表格列出了每个工具的名称、描述和示例提示。\\n    - 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\\n    - “安装服务器”按钮和 HTTP 连接 URL 存在。\\n    - “相关 MCP 服务器”部分显示了类似服务器的列表。\\n    - “新 MCP 服务器”部分显示了最近添加的服务器。\\n    - “这有用吗？是/否”反馈按钮可用。\\n\\n- **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\\n  - 验收标准:\\n    - 当我以开发者身份登录时。\\n    - 当我点击“添加服务器”按钮时。\\n    - 我看到一个多步骤表单，用于输入服务器详细信息。\\n    - 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框。\\n    - 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述。\\n    - 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段。\\n    - 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）。\\n    - 我可以添加多个“故障排除提示”，包括问题和解决方案。\\n    - 我可以选择相关的“类别”并添加“标签”。\\n    - 当我提交表单时，服务器进入待审核状态。\\n    - 我收到确认消息。\\n\\n## 7. 关键业务流程\\n\\n### 流程1: MCP 服务器发现与浏览\\n- **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\\n- **参与者**: 用户（包括最终用户、AI 代理）。\\n- **主要步骤**:\\n  1. **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\\n  2. **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\\n  3. **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\\n  4. **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\\n  5. **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\\n  6. **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\\n  7. **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\\n- **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\\n- **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\\n\\n### 流程2: MCP 服务器提交与发布\\n- **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\\n- **参与者**: 开发者、平台管理员。\\n- **主要步骤**:\\n  1. **开发者登录**: 开发者使用其账户登录市场平台。\\n  2. **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\\n  3. **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\\n  4. **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\\n  5. **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\\n  6. **审核结果反馈**:\\n     - **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\\n     - **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\\n  7. **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\\n- **输入**: 开发者提交的服务器详细信息。\\n- **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\\n\\n### 流程3: MCP 服务器信息更新\\n- **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效。\\n- **参与者**: 开发者、平台管理员。\\n- **主要步骤**:\\n  1. **开发者登录**: 开发者登录市场平台。\\n  2. **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\\n  3. **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\\n  4. **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\\n  5. **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\\n  6. **管理员审核更新**: 平台管理员对更新内容进行审查。\\n  7. **更新结果反馈**:\\n     - **审核通过**: 管理员批准更新，服务器信息在市场中更新。开发者收到更新成功的通知。\\n     - **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\\n- **输入**: 开发者修改后的服务器详细信息。\\n- **输出**: 更新后的 MCP 服务器信息。\\n\\n### 流程4: 用户反馈与社区互动\\n- **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\\n- **参与者**: 用户、MCP 服务器开发者、平台管理员。\\n- **主要步骤**:\\n  1. **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\\n  2. **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\\n  3. **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\\n  4. **提交评论**: 用户提交评论。\\n  5. **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\\n  6. **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\\n  7. **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\\n- **输入**: 用户有用性选择、评论内容、评分。\\n- **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。\",\n  \"content_type\": \"markdown\",\n  \"sections\": {\n    \"规则概述\": \"- **规则来源**: rules.md\\n- **处理时间**: 2023年10月27日\\n- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发\\n- **主要改进**:\\n    - 统一并标准化了规则的分类和结构。\\n    - 明确了各层职责和依赖关系。\\n    - 细化了命名规范和文件组织原则。\\n    - 强调了测试、工程实践和Git提交的重要性。\\n    - 补充了数据库设计和模块内代码组织的关键约束。\",\n    \"核心原则\": \"- **架构设计原则**:\\n    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。\\n    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。\\n    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。\\n    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。\\n- **开发方法论**:\\n    - **测试驱动**: 任何新功能或修改必须伴随自动化测试。\\n    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。\\n- **质量标准理念**:\\n    - **代码风格**: 严格遵循 PEP 8 规范。\\n    - **类型提示**: 强制要求所有函数签名和变量声明包含明确类型提示。\\n    - **文档语言**: 所有注释、文档字符串和文档内容必须使用**英文**编写。\\n    - **数据库设计**: 强制使用 UUID 作为主键，字段命名只反映业务含义，禁止技术后缀。\",\n    \"技术规范\": \"- **技术栈要求**:\\n    - **Web 框架**: FastAPI\\n    - **数据验证与建模**: Pydantic\\n    - **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)\\n    - **测试框架**: Pytest\\n    - **HTTP 测试**: FastAPI TestClient\\n    - **Mocking**: unittest.mock 或 pytest-mock\\n- **框架使用规范**:\\n    - FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型。\\n    - SQLAlchemy ORM 模型使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`。\\n    - 禁止在应用启动时自动创建数据库表，所有数据库结构变更必须通过 Alembic 迁移脚本完成。\\n- **编码标准**:\\n    - 严格遵循 PEP 8 规范。\\n    - 强制使用类型提示。\\n    - 所有注释和文档使用英文。\",\n    \"架构约束\": \"- **分层架构规则**:\\n    - **Interfaces (接口层)**: 处理 HTTP 请求、数据转换，通过依赖注入调用应用层服务。\\n    - **Application (应用层)**: 编排领域逻辑，协调用例，定义事务边界。\\n    - **Domain (领域层)**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\\n    - **Infrastructure (基础设施层)**: 实现与外部世界（数据库、文件系统、第三方 API）交互的技术细节，负责领域模型与 ORM 模型之间的转换。\\n- **模块组织方式**:\\n    - 项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。\\n    - 通用代码放置在根目录的 `common/` 文件夹下。\\n    - 模块内当包含多个相关但职责不同的业务子域时，通过文件命名前缀来区分，而不是创建深层嵌套的目录结构。\\n- **依赖关系约束**:\\n    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。\\n    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。\\n    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入任何 `fastapi`, `sqlalchemy` 或其他外部框架。\\n    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\\n    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可以被任何层级导入，但通用代码本身不能反向依赖任何业务模块。\\n    - **跨业务子域依赖**: 同模块内业务子域之间可以相互依赖，但必须通过应用层服务接口。跨模块则必须通过目标模块的应用层服务接口。\",\n    \"代码质量标准\": \"- **代码风格规范**:\\n    - 严格遵循 PEP 8。\\n    - 强制使用类型提示。\\n    - 所有代码、注释、文档字符串和文档内容必须使用**英文**编写。\\n- **测试要求**:\\n    - **测试框架**: Pytest。\\n    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致，按业务模块组织。\\n    - **测试类型与分层**:\\n        - **单元测试**: 针对单一模块或函数，`Domain Layer` 测试实体业务方法，`Application Layer` 测试服务业务流程时需模拟（Mock）外部依赖。\\n        - **接口/集成测试**: 使用 `TestClient` 测试从 HTTP 端点到数据库的完整流程，验证各层集成。\\n    - **核心测试原则**:\\n        - 任何新功能或修改**必须**有相应的测试用例覆盖。\\n        - 所有测试用例**必须通过**。\\n        - 测试失败时，优先修复生产代码。\\n        - **严禁**随意修改测试用例，除非需求变更或测试用例设计缺陷。\\n    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用**英文**描述。\\n- **文档标准**:\\n    - 所有 API 端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码变更同步更新。\\n    - 所有注释、文档字符串和文档内容必须使用**英文**编写。\",\n    \"工程实践\": \"- **开发流程**:\\n    - **代码生成约束与流程**: 遵循“Domain First”原则，从领域层开始，逐步实现应用层、基础设施层和接口层，最后集成到主应用。\\n    - **重构约束**:\\n        - 优先创建新文件进行修改，最后删除旧文件。\\n        - 重构前需分析完整上下文。\\n        - 重构后必须运行自动化测试，无测试覆盖部分应补充测试。\\n- **版本控制**:\\n    - **Git 提交规范**: 强制遵循约定式提交（Conventional Commits）规范。\\n        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。\\n        - **`type`**: 必须是 `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert` 之一。\\n        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。\\n        - **`subject`**: 简短描述，不超过 50 字符，祈使句，现在时态，首字母小写，不加句号，**英文**。\\n        - **`body`**: 详细描述，祈使句，现在时态，**英文**。\\n        - **`footer`**: 用于 `BREAKING CHANGE:` 或 `Closes #ISSUE_ID`。\\n    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。\\n- **部署规范**:\\n    - **依赖管理**:\\n        - 生成代码前检查 `requirements.txt`。\\n        - 添加新依赖后，立即执行 `pip freeze > requirements.txt`。\\n    - **环境变量管理**:\\n        - 优先复用现有环境变量。\\n        - 新增环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\\n    - **虚拟环境管理**:\\n        - 生成终端命令前提醒用户激活 `venv` 虚拟环境。\\n        - 提供激活命令参考。\\n    - **数据库迁移**:\\n        - **禁止**在应用启动时自动创建数据库表。\\n        - 所有数据库结构变更必须通过 Alembic 迁移脚本完成。\",\n    \"最佳实践\": \"- **开发技巧**:\\n    - **模块内代码组织**:\\n        - 当模块内包含多个业务子域时，通过文件命名前缀区分，避免深层嵌套目录。\\n        - **强制要求**所有模块内的文件使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。\\n        - 命名格式：`{业务子域}_{层级类型}.py`。\\n        - 业务子域识别原则：基于数据模型、业务流程、外部依赖或生命周期差异。\\n        - 禁止使用类似 `_management` 的功能性命名后缀。\\n        - 禁止过度嵌套目录。\\n    - **共享组件处理**:\\n        - 模块内共享组件放置在 `shared/` 目录。\\n        - 跨模块共享组件放置在根目录的 `common/` 或 `shared/` 目录。\\n- **常见问题解决方案**:\\n    - **测试失败**: 优先修复生产代码，而非修改测试。\\n- **性能优化建议**:\\n    - 数据库索引设计：主键自动创建唯一索引，外键字段和常用查询条件字段应创建索引。\",\n    \"约束与限制\": \"- **禁止的做法**:\\n    - 在 `Domain` 层导入任何外部框架（如 `fastapi`, `sqlalchemy`）。\\n    - 模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\\n    - 随意修改测试用例以通过测试。\\n    - 在应用启动时自动创建数据库表。\\n    - 数据库字段名包含技术实现细节（如 `id_uuid`, `name_str`）。\\n    - 使用通用文件名（如 `models.py`, `services.py`）而不带业务子域前缀。\\n    - 过度嵌套目录结构。\\n    - 使用类似 `_management` 的功能性命名后缀。\\n- **必须遵守的规则**:\\n    - 所有实体主键必须使用 UUID 类型。\\n    - 所有涉及实体 ID 的 Pydantic 模型字段和 FastAPI 路由参数必须声明为 `UUID` 类型。\\n    - 数据库字段名必须只反映业务含义。\\n    - 数据库表名使用复数形式的英文单词，下划线分隔，避免缩写。\\n    - 任何新功能或修改必须有相应的测试用例覆盖。\\n    - 所有测试用例必须通过。\\n    - 所有 Git 提交必须遵循约定式提交规范。\\n    - 任何代码生成或修改后，必须提醒用户运行相关自动化测试。\\n    - 任何需要新库的代码生成前，必须检查 `requirements.txt`。\\n    - 添加新依赖后，必须立即执行 `pip freeze > requirements.txt`。\\n    - 添加新的环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\\n    - 生成终端命令前必须提醒用户激活虚拟环境。\\n- **异常处理原则**:\\n    - 接口层负责处理 HTTP 错误。\\n    - 应用层定义事务边界。# 业务需求分析报告\",\n    \"业务概览\": \"- **项目名称**: MCP 服务器市场平台\\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\\n- **核心目标**:\\n    1. 促进 MCP 服务器的发现、评估和集成。\\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\\n    5. 确保平台的可信度、安全性和质量。\\n- **目标用户**:\\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\\n- **价值主张**:\\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\",\n    \"核心实体分析\": \"### 实体1: MCP 服务器 (MCP_Server)\\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\\n- **主要属性**:\\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\\n    - **分类**: 标签/关键词、所属类别。\\n- **业务规则**:\\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\\n    - 服务器内的每个工具定义必须有唯一的工具名称。\\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\\n- **关系**:\\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\\n\\n### 实体2: 用户 (User)\\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\\n- **主要属性**:\\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\\n    - 个人简介、头像（UserProfile）。\\n- **业务规则**:\\n    - 用户名和电子邮件必须唯一。\\n    - 密码必须加密存储。\\n    - 不同角色拥有不同的操作权限。\\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\\n- **关系**:\\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\\n    - 与“反馈”：用户可以提供有用性反馈。\\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\\n\\n### 实体3: 类别 (Category)\\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\\n- **主要属性**:\\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\\n- **业务规则**:\\n    - 类别名称必须唯一。\\n    - 类别计数应实时反映该类别下的服务器数量。\\n- **关系**:\\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\\n\\n### 实体4: 评论 (Review)\\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\\n- **主要属性**:\\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\\n- **业务规则**:\\n    - 评论必须关联到一个用户和一个 MCP 服务器。\\n    - 评论内容可能需要审核。\\n    - 评分应影响 MCP 服务器的整体评分。\\n- **关系**:\\n    - 与“用户”：一个评论由一个用户发布。\\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\\n\\n### 实体5: 反馈 (Feedback)\\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\\n- **主要属性**:\\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\\n- **业务规则**:\\n    - 反馈可以匿名，也可以关联到用户。\\n    - 反馈应被记录和聚合，用于平台改进。\\n- **关系**:\\n    - 可与“用户”关联。\\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\\n\\n### 特别关注的MCP核心实体\\n\\n- **MCP服务器**:\\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\\n    - **版本管理**: 虽然文档未明确提及，但“上次更新”和“编辑/更新”功能暗示了服务器信息的版本概念，可能需要支持服务器代码和元数据的版本控制。\\n- **工具定义**:\\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\\n- **用户角色**:\\n    - **开发者**: 提交、管理和更新 MCP 服务器。\\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\\n    - **管理员**: 审核内容、管理用户、监控平台。\\n- **质量评估**:\\n    - **Asecurity**: 无已知漏洞。\\n    - **Alicense**: 宽松许可证。\\n    - **Aquality**: 确认可用。\\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\\n- **开发者生态**:\\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\",\n    \"功能需求\": \"### 高优先级需求\\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\\n  - 验收标准:\\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\\n    - 列表分页/无限滚动，每个条目显示指定元数据。\\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\\n  - 验收标准:\\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\\n    - 支持“深度搜索”和“新搜索”功能。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\\n  - 验收标准:\\n    - 排序下拉菜单提供所有排序选项。\\n    - 选择不同排序选项时，服务器列表按预期重新排序。\\n  - 功能分类: 发现与浏览\\n\\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\\n  - 验收标准:\\n    - 点击服务器列表中的条目，导航到其详情页面。\\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\\n    - “工具”部分清晰呈现名称、描述和示例提示。\\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\\n    - 显示 HTTP 连接 URL。\\n  - 功能分类: 详情查看\\n\\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\\n  - 验收标准:\\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\\n    - 提交后，服务器进入待审核状态，并收到确认消息。\\n  - 功能分类: 内容管理\\n\\n### 中优先级需求\\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\\n  - 验收标准:\\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\\n  - 功能分类: 内容管理\\n\\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\\n  - 验收标准:\\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\\n    - 用户能够编辑显示名称、联系信息。\\n    - 支持密码找回/重置功能。\\n  - 功能分类: 用户管理\\n\\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\\n  - 验收标准:\\n    - 页面上显示“这有用吗？是/否”按钮。\\n    - 点击后，反馈被记录，按钮状态改变或消失。\\n  - 功能分类: 社区互动\\n\\n### 低优先级需求\\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\\n  - 验收标准:\\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\\n    - 用户可以选择评分（1-5星）并输入评论。\\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\\n  - 功能分类: 社区互动\\n\\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\\n  - 验收标准:\\n    - 管理员登录后，访问管理仪表板。\\n    - 能够暂停/删除用户账户。\\n    - 能够审核和批准/拒绝服务器提交和评论。\\n    - 能够添加/编辑/删除类别。\\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\\n  - 功能分类: 平台管理\",\n    \"非功能需求\": \"### 性能要求\\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\\n\\n### 安全要求\\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\\n\\n### 可用性要求\\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\\n\\n### 可扩展性要求\\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\",\n    \"业务规则\": \"1. **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\\n   - 触发条件: 开发者提交新的 MCP 服务器。\\n   - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\\n\\n2. **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\\n   - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\\n   - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\\n\\n3. **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\\n   - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\\n   - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\\n\\n4. **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\\n   - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\\n   - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\\n\\n5. **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\\n   - 触发条件: 用户提交评论。\\n   - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\\n\\n6. **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\\n   - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\\n   - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\\n\\n7. **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\\n   - 触发条件: 用户尝试访问服务器提交/编辑功能。\\n   - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\",\n    \"用户故事\": \"### 核心用户故事\\n- **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\\n  - 验收标准:\\n    - 当我导航到服务器列表页面 (/mcp/servers) 时。\\n    - 我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\\n    - 我看到一个分页/可滚动的 MCP 服务器条目列表。\\n    - 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\\n    - 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\\n\\n- **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\\n  - 验收标准:\\n    - 当我位于服务器列表页面 (/mcp/servers) 时。\\n    - 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\\n    - URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\\n    - 显示的列表仅显示标记为“Python”的服务器。\\n    - “Python”的类别计数准确（例如，“2,436”）。\\n    - 每个显示的服务器条目都符合 US-001 的标准。\\n\\n- **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\\n  - 验收标准:\\n    - 当我位于服务器列表页面时。\\n    - 当我在搜索栏中输入“区块链”并按 Enter 键时。\\n    - 页面显示名称、描述或标签中包含“区块链”的服务器列表。\\n    - “深度搜索”和“新搜索”按钮可用。\\n    - 每个显示的服务器条目都符合 US-001 的标准。\\n\\n- **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\\n  - 验收标准:\\n    - 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\\n    - 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\\n    - 页面突出显示：服务器标题、作者、标签、许可证、兼容性。\\n    - 页面包含导航链接：概述、Schema、相关服务器、评论、评分。\\n    - 页面提供“查看源代码”和“报告问题”链接。\\n    - “您可以使用此服务器做什么？”部分详细说明了核心功能。\\n    - “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\\n    - “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\\n    - “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\\n    - “故障排除”部分列出了常见问题和解决方案。\\n    - “工具”表格列出了每个工具的名称、描述和示例提示。\\n    - 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\\n    - “安装服务器”按钮和 HTTP 连接 URL 存在。\\n    - “相关 MCP 服务器”部分显示了类似服务器的列表。\\n    - “新 MCP 服务器”部分显示了最近添加的服务器。\\n    - “这有用吗？是/否”反馈按钮可用。\\n\\n- **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\\n  - 验收标准:\\n    - 当我以开发者身份登录时。\\n    - 当我点击“添加服务器”按钮时。\\n    - 我看到一个多步骤表单，用于输入服务器详细信息。\\n    - 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框。\\n    - 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述。\\n    - 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段。\\n    - 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）。\\n    - 我可以添加多个“故障排除提示”，包括问题和解决方案。\\n    - 我可以选择相关的“类别”并添加“标签”。\\n    - 当我提交表单时，服务器进入待审核状态。\\n    - 我收到确认消息。\",\n    \"关键业务流程\": \"### 流程1: MCP 服务器发现与浏览\\n- **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\\n- **参与者**: 用户（包括最终用户、AI 代理）。\\n- **主要步骤**:\\n  1. **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\\n  2. **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\\n  3. **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\\n  4. **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\\n  5. **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\\n  6. **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\\n  7. **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\\n- **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\\n- **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\\n\\n### 流程2: MCP 服务器提交与发布\\n- **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\\n- **参与者**: 开发者、平台管理员。\\n- **主要步骤**:\\n  1. **开发者登录**: 开发者使用其账户登录市场平台。\\n  2. **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\\n  3. **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\\n  4. **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\\n  5. **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\\n  6. **审核结果反馈**:\\n     - **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\\n     - **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\\n  7. **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\\n- **输入**: 开发者提交的服务器详细信息。\\n- **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\\n\\n### 流程3: MCP 服务器信息更新\\n- **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效。\\n- **参与者**: 开发者、平台管理员。\\n- **主要步骤**:\\n  1. **开发者登录**: 开发者登录市场平台。\\n  2. **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\\n  3. **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\\n  4. **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\\n  5. **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\\n  6. **管理员审核更新**: 平台管理员对更新内容进行审查。\\n  7. **更新结果反馈**:\\n     - **审核通过**: 管理员批准更新，服务器信息在市场中更新。开发者收到更新成功的通知。\\n     - **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\\n- **输入**: 开发者修改后的服务器详细信息。\\n- **输出**: 更新后的 MCP 服务器信息。\\n\\n### 流程4: 用户反馈与社区互动\\n- **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\\n- **参与者**: 用户、MCP 服务器开发者、平台管理员。\\n- **主要步骤**:\\n  1. **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\\n  2. **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\\n  3. **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\\n  4. **提交评论**: 用户提交评论。\\n  5. **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\\n  6. **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\\n  7. **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\\n- **输入**: 用户有用性选择、评论内容、评分。\\n- **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。\"\n  }\n}\n\n原始PRD内容：\n\n\n# **构建 MCP 服务器市场功能报告**\n\n## **1\\. 执行摘要**\n\n本报告旨在为开发一个 Model Context Protocol (MCP) 服务器市场平台提供详细的功能需求说明书、领域驱动设计（DDD）模型以及敏捷用户故事。该平台的目标是促进 MCP 服务器的发现、评估和集成，并借鉴现有平台（如 Glama.ai）的能力，同时加以增强。\n\n分析发现，MCP 生态系统是一个复杂的互联网络，需要强大的搜索、过滤、详细的服务器信息展示以及社区贡献功能。核心的 MCP 服务器 实体具有丰富的属性，这使得采用成熟的领域驱动设计方法变得至关重要。\n\n基于这些发现，建议采用分阶段的敏捷开发方法，利用详细的用户故事指导 AI Agent 进行代码生成。初期应重点关注核心服务器列表和发现功能，随后再逐步引入服务器提交和社区互动功能。\n\n## **2\\. Model Context Protocol (MCP) 生态系统介绍**\n\n### **2.1. MCP 的目的与核心概念**\n\nModel Context Protocol (MCP) 是一种开放协议，旨在标准化应用程序如何向大型语言模型 (LLM) 提供上下文信息 1。它类似于 AI 应用程序的 USB-C 端口，提供了一种标准化的方法来连接 AI 模型与各种数据源和工具。\n\nMCP 的核心目的是促进在 LLM 之上构建 AI 代理和复杂工作流。它解决了 LLM 频繁需要与数据和工具集成的问题，通过提供：不断增长的预构建集成列表，LLM 可以直接插入；在不同 LLM 提供商和供应商之间切换的灵活性；以及在现有基础设施内保护数据的最佳实践 1。\n\n这种协议的出现，反映了 LLM 越来越需要超越其静态训练数据，与实时、动态的信息和外部系统进行交互。MCP 旨在解决 LLM 的“上下文问题”，使其能够访问和利用外部数据与工具来执行更复杂的任务。这种标准化促进了一个生态系统的形成，其中各种应用程序和数据源可以轻松地“插入”并增强 LLM 的能力，从而加速了复杂 AI 代理和工作流的开发。它还有助于减少 LLM 供应商的锁定效应，并促进互操作性。\n\n### **2.2. 关键组件与通用架构**\n\nMCP 的核心在于其客户端-服务器架构，允许一个宿主应用程序连接到多个服务器 1。该架构的关键组件包括：\n\n* **MCP 宿主 (MCP Hosts)**：这些是需要通过 MCP 访问数据的程序或应用程序，例如 Claude Desktop、集成开发环境 (IDE) 或其他 AI 工具 1。  \n* **MCP 客户端 (MCP Clients)**：这些是协议客户端，与 MCP 服务器保持一对一连接 1。  \n* **MCP 服务器 (MCP Servers)**：这些是轻量级程序，每个程序都旨在通过标准化的 Model Context Protocol 暴露特定功能 1。  \n  * MCP 服务器提供的功能主要分为三类：**资源**（客户端可以读取的文件式数据，如 API 响应或文件内容）、**工具**（经用户批准后可由 LLM 调用的函数）和**提示**（旨在帮助用户完成特定任务的预编写模板） 2。例如，一个天气服务器可以暴露  \n    get-alerts（获取美国某个州的天气警报）和 get-forecast（获取特定经纬度的天气预报）这两个工具 2。  \n  * 在开发层面，MCP 服务器支持**自动工具定义**。使用 Python 的 FastMCP 等 SDK，工具定义可以从 Python 类型提示和文档字符串中自动生成，从而简化了 MCP 工具的创建和维护。同样，Java 的 Spring AI 中的 @Tool 注解可以自动将服务注册为工具 2。  \n  * MCP 服务器与客户端之间的**通信传输**主要使用 stdio（标准输入/输出）2。  \n  * **集成** MCP 服务器通常通过将服务器的命令和参数添加到客户端的配置文件（例如 claude\\_desktop\\_config.json）中来完成 2。  \n  * **调试**对于 MCP 服务器的连接和故障排除至关重要，Claude Desktop 会将 MCP 相关的日志写入 mcp.log 和 mcp-server-SERVERNAME.log 文件 2。  \n* **本地数据源 (Local Data Sources)**：指用户计算机上存在的数据，包括文件、数据库和服务，MCP 服务器可以安全地访问这些数据 1。  \n* **远程服务 (Remote Services)**：指通过互联网可访问的外部系统，通常通过 API，MCP 服务器可以连接到这些系统 1。\n\n这种 MCP 生态系统本质上是分布式的，每个服务器都提供特定的功能。为了使这个生态系统蓬勃发展，用户（包括开发者、AI 代理和最终用户）必须有一种高效的方式来发现可用的服务器，了解它们的功能，并学习如何集成它们。MCP 服务器市场网站正是扮演了这一角色，它充当了分散式 MCP 服务器网络的中央注册和发现门户。如果没有这样一个平台，服务器的发现能力将受到严重限制，从而阻碍 MCP 的采用和实用性。这表明，该市场不仅仅是一个静态列表，它是一个连接到不断发展和演变的协议的动态接口。\n\n### **2.3. MCP 服务器市场概念概述**\n\nMCP 服务器市场是一个集中式目录，用于展示“开源 MCP 服务器”，这些服务器通过文件访问、数据库连接、API 集成以及其他上下文服务来扩展 AI 能力 3。它为用户提供了一个平台，以查找、评估并可能安装或集成这些服务器。\n\n鉴于 MCP 服务器可以访问本地数据和远程服务，并且旨在集成到关键的 AI 工作流中，信任和质量变得至关重要。市场平台通过提供“质量/安全/许可证指标”（例如“Asecurity”、“Alicense”、“Aquality”）4 和反馈机制（例如“这有用吗？是/否”按钮）3，隐含地承担了对这些服务器进行策展和验证的角色。这表明该平台不仅仅是一个列出服务，更是一个把关者和信任建立者，这对于开放协议的广泛采用至关重要，因为在开放协议中任何人都可以贡献。此功能对于降低将第三方代码集成到敏感 AI 应用程序中的风险至关重要。\n\n## **3\\. 现有 MCP 服务器市场 (Glama.ai) 功能分析**\n\n### **3.1. 首页功能与用户体验**\n\n尽管提供的资料没有直接详细说明 Glama.ai 首页的具体功能或显示方式，但其服务器列表页面的存在 3 表明，首页很可能作为核心功能的明确入口点。首页可能主要用于引导用户进入服务器列表，并可能突出显示特色服务器、最新更新或关键类别。\n\n### **3.2. MCP 服务器列表与发现**\n\n#### **3.2.1. 显示元素与信息**\n\n服务器列表页面显示了“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）以及“上次更新”的时间戳 3。每个服务器条目都包含丰富的详细信息，以帮助用户快速了解其特性：\n\n* **服务器名称**：通常指示其主要功能，并作为可点击的链接 5。  \n* **官方标签**：许多条目标有“官方”标签，表明它们由相关组织正式支持或开发 5。  \n* **开发者/组织**：开发或维护服务器的实体名称，也是可点击的链接 5。  \n* **质量/安全/许可证指标**：每个服务器都有“Asecurity”、“Alicense”和“Aquality”的指标，提供有关服务器安全性、许可和整体质量的信息 5。  \n* **描述**：简要说明 MCP 服务器的功能以及它提供的数据或功能类型 5。  \n* **上次更新**：指示服务器上次更新的时间戳 5。  \n* **编程语言**：指定服务器所使用的编程语言，例如“TypeScript”、“Python”5。  \n* **许可证类型**：服务器发布的软件许可证类型，例如“MIT 许可证”、“Apache 2.0”5。  \n* **操作系统兼容性**：显示与不同操作系统兼容的图标或文本，例如“Apple”和“Linux”5。\n\n每个服务器条目中包含的丰富元数据（如语言、许可证、操作系统兼容性、质量指标）远不止简单的命名。这些广泛的元数据对于用户快速评估服务器的相关性、可信度和技术适用性至关重要，而无需点击进入每个详细页面。对于生成代码的 AI Agent 而言，这种结构化的元数据在根据预定义条件（例如，“查找具有 MIT 许可证且支持 Linux 并具有 Asecurity 评级的 Python 服务器”）过滤和选择适当服务器时具有不可估量的价值。这表明，市场的价值与其服务器元数据的完整性和准确性密切相关。\n\n#### **3.2.2. 筛选、排序与分类机制**\n\n服务器列表页面提供了多种排序选项，包括：搜索相关性（默认排序选项）、添加日期、更新日期、每周下载量、GitHub 星数以及最近的 GitHub 星数 3。\n\n此外，页面还列出了众多类别，每个类别旁边都显示有计数，这些类别可作为过滤选项。例如，包括 Python (2,436)、远程 (2,425)、TypeScript (1,988)、开发工具 (1,703)、数据库 (715)、RAG 系统 (637)、代码执行 (577)、代理编排 (549)、自治代理 (546)、官方 (454)、网页抓取 (304)、云平台 (298)、通信 (250)、浏览器自动化 (241)、内容管理系统 (230)、项目管理 (216)、知识与记忆 (213)、金融 (208)、文档访问 (207)、文件系统 (206)、区块链 (200)、图像与视频处理 (167)、API 测试 (163)、代码分析 (158)、安全 (155) 等 3。\n\n这些类别被组织成导航菜单，并有特定的类别页面（例如 /categories/search、/categories/databases）显示与该类别相关的服务器 6。在特定类别内，服务器以连续可滚动的列表形式呈现，没有进一步的子分类或过滤选项 6。\n\n这种广泛的类别列表（超过 30 个）表明存在一个高度细粒度的分类系统。虽然这允许进行精确过滤，但它也给用户导航和发现带来了挑战。平台必须在为特定需求（例如“区块链”、“RAG 系统”）提供足够详细信息与确保用户不会感到信息过载之间取得平衡。这可能需要分层分类或强大的标签系统，以允许进行广泛和狭窄的搜索。类别中较高的计数也意味着服务器数量庞大，这进一步强调了高效过滤的必要性。\n\n#### **3.2.3. 搜索功能与结果呈现**\n\n页面上提供了“深度搜索”和“新搜索”按钮，这表明除了简单的关键词匹配之外，还存在高级搜索功能 3。搜索结果页面（例如\n\n/categories/search）显示了“用于搜索的 MCP 服务器”列表，每个条目都包含标准的服务器详细信息 5。\n\n该页面隐含的搜索功能是“使用自然语言访问和查询各种来源的数据”的能力 5。搜索类别中服务器的描述突出了其多样化的功能，例如：按维度过滤和检索指标（Clarity Data Export MCP Server）、实时公共网络数据访问（Bright Data MCP）、特定领域搜索（Tavily MCP Server）、SEO 数据访问（DataForSEO MCP Server）、区块链数据查询（Satstream MCP Server）、经济数据访问（MCP-FREDAPI）等 5。\n\n该平台提供了以人为中心的搜索和过滤功能（关键词、类别、排序），但同时也列出了其主要目的在于使 AI Agent 执行自然语言搜索的服务器。这形成了一种有趣的二元性：市场平台本身可供人类搜索，而市场上的产品（MCP 服务器）则使 AI 能够搜索其他数据。这意味着平台自身的搜索功能必须足够强大，以允许用户找到“正确的 AI 搜索工具”，从而使元搜索功能成为一项关键特性。这也表明，该平台最终可能会为其自身的服务器目录集成 AI 驱动的搜索功能。\n\n### **3.3. 单个 MCP 服务器详情视图**\n\n单个服务器的详细页面（例如 ChainGPT MCP）提供了服务器的全面概述，包括其目的、功能、设置说明、安装方法、故障排除提示、可用工具和相关资源 4。\n\n页面上突出显示的信息包括：\n\n* **服务器标识**：服务器标题（“ChainGPT MCP”）和作者（“by kohasummons”）4。  \n* **元数据**：与服务器相关的标签/关键词（GitHub、Blockchain、Search、Cryptocurrency、TypeScript）、许可证（MIT 许可证）以及兼容性/支持平台（Apple）4。  \n* **导航链接**：提供了一组内部导航链接，可快速访问页面的不同部分：概述、InspectNew、Schema、相关服务器、评论、评分和需要帮助？4。  \n* **开发链接**：提供“查看源代码”（GitHub）和“报告问题”的链接 4。  \n* **服务器功能**（“您可以使用此服务器做什么？”）：详细说明了 ChainGPT MCP 服务器的核心功能，例如与 ChainGPT AI 交互、访问加密新闻、跟踪加密市场、分析区块链数据以及检索聊天历史记录 4。  \n* **可用集成**（“此服务器有哪些集成可用？”）：概述了技术要求和集成点，例如支持 macOS 系统上的配置以与 Claude Desktop 应用程序集成，需要 Node.js 环境 (v18 或更高版本)，并支持使用 npm/npx 命令进行安装和执行 4。  \n* **设置**：设置服务器的先决条件，例如获取 ChainGPT 密钥、Node.js 环境和兼容的 MCP 客户端 4。  \n* **安装**：详细的安装说明，包括通过 Smithery 或手动安装的命令行指令，以及配置 Claude Desktop 以识别 ChainGPT MCP 服务器的步骤和 JSON 配置片段 4。  \n* **故障排除**：专门用于常见问题及其解决方案的部分，例如服务器未找到、API 密钥问题、连接问题、Node.js 版本要求和工具调用超时 4。  \n* **工具**：一个表格列出了可用的工具，包括它们的名称、描述和示例提示，例如 chaingpt\\_invoke\\_chat 和 chaingpt\\_get\\_news 4。  \n* **贡献**：一个鼓励拉取请求和问题讨论的标准部分 4。  \n* **质量指标**：显示“Asecurity – 无已知漏洞”、“Alicense – 宽松许可证”和“Aquality – 确认可用”，并提供指向其计算方法的链接 4。  \n* **技术细节**：指示服务器是否“远程可用”，并提供 HTTP 连接 URL 4。  \n* **相关资源**：指向外部讨论的链接，例如“关于此服务器的 Reddit 讨论”4。  \n* **相关 MCP 服务器**：列出其他相关的 MCP 服务器，包括其作者、安全、许可证、质量、描述、上次更新日期、技术和兼容平台 4。  \n* **新 MCP 服务器**：列出最近添加的 MCP 服务器 4。  \n* **MCP 目录 API**：提供通过 MCP API 访问服务器信息的信息，包括 curl 命令示例和 Discord 服务器链接 4。  \n* **用户反馈**：“这有用吗？是/否”按钮 4。  \n* **行动呼吁**：“安装服务器”按钮 4。\n\n需要注意的是，inspect、schema 和 related-servers 子页面无法访问 7。然而，与模式（通过工具）和相关服务器相关的信息已在主详细页面上呈现。\n\n服务器详细页面（4）上信息的数量和特异性表明，它不仅是一个产品页面，而且是每个服务器的迷你文档门户。这对于需要精确集成、设置和故障排除说明的开发人员和 AI Agent 至关重要。包含工具的示例提示对于 LLM 和 AI Agent 尤其有价值。这意味着平台必须支持富文本编辑、代码片段和结构化数据输入，以供服务器提交，从而本质上成为一个开发人员文档的内容管理系统。\n\n### **3.4. 用户交互与贡献功能**\n\n服务器列表页面上存在“添加服务器”按钮 3，以及服务器详细页面上的“报告问题”和“贡献”部分 4，都表明平台鼓励用户参与。此外，在列表和详细页面上都提供了“这有用吗？是/否”的反馈机制 3。为了支持社区，平台还提供了指向 Reddit 和 Discord 社区的社交媒体链接，并支持新闻通讯注册 3。\n\n“添加服务器”、“报告问题”、“贡献”和反馈机制的存在，表明平台旨在构建一个社区驱动的生态系统。对于像 MCP 这样的开放协议，社区贡献对于其增长和多样化至关重要。市场不仅需要促进发现，还需要积极鼓励和简化通过用户参与进行服务器提交、维护和改进的过程。这意味着需要强大的用户身份验证、内容审核，以及可能超越简单有用性反馈的评论/评分系统。\n\n### **3.5. MCP 服务器关键属性与能力总结**\n\n下表总结了从现有平台分析中识别出的 MCP 服务器的关键属性和能力。这张表格为开发人员和 AI Agent 提供了一个简洁的参考，突出了定义 MCP 服务器在市场上的关键元数据点。这种结构化的视图对于人类理解和机器处理（例如，AI Agent 需要理解服务器属性以生成代码）都至关重要。\n\n**表：MCP 服务器关键属性与能力**\n\n| 属性/能力 | 描述 | 来源片段 | 重要性/目的 |\n| :---- | :---- | :---- | :---- |\n| **服务器名称** | 服务器的唯一标识符，通常指示其功能。 | 3 | 发现、识别 |\n| **作者/组织** | 服务器的创建者或维护者。 | 4 | 归属、可信度 |\n| **简要描述** | 服务器功能的简洁概述。 | 5 | 快速理解 |\n| **详细描述** | 服务器目的和功能的全面说明。 | 4 | 深入理解 |\n| **标签/关键词** | 与服务器相关的分类词，用于过滤和搜索。 | 4 | 发现、分类 |\n| **许可证类型** | 服务器软件的许可协议（例如 MIT 许可证）。 | 4 | 法律合规性、使用限制 |\n| **编程语言** | 服务器的实现语言（例如 Python, TypeScript）。 | 2 | 技术栈兼容性、开发人员偏好 |\n| **操作系统兼容性** | 服务器支持的操作系统（例如 Apple, Linux）。 | 4 | 部署环境兼容性 |\n| **质量评分 (Asecurity, Alicense, Aquality)** | 指示服务器的安全性、许可证合规性和整体质量。 | 4 | 信任、可靠性评估 |\n| **远程能力** | 指示服务器是否可以远程托管和运行。 | 4 | 部署灵活性 |\n| **HTTP 连接 URL** | 用于直接连接服务器的 URL。 | 4 | 直接集成 |\n| **提供的能力 (资源, 工具, 提示)** | 服务器暴露给客户端的核心功能类型。 | 2 | 功能分类、LLM 交互类型 |\n| **工具定义 (名称, 描述, 示例提示, 模式)** | 服务器提供的具体函数，包括其输入/输出结构。 | 2 | LLM 调用、功能理解、代码生成 |\n| **设置说明** | 运行服务器所需的先决条件（例如 API 密钥）。 | 4 | 部署准备 |\n| **安装方法** | 详细的安装步骤和命令（例如 npm/npx, pnpm）。 | 4 | 部署指南 |\n| **故障排除提示** | 常见问题及其解决方案。 | 4 | 用户支持、问题解决 |\n| **源代码 URL** | 指向服务器代码仓库的链接。 | 4 | 透明度、贡献、审查 |\n| **问题跟踪器 URL** | 用于报告和跟踪服务器问题的链接。 | 4 | 错误报告、维护 |\n| **上次更新日期** | 服务器信息或代码上次更新的时间。 | 3 | 活跃度、新近度 |\n| **每周下载量/GitHub 星数** | 服务器的受欢迎程度指标。 | 3 | 流行度、社区认可 |\n| **相关服务器** | 与当前服务器功能相似的其他服务器列表。 | 4 | 发现替代方案、扩展探索 |\n| **类别** | 服务器所属的功能类别（例如 数据库、搜索）。 | 3 | 分类浏览、过滤 |\n\n## **4\\. 新平台功能需求**\n\n为了构建一个功能丰富且用户友好的 MCP 服务器市场，以下是根据现有平台分析和未来发展需求提出的功能需求。\n\n### **4.1. 用户管理与认证**\n\n用户必须能够注册、登录和管理他们的个人资料。虽然现有资料没有明确详细说明用户账户，但“添加服务器”按钮 3 和服务器作者的显示（例如“by kohasummons”）4 表明，用户账户对于归属和贡献是必需的。这是任何允许用户生成内容的平台的基础要求。\n\n* **需求**：用户必须能够注册、登录和管理他们的个人资料。  \n* **详细信息**：  \n  * 提供安全的认证机制（例如，OAuth、电子邮件/密码）。  \n  * 允许用户编辑个人资料（显示名称、联系信息）。  \n  * 支持密码管理功能。\n\n### **4.2. MCP 服务器提交与生命周期管理**\n\n开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。\n\n* **需求**：开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。  \n* **详细信息**：  \n  * **服务器提交表单**：捕获所有在 4 和 5 中识别的属性（服务器标题、作者、描述、标签、许可证、兼容性、功能概述、详细功能、集成、设置说明、安装方法、故障排除、工具（包括名称、描述、示例提示）、源代码 URL、问题跟踪器 URL、HTTP 连接 URL）。  \n  * **工具定义输入**：为单个工具提供结构化输入，包括其 OpenAPI/JSON Schema 定义（4 中的“Schema”导航链接和 2 中的“Tools”表格暗示）。服务器提交过程不仅仅是简单的文本输入。它是一个复杂的、结构化的数据摄取流程，需要以对开发者友好且对 AI Agent 机器可读的方式捕获结构化信息（例如，工具模式、安装步骤、故障排除点）。这需要支持富文本编辑器、代码块，并可能在提交过程中进行模式验证，以确保数据质量。  \n  * **质量指标输入/生成**：提供提交或生成 Asecurity、Alicense、Aquality 评分的机制。这可能涉及自动化检查或人工审核 4。  \n  * **草稿/发布工作流**：支持保存草稿、预览以及发布/取消发布服务器的功能。  \n  * **编辑/更新**：允许作者更新服务器信息。  \n  * **版本控制**：支持服务器的不同版本（3 和 5 中的“上次更新”暗示）。  \n  * **审核**：对新提交和更新进行管理审核流程。\n\n### **4.3. 高级服务器发现与探索**\n\n用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。\n\n* **需求**：用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。  \n* **详细信息**：  \n  * **关键词搜索**：支持对服务器名称、描述、功能和标签进行全文搜索。支持“深度搜索”和“新搜索”功能 3。  \n  * **分类**：显示并按预定义类别（例如 Python、远程、数据库、RAG 系统、区块链等）进行过滤，并显示每个类别的服务器计数 3。  \n  * **高级过滤**：按编程语言、许可证类型、操作系统兼容性、“官方”状态、质量评分（Asecurity、Alicense、Aquality）进行过滤 5。  \n  * **排序**：按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数进行排序 3。  \n  * **分页/无限滚动**：高效加载大量服务器列表，支持“加载更多”按钮 3。\n\n详细的过滤和排序选项不仅适用于人类用户，对于 AI Agent 也至关重要。一个 AI Agent 可以通过编程方式查询市场 API（4 中“MCP 目录 API”的提示）来查找符合特定条件的服务器（例如，“查找所有具有 Asecurity 评级的基于 Python 的 RAG 系统服务器”）。这意味着底层的搜索和过滤逻辑必须健壮、高性能，并且最好通过 API 暴露以供编程访问，从而使该平台成为自动化 AI 开发工作流的宝贵资源。\n\n### **4.4. 详细服务器信息显示与交互**\n\n为每个 MCP 服务器提供一个全面且结构良好的详细页面。\n\n* **需求**：为每个 MCP 服务器提供一个全面且结构良好的详细页面。  \n* **详细信息**：  \n  * 显示 4 中指定的所有属性和功能（服务器标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。  \n  * 提供交互式“安装服务器”按钮（可能根据客户端动态显示说明）4。  \n  * 提供“查看源代码”和“报告问题”的可点击链接 4。  \n  * 清晰呈现“工具”部分，包括描述和示例提示 4。  \n  * 显示“相关 MCP 服务器”和“新 MCP 服务器”以增强发现能力 4。  \n  * 与外部资源（例如 Reddit 讨论）集成 4。  \n  * 提供 HTTP 连接 URL 以便直接集成 4。\n\n“安装服务器”按钮 4 是一个关键的行动呼吁。它的有效性取决于提供针对各种 MCP 客户端（例如 Claude Desktop、IDE）量身定制的清晰、可操作的说明。这表明需要根据检测到的客户端或用户偏好进行动态内容生成或条件显示安装步骤。此功能直接将市场的发现功能与服务器在 AI 应用程序中的实际集成连接起来。\n\n### **4.5. 社区功能**\n\n使用户能够提供反馈并与平台和服务器开发者互动。\n\n* **需求**：使用户能够提供反馈并与平台和服务器开发者互动。  \n* **详细信息**：  \n  * 提供页面“这有用吗？是/否”的反馈机制 3。  \n  * 支持对单个服务器进行评论和评分（4 中“评论”和“评分”导航链接暗示）。  \n  * 提供讨论论坛或与现有社区平台（例如 3 和 4 中提到的 Discord、Reddit 链接）集成。  \n  * 支持新闻通讯注册以获取更新 3。\n\n反馈机制（有用性、评论、问题报告）对于 MCP 生态系统的健康和发展至关重要。它们为服务器开发者提供改进的宝贵信号，并为其他用户评估服务器质量和可靠性提供依据。这形成了一个良性循环：良好的反馈促成更好的服务器，从而带来更多的采用，进一步丰富了市场。平台必须有效地捕获、聚合和显示这些反馈。\n\n### **4.6. 平台管理与监控**\n\n提供工具供管理员管理内容、用户并监控平台健康状况。\n\n* **需求**：提供工具供管理员管理内容、用户并监控平台健康状况。  \n* **详细信息**：  \n  * 用户账户管理（暂停、删除）。  \n  * 服务器提交和评论的内容审核。  \n  * 类别管理（添加、编辑、删除类别）。  \n  * 分析仪表板（服务器下载量、视图、搜索查询）。  \n  * 错误日志和监控（例如，用于处理服务器 API 访问问题）。\n\n这些功能对于维护平台完整性、质量和可扩展性至关重要，尤其是在处理用户生成内容时。\n\n## **5\\. 领域驱动设计 (DDD) 核心实体模型**\n\n为了有效构建 MCP 服务器市场，采用领域驱动设计（DDD）方法至关重要，它有助于将复杂的业务逻辑分解为可管理的部分。\n\n### **5.1. 边界上下文识别**\n\n系统被划分为以下边界上下文，每个上下文都围绕特定的领域功能和一致性边界：\n\n* **服务器目录上下文 (Server Catalog Context)**：负责管理 MCP 服务器列表、发现、搜索和详细信息。这是市场的核心公共面向部分。  \n* **服务器提交与管理上下文 (Server Submission & Management Context)**：处理服务器提交、更新和开发者特定交互的生命周期。  \n* **用户与身份上下文 (User & Identity Context)**：管理用户注册、身份验证、个人资料和角色。  \n* **社区与反馈上下文 (Community & Feedback Context)**：管理评论、评分和通用平台反馈。  \n* **管理上下文 (Administration Context)**：处理平台级别的配置、审核和分析。\n\n将系统分解为不同的边界上下文是一项战略性的 DDD 决策。它避免了单体设计，降低了开发团队的认知负担，并允许系统不同部分的独立演进和扩展。例如，服务器目录 可以针对读取性能进行高度优化，而 服务器提交 则专注于数据完整性和工作流。这种分离对于需要满足不同用户角色和功能的复杂系统至关重要。\n\n### **5.2. 聚合根、实体与值对象详细建模**\n\n以下是核心边界上下文中的聚合根、实体和值对象的详细模型：\n\n#### **5.2.1. 服务器目录上下文**\n\n* **聚合根: MCP\\_Server**  \n  * **标识**: serverId (UUID)  \n  * **属性 (值对象/基本类型)**:  \n    * ServerIdentity: name (字符串), author (字符串), slug (字符串, 唯一的 URL 标识符)。  \n    * Description: shortDescription (字符串), longDescription (富文本)。  \n    * Metadata: licenseType (枚举/字符串, 例如 \"MIT License\"), programmingLanguage (枚举/字符串, 例如 \"Python\"), supportedOperatingSystems (枚举集合, 例如 \"Apple\", \"Linux\")。  \n    * QualityMetrics: securityScore (枚举/字符串, 例如 \"Asecurity\"), licenseScore (枚举/字符串, 例如 \"Alicense\"), qualityScore (枚举/字符串, 例如 \"Aquality\")。  \n    * IntegrationDetails: httpConnectionUrl (URL), remoteCapable (布尔值)。  \n    * Timestamps: dateAdded (DateTime), lastUpdated (DateTime)。  \n    * PopularityMetrics: weeklyDownloads (整数), gitHubStars (整数), recentGitHubStars (整数)。  \n  * **聚合内的实体**:  \n    * ServerCapability: 代表服务器可以做什么。  \n      * type (枚举: RESOURCE, TOOL, PROMPT)  \n      * description (字符串)  \n      * details (富文本, 例如 \"与 ChainGPT AI 交互\")  \n    * ToolDefinition: 代表服务器暴露的特定函数。  \n      * toolName (字符串, 例如 chaingpt\\_invoke\\_chat)  \n      * toolDescription (字符串)  \n      * examplePrompts (字符串列表)  \n      * schema (JSON/OpenAPI 模式, 代表输入/输出结构)  \n    * IntegrationStep: 集成服务器的详细说明。  \n      * platform (字符串, 例如 \"Claude Desktop\", \"Smithery\")  \n      * method (字符串, 例如 \"手动安装\", \"通过 Smithery\")  \n      * instructions (包含代码片段的富文本)  \n    * TroubleshootingTip: 常见问题和解决方案。  \n      * issue (字符串)  \n      * solution (富文本)  \n  * **值对象**:  \n    * Category: name (字符串, 例如 \"数据库\", \"搜索\"), count (整数)。  \n    * Tag: name (字符串, 例如 \"区块链\", \"TypeScript\")。  \n    * ExternalLink: url (URL), type (枚举: GITHUB\\_SOURCE, ISSUE\\_TRACKER, REDDIT\\_DISCUSSION)。  \n* **聚合根: Category**  \n  * **标识**: categoryId (UUID)  \n  * **属性**: name (字符串, 唯一), slug (字符串), description (字符串), serverCount (整数)。  \n  * **关系**: MCP\\_Server 与 Category 之间存在多对多关系（通过 Tag 或直接关联）。\n\n#### **5.2.2. 用户与身份上下文**\n\n* **聚合根: User**  \n  * **标识**: userId (UUID)  \n  * **属性**: username (字符串), email (字符串), passwordHash (字符串), profileName (字符串), roles (枚举集合: DEVELOPER, USER, ADMIN)。  \n  * **聚合内的实体**: UserProfile (例如, 个人简介, 头像)。\n\n#### **5.2.3. 社区与反馈上下文**\n\n* **聚合根: Review**  \n  * **标识**: reviewId (UUID)  \n  * **属性**: rating (整数), comment (字符串), timestamp (DateTime)。  \n  * **关系**: Review 属于 User (评论者) 和 MCP\\_Server (被评论的服务器)。  \n* **实体: Feedback**  \n  * **标识**: feedbackId (UUID)  \n  * **属性**: pageUrl (URL), helpful (布尔值), comment (可选字符串), timestamp (DateTime)。  \n  * **关系**: Feedback 可以与 User 关联。\n\n### **5.3. 关键关系与领域不变性**\n\n* 一个 MCP\\_Server 必须有一个唯一的 slug。  \n* MCP\\_Server 内的 ToolDefinition 必须有唯一的 toolName。  \n* QualityMetrics 应该一致地应用，并可能基于外部数据（例如，GitHub 仓库分析）进行计算。  \n* 一个 Review 必须链接到一个现有的 User 和 MCP\\_Server。  \n* Category 必须有唯一的名称。\n\n**表：DDD 模型 \\- 核心实体、属性与关系**\n\n下表提供了所识别领域模型的清晰、结构化概述，可作为数据库模式设计和后端服务开发的蓝图。它明确定义了聚合、实体和值对象，以及它们的关键属性和关系，这对于 AI Agent 生成数据模型和 ORM 代码至关重要。\n\n| 边界上下文 | 领域对象类型 | 名称 | 关键属性 | 关系 | 关键不变性/规则 |\n| :---- | :---- | :---- | :---- | :---- | :---- |\n| **服务器目录** | 聚合根 | MCP\\_Server | serverId, ServerIdentity, Description, Metadata, QualityMetrics, IntegrationDetails, Timestamps, PopularityMetrics | 包含 ServerCapability, ToolDefinition, IntegrationStep, TroubleshootingTip 实体；与 Category 多对多 | slug 唯一；ToolDefinition.toolName 在 MCP\\_Server 内唯一 |\n|  | 实体 | ServerCapability | type, description, details | 属于 MCP\\_Server | \\- |\n|  | 实体 | ToolDefinition | toolName, toolDescription, examplePrompts, schema | 属于 MCP\\_Server | \\- |\n|  | 实体 | IntegrationStep | platform, method, instructions | 属于 MCP\\_Server | \\- |\n|  | 实体 | TroubleshootingTip | issue, solution | 属于 MCP\\_Server | \\- |\n|  | 值对象 | Category | name, count | \\- | \\- |\n|  | 值对象 | Tag | name | \\- | \\- |\n|  | 值对象 | ExternalLink | url, type | \\- | \\- |\n| **服务器目录** | 聚合根 | Category | categoryId, name, slug, description, serverCount | 与 MCP\\_Server 多对多 | name 唯一 |\n| **用户与身份** | 聚合根 | User | userId, username, email, passwordHash, profileName, roles | 包含 UserProfile 实体 | username, email 唯一 |\n|  | 实体 | UserProfile | bio, avatar | 属于 User | \\- |\n| **社区与反馈** | 聚合根 | Review | reviewId, rating, comment, timestamp | 属于 User，关联 MCP\\_Server | 必须链接到现有 User 和 MCP\\_Server |\n|  | 实体 | Feedback | feedbackId, pageUrl, helpful, comment, timestamp | 可关联 User | \\- |\n\n## **6\\. 敏捷用户故事**\n\n以下用户故事遵循“作为 \\[用户角色\\]，我想要 \\[行动\\]，以便 \\[益处\\]”的格式，并确保符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。每个故事都包含详细的验收标准，为 AI Agent 进行相关的代码生成提供清晰、明确的步骤。\n\n### **6.1. 用户故事通用原则**\n\n* 遵循“作为 \\[用户角色\\]，我想要 \\[行动\\]，以便 \\[益处\\]”的格式。  \n* 确保故事符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。  \n* 每个故事都有详细的验收标准，提供清晰、明确的测试步骤和 AI Agent 代码生成指导。  \n* 重点关注具体的数据点和交互，以提高 AI Agent 的清晰度。\n\n### **6.2. 服务器发现与列表用户故事**\n\n* **US-001: 查看所有 MCP 服务器**  \n  * **描述**：作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。  \n  * **验收标准**：  \n    * 当我导航到服务器列表页面 (/mcp/servers) 时。  \n    * 我看到“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）3。  \n    * 我看到整个列表的“上次更新”时间戳 3。  \n    * 我看到一个分页/可滚动的 MCP 服务器条目列表。  \n    * 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）5。  \n    * 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目 3。  \n* **US-002: 按类别筛选服务器**  \n  * **描述**：作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面 (/mcp/servers) 时。  \n    * 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。  \n    * URL 更新以反映该类别（例如，/mcp/servers/categories/python）6。  \n    * 显示的列表仅显示标记为“Python”的服务器 3。  \n    * “Python”的类别计数准确（例如，“2,436”）3。  \n    * 每个显示的服务器条目都符合 US-001 的标准。  \n* **US-003: 按受欢迎程度/新近度排序服务器**  \n  * **描述**：作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面时。  \n    * 当我从排序下拉菜单中选择“每周下载量”时。  \n    * 服务器列表重新排序，以最高每周下载量的服务器优先显示 3。  \n    * 当我从排序下拉菜单中选择“更新日期”时。  \n    * 服务器列表重新排序，以最近更新的服务器优先显示 3。  \n    * 排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数 3。  \n* **US-004: 按关键词搜索服务器**  \n  * **描述**：作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。  \n  * **验收标准**：  \n    * 当我位于服务器列表页面时。  \n    * 当我在搜索栏中输入“区块链”并按 Enter 键时。  \n    * 页面显示名称、描述或标签中包含“区块链”的服务器列表 5。  \n    * “深度搜索”和“新搜索”按钮可用 3。  \n    * 每个显示的服务器条目都符合 US-001 的标准。  \n* **US-005: 查看单个服务器详情**  \n  * **描述**：作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。  \n  * **验收标准**：  \n    * 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。  \n    * 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）4。  \n    * 页面突出显示：服务器标题、作者、标签、许可证、兼容性 4。  \n    * 页面包含导航链接：概述、Schema、相关服务器、评论、评分 4。  \n    * 页面提供“查看源代码”和“报告问题”链接 4。  \n    * “您可以使用此服务器做什么？”部分详细说明了核心功能 4。  \n    * “此服务器有哪些集成可用？”部分概述了技术要求和集成点 4。  \n    * “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）4。  \n    * “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）4。  \n    * “故障排除”部分列出了常见问题和解决方案 4。  \n    * “工具”表格列出了每个工具的名称、描述和示例提示 4。  \n    * 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接 4。  \n    * “安装服务器”按钮和 HTTP 连接 URL 存在 4。  \n    * “相关 MCP 服务器”部分显示了类似服务器的列表 4。  \n    * “新 MCP 服务器”部分显示了最近添加的服务器 4。  \n    * “这有用吗？是/否”反馈按钮可用 4。\n\n### **6.3. 服务器提交与管理用户故事**\n\n* **US-006: 提交新的 MCP 服务器**  \n  * **描述**：作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。  \n  * **验收标准**：  \n    * 当我以开发者身份登录时。  \n    * 当我点击“添加服务器”按钮时 3。  \n    * 我看到一个多步骤表单，用于输入服务器详细信息。  \n    * 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框 2。  \n    * 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述 2。  \n    * 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段 2。  \n    * 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）4。  \n    * 我可以添加多个“故障排除提示”，包括问题和解决方案 4。  \n    * 我可以选择相关的“类别”并添加“标签”4。  \n    * 当我提交表单时，服务器进入待审核状态。  \n    * 我收到确认消息。  \n* **US-007: 编辑我提交的 MCP 服务器**  \n  * **描述**：作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。  \n  * **验收标准**：  \n    * 当我以已提交服务器的作者身份登录时。  \n    * 当我导航到我的“我的服务器”仪表板时。  \n    * 我选择一个现有服务器进行编辑。  \n    * 我看到提交表单预填充了服务器的当前数据 4。  \n    * 我可以修改任何字段并重新提交以供审核。  \n    * 当我重新提交时，服务器的“上次更新”时间戳在批准后更新 3。\n\n### **6.4. 社区与反馈用户故事**\n\n* **US-008: 提供有用性反馈**  \n  * **描述**：作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。  \n  * **验收标准**：  \n    * 当我位于任何服务器列表或详情页面时。  \n    * 当我点击“这有用吗？”提示中的“是”或“否”时 3。  \n    * 我的反馈被记录。  \n    * 反馈提示消失或改变以表明我的提交。  \n* **US-009: 为 MCP 服务器留下评论**  \n  * **描述**：作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。  \n  * **验收标准**：  \n    * 当我登录并位于 MCP 服务器的详情页面时。  \n    * 当我导航到“评论”部分时 4。  \n    * 我可以看到现有评论和一个提交新评论的表单。  \n    * 我可以选择一个评分（例如，1-5 星）并输入评论。  \n    * 当我提交评论时，它在审核后（如果适用）出现在页面上。  \n    * 服务器的整体评分更新 4。\n\n**表：用户故事目录**\n\n下表提供了结构化、可操作的开发任务列表，直接映射到功能需求。其详细的验收标准旨在足够精确，以便 AI Agent 能够解释并生成相关的代码，涵盖前端 UI 和后端逻辑。这种详细程度确保了开发过程的完整性并减少了歧义。\n\n| 故事 ID | 标题 | 描述 | 验收标准 | 优先级 |\n| :---- | :---- | :---- | :---- | :---- |\n| US-001 | 查看所有 MCP 服务器 | 作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。 | 当我导航到服务器列表页面 (/mcp/servers) 时，我看到“开源 MCP 服务器”的总数和“上次更新”时间戳。我看到一个分页/可滚动的 MCP 服务器条目列表，每个条目显示名称、官方标签、开发者、质量指标、描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标。当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。 | 高 |\n| US-002 | 按类别筛选服务器 | 作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域相关的服务器。 | 当我位于服务器列表页面时，我从侧边栏/筛选选项中选择一个类别（例如，“Python”）。URL 更新以反映该类别，显示的列表仅显示标记为该类别的服务器，且类别计数准确。每个显示的服务器条目都符合 US-001 的标准。 | 高 |\n| US-003 | 按受欢迎程度/新近度排序服务器 | 作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。 | 当我位于服务器列表页面时，我从排序下拉菜单中选择“每周下载量”或“更新日期”。服务器列表重新排序，以最高每周下载量或最近更新的服务器优先显示。排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数。 | 高 |\n| US-004 | 按关键词搜索服务器 | 作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。 | 当我位于服务器列表页面时，我在搜索栏中输入关键词并按 Enter 键。页面显示名称、描述或标签中包含关键词的服务器列表。“深度搜索”和“新搜索”按钮可用。每个显示的服务器条目都符合 US-001 的标准。 | 高 |\n| US-005 | 查看单个服务器详情 | 作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。 | 当我点击服务器列表中的一个服务器条目时，我被导航到服务器的详情页面。页面突出显示服务器标题、作者、标签、许可证、兼容性，并包含导航链接、开发链接、详细的功能、集成、设置、安装、故障排除、工具、质量指标、安装按钮、HTTP 连接 URL、相关/新服务器列表和有用性反馈按钮。 | 高 |\n| US-006 | 提交新的 MCP 服务器 | 作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。 | 当我以开发者身份登录并点击“添加服务器”按钮时，我看到一个多步骤表单，用于输入服务器的详细信息。表单包含所有必要的字段，包括结构化的功能、工具、集成步骤和故障排除提示。提交后，服务器进入待审核状态，并收到确认消息。 | 中 |\n| US-007 | 编辑我提交的 MCP 服务器 | 作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。 | 当我以已提交服务器的作者身份登录并导航到“我的服务器”仪表板时，我选择一个现有服务器进行编辑。我看到提交表单预填充了服务器的当前数据，可以修改任何字段并重新提交以供审核。重新提交后，服务器的“上次更新”时间戳在批准后更新。 | 中 |\n| US-008 | 提供有用性反馈 | 作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。 | 当我位于任何服务器列表或详情页面时，我点击“这有用吗？”提示中的“是”或“否”。我的反馈被记录，反馈提示消失或改变以表明我的提交。 | 低 |\n| US-009 | 为 MCP 服务器留下评论 | 作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。 | 当我登录并位于 MCP 服务器的详情页面时，我导航到“评论”部分。我可以看到现有评论和一个提交新评论的表单。我可以选择一个评分并输入评论。提交后，评论在审核后（如果适用）出现在页面上，服务器的整体评分更新。 | 低 |\n\n## **7\\. 结论与下一步**\n\n### **7.1. 拟议架构与开发方法总结**\n\n本报告概述了一个基于领域驱动设计的健壮、模块化架构，强调通过边界上下文实现清晰的关注点分离。这种分解有助于避免单体设计，降低开发复杂性，并允许系统不同部分独立演进和扩展。详细的功能需求和敏捷用户故事为迭代开发提供了清晰的路线图，由于其特异性和完整性，非常适合利用 AI Agent 进行代码生成。这种方法将确保开发过程的高效性和产出的高质量。\n\n### **7.2. 实施与未来增强建议**\n\n为了成功实施和持续发展 MCP 服务器市场，建议采取以下步骤和考虑未来的增强功能：\n\n* **分阶段开发**：建议优先开发核心服务器列表和发现功能（US-001 至 US-005），其次是服务器提交和管理功能（US-006，US-007），最后是社区功能（US-008，US-009）。这种迭代方法将确保核心价值的快速交付和用户反馈的早期整合。  \n* **API 优先方法**：开发一个全面的市场 API（如 4 中“MCP 目录 API”所暗示），以实现 AI Agent 和其他应用程序的编程访问，而不仅仅是基于 Web 的用户界面。这将极大地扩展平台的实用性和集成潜力。  \n* **自动化质量检查**：探索集成自动化工具，在服务器提交过程中生成/验证 Asecurity、Alicense 和 Aquality 评分。这将提高数据质量和用户对服务器信息的信任。  \n* **AI 驱动的搜索**：研究使用 LLM 增强市场自身的搜索能力，允许用户使用自然语言查询来查找相关的 MCP 服务器。这将提升用户体验并进一步利用 AI 技术。  \n* **监控与分析**：实施强大的日志记录和分析系统，以跟踪服务器使用情况、热门类别和用户行为，从而为未来的开发提供信息并优化平台。  \n* **可扩展性考虑**：在设计数据模型和服务时，应充分考虑可扩展性，以应对服务器数量和用户流量的增长。确保基础设施能够支持未来的扩展需求。\n\n#### **Works cited**\n\n1. Model Context Protocol: Introduction, accessed June 20, 2025, [https://modelcontextprotocol.io/introduction](https://modelcontextprotocol.io/introduction)  \n2. For Server Developers \\- Model Context Protocol, accessed June 20, 2025, [https://modelcontextprotocol.io/quickstart/server](https://modelcontextprotocol.io/quickstart/server)  \n3. MCP servers | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers](https://glama.ai/mcp/servers)  \n4. ChainGPT MCP | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp)  \n5. Search | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/categories/search](https://glama.ai/mcp/servers/categories/search)  \n6. Databases | Glama, accessed June 20, 2025, [https://glama.ai/mcp/servers/categories/databases](https://glama.ai/mcp/servers/categories/databases)  \n7. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/inspect](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/inspect)  \n8. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/schema](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/schema)  \n9. accessed January 1, 1970, [https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/related-servers](https://glama.ai/mcp/servers/@kohasummons/chaingpt-mcp/related-servers)\n\n请提供改进后的完整分析结果。\n"}]}
{"timestamp": "2025-06-25T22:32:20.591675", "agent_name": "business_analyzer", "interaction_id": 4, "task_description": "业务需求分析", "interaction_type": "response", "content": "# 处理后的开发规则\n\n## 1. 规则概述\n- **规则来源**: rules.md\n- **处理时间**: 2023年10月27日\n- **规则范围**: 基于 FastAPI 和领域驱动设计（DDD）的 Python 项目代码生成与开发\n- **主要改进**:\n    - 统一并标准化了规则的分类和结构。\n    - 明确了各层职责和依赖关系。\n    - 细化了命名规范和文件组织原则。\n    - 强调了测试、工程实践和Git提交的重要性。\n    - 补充了数据库设计和模块内代码组织的关键约束。\n\n## 2. 核心原则\n- **架构设计原则**:\n    - **领域驱动设计 (DDD)**: 所有开发活动围绕领域模型展开，严格遵守分层架构的职责分离原则。\n    - **分层架构**: 模块内部遵循经典四层 DDD 架构（Interfaces, Application, Domain, Infrastructure）。\n    - **依赖倒置**: 上层依赖下层的抽象接口，下层实现这些接口。\n    - **模块化**: 项目按业务功能划分为独立模块，模块间通过应用层服务接口通信。\n- **开发方法论**:\n    - **测试驱动**: 任何新功能或修改必须伴随自动化测试。\n    - **原子化提交**: 每个提交只关注一个独立的、逻辑相关的变更。\n- **质量标准理念**:\n    - **代码风格**: 严格遵循 PEP 8 规范。\n    - **类型提示**: 强制要求所有函数签名和变量声明包含明确类型提示。\n    - **文档语言**: 所有注释、文档字符串和文档内容必须使用**英文**编写。\n    - **数据库设计**: 强制使用 UUID 作为主键，字段命名只反映业务含义，禁止技术后缀。\n\n## 3. 技术规范\n- **技术栈要求**:\n    - **Web 框架**: FastAPI\n    - **数据验证与建模**: Pydantic\n    - **ORM 与数据库**: SQLAlchemy (配合 Alembic 进行数据库迁移)\n    - **测试框架**: Pytest\n    - **HTTP 测试**: FastAPI TestClient\n    - **Mocking**: unittest.mock 或 pytest-mock\n- **框架使用规范**:\n    - FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型。\n    - SQLAlchemy ORM 模型使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`。\n    - 禁止在应用启动时自动创建数据库表，所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n- **编码标准**:\n    - 严格遵循 PEP 8 规范。\n    - 强制使用类型提示。\n    - 所有注释和文档使用英文。\n\n## 4. 架构约束\n- **分层架构规则**:\n    - **Interfaces (接口层)**: 处理 HTTP 请求、数据转换，通过依赖注入调用应用层服务。\n    - **Application (应用层)**: 编排领域逻辑，协调用例，定义事务边界。\n    - **Domain (领域层)**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\n    - **Infrastructure (基础设施层)**: 实现与外部世界（数据库、文件系统、第三方 API）交互的技术细节，负责领域模型与 ORM 模型之间的转换。\n- **模块组织方式**:\n    - 项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。\n    - 通用代码放置在根目录的 `common/` 文件夹下。\n    - 模块内当包含多个相关但职责不同的业务子域时，通过文件命名前缀来区分，而不是创建深层嵌套的目录结构。\n- **依赖关系约束**:\n    - **模块内部单向依赖**: `Interfaces` → `Application` → `Domain`。\n    - **依赖倒置**: `Application` 层依赖 `Domain` 层定义的抽象接口，`Infrastructure` 层实现这些接口。\n    - **领域层纯粹性**: `Domain` 层**绝对禁止**导入任何 `fastapi`, `sqlalchemy` 或其他外部框架。\n    - **模块间通信**: 必须通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - **通用模块使用**: `common/` 或 `utils/` 下的通用代码可以被任何层级导入，但通用代码本身不能反向依赖任何业务模块。\n    - **跨业务子域依赖**: 同模块内业务子域之间可以相互依赖，但必须通过应用层服务接口。跨模块则必须通过目标模块的应用层服务接口。\n\n## 5. 代码质量标准\n- **代码风格规范**:\n    - 严格遵循 PEP 8。\n    - 强制使用类型提示。\n    - 所有代码、注释、文档字符串和文档内容必须使用**英文**编写。\n- **测试要求**:\n    - **测试框架**: Pytest。\n    - **测试目录结构**: `tests/` 目录结构与 `modules/` 目录保持一致，按业务模块组织。\n    - **测试类型与分层**:\n        - **单元测试**: 针对单一模块或函数，`Domain Layer` 测试实体业务方法，`Application Layer` 测试服务业务流程时需模拟（Mock）外部依赖。\n        - **接口/集成测试**: 使用 `TestClient` 测试从 HTTP 端点到数据库的完整流程，验证各层集成。\n    - **核心测试原则**:\n        - 任何新功能或修改**必须**有相应的测试用例覆盖。\n        - 所有测试用例**必须通过**。\n        - 测试失败时，优先修复生产代码。\n        - **严禁**随意修改测试用例，除非需求变更或测试用例设计缺陷。\n    - **测试用例命名规范**: 使用 `should_[预期行为]_when_[条件]` 格式（BDD风格），必须使用**英文**描述。\n- **文档标准**:\n    - 所有 API 端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档，并与代码变更同步更新。\n    - 所有注释、文档字符串和文档内容必须使用**英文**编写。\n\n## 6. 工程实践\n- **开发流程**:\n    - **代码生成约束与流程**: 遵循“Domain First”原则，从领域层开始，逐步实现应用层、基础设施层和接口层，最后集成到主应用。\n    - **重构约束**:\n        - 优先创建新文件进行修改，最后删除旧文件。\n        - 重构前需分析完整上下文。\n        - 重构后必须运行自动化测试，无测试覆盖部分应补充测试。\n- **版本控制**:\n    - **Git 提交规范**: 强制遵循约定式提交（Conventional Commits）规范。\n        - **格式**: `<type>(<scope>): <subject>`，可选 `<body>` 和 `<footer>`。\n        - **`type`**: 必须是 `feat`, `fix`, `docs`, `style`, `refactor`, `perf`, `test`, `build`, `ci`, `chore`, `revert` 之一。\n        - **`scope`**: 受影响的业务模块名（如 `auth`, `orders`）。\n        - **`subject`**: 简短描述，不超过 50 字符，祈使句，现在时态，首字母小写，不加句号，**英文**。\n        - **`body`**: 详细描述，祈使句，现在时态，**英文**。\n        - **`footer`**: 用于 `BREAKING CHANGE:` 或 `Closes #ISSUE_ID`。\n    - **提交策略**: 遵循原子化提交原则，每个提交只关注一个独立的、逻辑相关的变更。\n- **部署规范**:\n    - **依赖管理**:\n        - 生成代码前检查 `requirements.txt`。\n        - 添加新依赖后，立即执行 `pip freeze > requirements.txt`。\n    - **环境变量管理**:\n        - 优先复用现有环境变量。\n        - 新增环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - **虚拟环境管理**:\n        - 生成终端命令前提醒用户激活 `venv` 虚拟环境。\n        - 提供激活命令参考。\n    - **数据库迁移**:\n        - **禁止**在应用启动时自动创建数据库表。\n        - 所有数据库结构变更必须通过 Alembic 迁移脚本完成。\n\n## 7. 最佳实践\n- **开发技巧**:\n    - **模块内代码组织**:\n        - 当模块内包含多个业务子域时，通过文件命名前缀区分，避免深层嵌套目录。\n        - **强制要求**所有模块内的文件使用业务子域前缀命名，严禁使用通用文件名（如 `models.py`）。\n        - 命名格式：`{业务子域}_{层级类型}.py`。\n        - 业务子域识别原则：基于数据模型、业务流程、外部依赖或生命周期差异。\n        - 禁止使用类似 `_management` 的功能性命名后缀。\n        - 禁止过度嵌套目录。\n    - **共享组件处理**:\n        - 模块内共享组件放置在 `shared/` 目录。\n        - 跨模块共享组件放置在根目录的 `common/` 或 `shared/` 目录。\n- **常见问题解决方案**:\n    - **测试失败**: 优先修复生产代码，而非修改测试。\n- **性能优化建议**:\n    - 数据库索引设计：主键自动创建唯一索引，外键字段和常用查询条件字段应创建索引。\n\n## 8. 约束与限制\n- **禁止的做法**:\n    - 在 `Domain` 层导入任何外部框架（如 `fastapi`, `sqlalchemy`）。\n    - 模块间直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n    - 随意修改测试用例以通过测试。\n    - 在应用启动时自动创建数据库表。\n    - 数据库字段名包含技术实现细节（如 `id_uuid`, `name_str`）。\n    - 使用通用文件名（如 `models.py`, `services.py`）而不带业务子域前缀。\n    - 过度嵌套目录结构。\n    - 使用类似 `_management` 的功能性命名后缀。\n- **必须遵守的规则**:\n    - 所有实体主键必须使用 UUID 类型。\n    - 所有涉及实体 ID 的 Pydantic 模型字段和 FastAPI 路由参数必须声明为 `UUID` 类型。\n    - 数据库字段名必须只反映业务含义。\n    - 数据库表名使用复数形式的英文单词，下划线分隔，避免缩写。\n    - 任何新功能或修改必须有相应的测试用例覆盖。\n    - 所有测试用例必须通过。\n    - 所有 Git 提交必须遵循约定式提交规范。\n    - 任何代码生成或修改后，必须提醒用户运行相关自动化测试。\n    - 任何需要新库的代码生成前，必须检查 `requirements.txt`。\n    - 添加新依赖后，必须立即执行 `pip freeze > requirements.txt`。\n    - 添加新的环境变量必须使用 `AI4SE_MCP_HUB_` 作为前缀，并更新 `.env.example`。\n    - 生成终端命令前必须提醒用户激活虚拟环境。\n- **异常处理原则**:\n    - 接口层负责处理 HTTP 错误。\n    - 应用层定义事务边界。# 业务需求分析报告\n\n## 1. 业务概览\n- **项目名称**: MCP 服务器市场平台\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\n- **核心目标**:\n    1. 促进 MCP 服务器的发现、评估和集成。\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\n    5. 确保平台的可信度、安全性和质量。\n- **目标用户**:\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\n- **价值主张**:\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\n\n## 2. 核心实体分析\n\n### 实体1: MCP 服务器 (MCP_Server)\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\n- **主要属性**:\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\n    - **分类**: 标签/关键词、所属类别。\n- **业务规则**:\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\n    - 服务器内的每个工具定义必须有唯一的工具名称。\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\n- **关系**:\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\n\n### 实体2: 用户 (User)\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\n- **主要属性**:\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\n    - 个人简介、头像（UserProfile）。\n- **业务规则**:\n    - 用户名和电子邮件必须唯一。\n    - 密码必须加密存储。\n    - 不同角色拥有不同的操作权限。\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\n- **关系**:\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\n    - 与“反馈”：用户可以提供有用性反馈。\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\n\n### 实体3: 类别 (Category)\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\n- **主要属性**:\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\n- **业务规则**:\n    - 类别名称必须唯一。\n    - 类别计数应实时反映该类别下的服务器数量。\n- **关系**:\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\n\n### 实体4: 评论 (Review)\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\n- **主要属性**:\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\n- **业务规则**:\n    - 评论必须关联到一个用户和一个 MCP 服务器。\n    - 评论内容可能需要审核。\n    - 评分应影响 MCP 服务器的整体评分。\n- **关系**:\n    - 与“用户”：一个评论由一个用户发布。\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\n\n### 实体5: 反馈 (Feedback)\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\n- **主要属性**:\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\n- **业务规则**:\n    - 反馈可以匿名，也可以关联到用户。\n    - 反馈应被记录和聚合，用于平台改进。\n- **关系**:\n    - 可与“用户”关联。\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\n\n### 特别关注的MCP核心实体\n\n- **MCP服务器**:\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\n    - **版本管理**: 虽然文档未明确提及，但“上次更新”和“编辑/更新”功能暗示了服务器信息的版本概念，可能需要支持服务器代码和元数据的版本控制。\n- **工具定义**:\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\n- **用户角色**:\n    - **开发者**: 提交、管理和更新 MCP 服务器。\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\n    - **管理员**: 审核内容、管理用户、监控平台。\n- **质量评估**:\n    - **Asecurity**: 无已知漏洞。\n    - **Alicense**: 宽松许可证。\n    - **Aquality**: 确认可用。\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\n- **开发者生态**:\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\n\n## 3. 功能需求\n\n### 高优先级需求\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\n  - 验收标准:\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\n    - 列表分页/无限滚动，每个条目显示指定元数据。\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\n  - 功能分类: 发现与浏览\n\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\n  - 验收标准:\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\n    - 支持“深度搜索”和“新搜索”功能。\n  - 功能分类: 发现与浏览\n\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\n  - 验收标准:\n    - 排序下拉菜单提供所有排序选项。\n    - 选择不同排序选项时，服务器列表按预期重新排序。\n  - 功能分类: 发现与浏览\n\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\n  - 验收标准:\n    - 点击服务器列表中的条目，导航到其详情页面。\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\n    - “工具”部分清晰呈现名称、描述和示例提示。\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\n    - 显示 HTTP 连接 URL。\n  - 功能分类: 详情查看\n\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\n  - 验收标准:\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\n    - 提交后，服务器进入待审核状态，并收到确认消息。\n  - 功能分类: 内容管理\n\n### 中优先级需求\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\n  - 验收标准:\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\n  - 功能分类: 内容管理\n\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\n  - 验收标准:\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\n    - 用户能够编辑显示名称、联系信息。\n    - 支持密码找回/重置功能。\n  - 功能分类: 用户管理\n\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\n  - 验收标准:\n    - 页面上显示“这有用吗？是/否”按钮。\n    - 点击后，反馈被记录，按钮状态改变或消失。\n  - 功能分类: 社区互动\n\n### 低优先级需求\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\n  - 验收标准:\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\n    - 用户可以选择评分（1-5星）并输入评论。\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\n  - 功能分类: 社区互动\n\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\n  - 验收标准:\n    - 管理员登录后，访问管理仪表板。\n    - 能够暂停/删除用户账户。\n    - 能够审核和批准/拒绝服务器提交和评论。\n    - 能够添加/编辑/删除类别。\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\n  - 功能分类: 平台管理\n\n## 4. 非功能需求\n\n### 性能要求\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\n\n### 安全要求\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\n\n### 可用性要求\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\n\n### 可扩展性要求\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\n\n## 5. 业务规则\n\n1. **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\n   - 触发条件: 开发者提交新的 MCP 服务器。\n   - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\n\n2. **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\n   - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\n   - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\n\n3. **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\n   - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\n   - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\n\n4. **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\n   - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\n   - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\n\n5. **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\n   - 触发条件: 用户提交评论。\n   - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\n\n6. **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\n   - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\n   - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\n\n7. **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\n   - 触发条件: 用户尝试访问服务器提交/编辑功能。\n   - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\n\n## 6. 用户故事\n\n### 核心用户故事\n- **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\n  - 验收标准:\n    - 当我导航到服务器列表页面 (/mcp/servers) 时。\n    - 我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\n    - 我看到一个分页/可滚动的 MCP 服务器条目列表。\n    - 每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\n    - 当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\n\n- **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\n  - 验收标准:\n    - 当我位于服务器列表页面 (/mcp/servers) 时。\n    - 当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\n    - URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\n    - 显示的列表仅显示标记为“Python”的服务器。\n    - “Python”的类别计数准确（例如，“2,436”）。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\n  - 验收标准:\n    - 当我位于服务器列表页面时。\n    - 当我在搜索栏中输入“区块链”并按 Enter 键时。\n    - 页面显示名称、描述或标签中包含“区块链”的服务器列表。\n    - “深度搜索”和“新搜索”按钮可用。\n    - 每个显示的服务器条目都符合 US-001 的标准。\n\n- **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\n  - 验收标准:\n    - 当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\n    - 我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\n    - 页面突出显示：服务器标题、作者、标签、许可证、兼容性。\n    - 页面包含导航链接：概述、Schema、相关服务器、评论、评分。\n    - 页面提供“查看源代码”和“报告问题”链接。\n    - “您可以使用此服务器做什么？”部分详细说明了核心功能。\n    - “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\n    - “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\n    - “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\n    - “故障排除”部分列出了常见问题和解决方案。\n    - “工具”表格列出了每个工具的名称、描述和示例提示。\n    - 质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\n    - “安装服务器”按钮和 HTTP 连接 URL 存在。\n    - “相关 MCP 服务器”部分显示了类似服务器的列表。\n    - “新 MCP 服务器”部分显示了最近添加的服务器。\n    - “这有用吗？是/否”反馈按钮可用。\n\n- **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\n  - 验收标准:\n    - 当我以开发者身份登录时。\n    - 当我点击“添加服务器”按钮时。\n    - 我看到一个多步骤表单，用于输入服务器详细信息。\n    - 表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框。\n    - 我可以添加多个“功能”，包括类型（资源、工具、提示）和描述。\n    - 我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段。\n    - 我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）。\n    - 我可以添加多个“故障排除提示”，包括问题和解决方案。\n    - 我可以选择相关的“类别”并添加“标签”。\n    - 当我提交表单时，服务器进入待审核状态。\n    - 我收到确认消息。\n\n## 7. 关键业务流程\n\n### 流程1: MCP 服务器发现与浏览\n- **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\n- **参与者**: 用户（包括最终用户、AI 代理）。\n- **主要步骤**:\n  1. **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\n  2. **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\n  3. **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\n  4. **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\n  5. **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\n  6. **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\n  7. **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\n- **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\n- **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\n\n### 流程2: MCP 服务器提交与发布\n- **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者使用其账户登录市场平台。\n  2. **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\n  3. **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\n  4. **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\n  5. **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\n  6. **审核结果反馈**:\n     - **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\n     - **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n  7. **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\n- **输入**: 开发者提交的服务器详细信息。\n- **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\n\n### 流程3: MCP 服务器信息更新\n- **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效。\n- **参与者**: 开发者、平台管理员。\n- **主要步骤**:\n  1. **开发者登录**: 开发者登录市场平台。\n  2. **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\n  3. **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\n  4. **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\n  5. **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\n  6. **管理员审核更新**: 平台管理员对更新内容进行审查。\n  7. **更新结果反馈**:\n     - **审核通过**: 管理员批准更新，服务器信息在市场中更新。开发者收到更新成功的通知。\n     - **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n- **输入**: 开发者修改后的服务器详细信息。\n- **输出**: 更新后的 MCP 服务器信息。\n\n### 流程4: 用户反馈与社区互动\n- **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\n- **参与者**: 用户、MCP 服务器开发者、平台管理员。\n- **主要步骤**:\n  1. **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\n  2. **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\n  3. **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\n  4. **提交评论**: 用户提交评论。\n  5. **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\n  6. **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\n  7. **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\n- **输入**: 用户有用性选择、评论内容、评分。\n- **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。好的，作为资深业务分析专家，我将根据您的要求，对原始分析结果进行全面改进，增加缺失的业务实体和功能需求，并更全面地覆盖原始文档内容。\n\n以下是改进后的业务分析报告：\n\n# 改进后的业务分析报告\n\n## 1. 业务概览\n\n- **项目名称**: MCP 服务器市场平台\n- **项目描述**: 本项目旨在开发一个 Model Context Protocol (MCP) 服务器市场平台，促进 MCP 服务器的发现、评估和集成。该平台将作为一个集中式目录，展示开源 MCP 服务器，并提供强大的搜索、过滤、详细信息展示以及社区贡献功能，以解决 LLM 与外部数据源和工具集成的问题，并降低 LLM 供应商锁定效应。\n- **核心目标**:\n    1. 促进 MCP 服务器的发现、评估和集成。\n    2. 提供一个集中式目录，展示开源 MCP 服务器。\n    3. 增强用户（包括开发者、AI 代理和最终用户）发现、评估和集成 MCP 服务器的能力。\n    4. 建立一个社区驱动的生态系统，鼓励用户参与和贡献。\n    5. 确保平台的可信度、安全性和质量。\n- **目标用户**:\n    1. **最终用户**: 需要通过 LLM 访问外部数据和工具的普通用户。\n    2. **AI 代理**: 需要发现和集成 MCP 服务器以执行复杂任务的自动化程序。\n    3. **MCP 服务器开发者**: 希望发布、管理和推广其 MCP 服务器的开发者。\n    4. **平台管理员**: 负责平台内容审核、用户管理和系统监控的人员。\n- **价值主张**:\n    1. **简化集成**: 为 LLM 提供标准化的数据和工具集成方式，加速 AI 代理和复杂工作流的开发。\n    2. **增强发现**: 提供高效的搜索、过滤和分类机制，使用户能够快速找到所需的 MCP 服务器。\n    3. **建立信任**: 通过质量/安全/许可证指标和社区反馈机制，帮助用户评估服务器的可信度和质量。\n    4. **促进生态**: 鼓励开发者提交和维护服务器，形成一个活跃的、社区驱动的 MCP 生态系统。\n    5. **降低锁定**: 促进不同 LLM 提供商和供应商之间的互操作性，减少供应商锁定。\n    6. **自动化支持**: 为 AI Agent 提供结构化的元数据和 API 访问，支持自动化开发工作流。\n\n## 2. 核心实体分析\n\n### 实体1: MCP 服务器 (MCP_Server)\n\n- **描述**: MCP 服务器是提供特定功能的轻量级程序，通过标准化的 Model Context Protocol 暴露功能（资源、工具、提示）。它是市场平台的核心展示对象。\n- **主要属性**:\n    - **基本信息**: 服务器名称、作者/组织、简要描述、详细描述、上次更新日期、添加日期。\n    - **技术属性**: 编程语言、操作系统兼容性、许可证类型、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL。\n    - **功能定义**: 提供的能力类型（资源、工具、提示）、工具定义（名称、描述、示例提示、JSON Schema 定义）。\n    - **集成与部署**: 设置说明、安装方法、故障排除提示。\n    - **质量与流行度**: 质量评分（Asecurity、Alicense、Aquality）、每周下载量、GitHub 星数、最近 GitHub 星数。\n    - **分类**: 标签/关键词、所属类别。\n    - **状态**: 草稿、待审核、已发布、已拒绝、已下架（新增）。\n- **业务规则**:\n    - 每个 MCP 服务器必须有一个唯一的标识符 (slug)。\n    - 服务器内的每个工具定义必须有唯一的工具名称。\n    - 质量指标（Asecurity、Alicense、Aquality）应基于可验证的数据或人工审核生成。\n    - 提交的服务器信息必须完整且准确，特别是工具的 JSON Schema 定义。\n    - 服务器的源代码 URL 和问题跟踪器 URL 必须是有效的。\n    - 服务器状态流转：草稿 -> 待审核 -> (已发布 / 已拒绝)；已发布 -> 待更新审核 -> (已发布 / 已拒绝)；已发布 -> 已下架。\n- **关系**:\n    - 与“用户”：一个用户（开发者）可以提交和管理多个 MCP 服务器。\n    - 与“类别”：一个 MCP 服务器可以属于多个类别。\n    - 与“评论”：一个 MCP 服务器可以有多个评论和评分。\n    - 与“反馈”：一个 MCP 服务器可以接收有用性反馈。\n    - 与“版本”（新增实体）：一个 MCP 服务器可以有多个版本。\n- **MCP相关性**: MCP 服务器是 MCP 生态系统的核心组成部分，它通过标准化协议（MCP）向 LLM 暴露功能。市场平台是这些服务器的中央注册和发现门户，对于 MCP 生态系统的普及和发展至关重要。\n\n### 实体2: 用户 (User)\n\n- **描述**: 平台的用户，包括 MCP 服务器开发者、最终用户、AI 代理和平台管理员。他们可以注册、登录、管理个人资料，并根据角色执行不同的操作（如提交服务器、提供反馈、浏览）。\n- **主要属性**:\n    - 用户ID、用户名、电子邮件、密码哈希、个人资料名称、角色（开发者、用户、管理员）。\n    - 个人简介、头像（UserProfile）。\n    - 注册日期、上次登录日期。\n- **业务规则**:\n    - 用户名和电子邮件必须唯一。\n    - 密码必须加密存储。\n    - 不同角色拥有不同的操作权限。\n    - 开发者必须登录才能提交或编辑 MCP 服务器。\n- **关系**:\n    - 与“MCP 服务器”：开发者用户可以提交和管理 MCP 服务器。\n    - 与“评论”：用户可以对 MCP 服务器发表评论。\n    - 与“反馈”：用户可以提供有用性反馈。\n- **MCP相关性**: 用户是 MCP 生态系统的参与者，包括服务器的生产者（开发者）和消费者（最终用户、AI 代理）。用户的参与和贡献是市场平台成功的关键。\n\n### 实体3: 类别 (Category)\n\n- **描述**: 用于对 MCP 服务器进行分类和组织，方便用户进行筛选和发现。\n- **主要属性**:\n    - 类别ID、名称、slug（唯一标识符）、描述、服务器计数。\n    - 创建日期、上次更新日期。\n- **业务规则**:\n    - 类别名称必须唯一。\n    - 类别计数应实时反映该类别下的服务器数量。\n- **关系**:\n    - 与“MCP 服务器”：多对多关系，一个类别可以包含多个 MCP 服务器，一个 MCP 服务器可以属于多个类别。\n- **MCP相关性**: 类别是 MCP 服务器发现机制的重要组成部分，有助于用户在庞大的服务器库中进行高效导航。\n\n### 实体4: 评论 (Review)\n\n- **描述**: 用户对 MCP 服务器的评价和反馈，包括评分和文字评论。\n- **主要属性**:\n    - 评论ID、评分（1-5星）、评论内容、时间戳。\n    - 状态（待审核、已发布、已拒绝）。\n- **业务规则**:\n    - 评论必须关联到一个用户和一个 MCP 服务器。\n    - 评论内容可能需要审核。\n    - 评分应影响 MCP 服务器的整体评分。\n    - 评论状态流转：提交 -> 待审核 -> (已发布 / 已拒绝)。\n- **关系**:\n    - 与“用户”：一个评论由一个用户发布。\n    - 与“MCP 服务器”：一个评论针对一个 MCP 服务器。\n- **MCP相关性**: 评论是 MCP 生态系统中社区互动和质量评估的重要组成部分，为其他用户提供参考，并帮助开发者改进服务器。\n\n### 实体5: 反馈 (Feedback)\n\n- **描述**: 用户对页面或服务器有用性的快速反馈（是/否）。\n- **主要属性**:\n    - 反馈ID、页面URL、是否有用（布尔值）、可选评论、时间戳。\n    - IP地址（用于匿名反馈的追踪）。\n- **业务规则**:\n    - 反馈可以匿名，也可以关联到用户。\n    - 反馈应被记录和聚合，用于平台改进。\n- **关系**:\n    - 可与“用户”关联。\n- **MCP相关性**: 反馈机制是 MCP 生态系统健康发展的重要信号，帮助平台了解用户需求和服务器的实际价值。\n\n### 实体6: 版本 (Version) - **新增实体**\n\n- **描述**: 记录 MCP 服务器的不同版本信息，包括代码版本、元数据更新等。\n- **主要属性**:\n    - 版本ID、版本号（例如 v1.0.0）、发布日期、更新日志/变更说明。\n    - 关联的 MCP 服务器ID。\n    - 源代码提交哈希（可选）。\n- **业务规则**:\n    - 每个 MCP 服务器可以有多个版本。\n    - 版本号必须遵循语义化版本控制规范（例如 Major.Minor.Patch）。\n    - 每次服务器信息或代码有重大更新时，应创建新版本。\n- **关系**:\n    - 与“MCP 服务器”：一对多关系，一个 MCP 服务器可以有多个版本。\n- **MCP相关性**: 版本管理对于 MCP 服务器的迭代开发、兼容性维护和用户追踪变更至关重要，尤其是在 AI Agent 自动化集成时，需要明确的版本依赖。\n\n### 实体7: 质量指标 (QualityMetric) - **新增实体**\n\n- **描述**: 独立记录 MCP 服务器的各项质量指标，并可追溯其生成方式。\n- **主要属性**:\n    - 指标ID、MCP 服务器ID、指标类型（Asecurity, Alicense, Aquality）。\n    - 评分值（例如 A, B, C 或具体分数）。\n    - 生成方式（自动化分析、人工审核、第三方认证）。\n    - 生成/更新日期。\n    - 详细报告链接（可选）。\n- **业务规则**:\n    - 每个 MCP 服务器可以有多个质量指标。\n    - 质量指标应定期更新或在服务器提交/更新时重新评估。\n    - 评分应基于透明和可验证的规则。\n- **关系**:\n    - 与“MCP 服务器”：一对多关系，一个 MCP 服务器可以有多个质量指标记录。\n- **MCP相关性**: 质量指标是用户信任和采用的关键，独立实体化有助于更灵活地管理、更新和展示这些指标，并支持更复杂的评估逻辑。\n\n### 特别关注的MCP核心实体\n\n- **MCP服务器**:\n    - **服务器的元数据**: 包含名称、作者、描述、许可证、编程语言、操作系统兼容性、质量指标（Asecurity, Alicense, Aquality）、远程能力、HTTP 连接 URL、源代码 URL、问题跟踪器 URL、上次更新日期、流行度指标（下载量、GitHub 星数）。这些元数据对于 AI Agent 进行自动化发现和集成至关重要。\n    - **功能**: 资源、工具、提示。特别是工具，需要详细的定义（名称、描述、示例提示、JSON Schema），以便 LLM 能够正确调用。\n    - **版本管理**: 明确支持服务器代码和元数据的版本控制，记录每次更新的变更日志。\n- **工具定义**:\n    - **结构化**: 必须支持结构化的工具定义，包括名称、描述、示例提示和最重要的 JSON Schema 定义，这直接关系到 LLM 的调用能力和准确性。\n    - **自动生成**: 平台应考虑支持或鼓励开发者使用 SDK 自动生成工具定义，以简化提交流程并确保数据质量。\n- **用户角色**:\n    - **开发者**: 提交、管理和更新 MCP 服务器。\n    - **最终用户**: 发现、评估和使用 MCP 服务器，提供反馈。\n    - **AI Agent**: 通过 API 编程访问市场数据，自动化服务器发现和集成。\n    - **管理员**: 审核内容、管理用户、监控平台。\n- **质量评估**:\n    - **Asecurity**: 无已知漏洞。\n    - **Alicense**: 宽松许可证。\n    - **Aquality**: 确认可用。\n    - 这些指标是用户信任和采用的关键，平台需要机制来生成或验证这些评分，可能涉及自动化分析和人工审核。\n- **开发者生态**:\n    - **服务器提交**: 提供详细的表单和结构化输入，支持富文本、代码块和模式验证。\n    - **审核流程**: 对新提交和更新进行管理审核，确保内容质量和合规性。\n    - **社区反馈**: 评论、评分、有用性反馈、问题报告，形成开发者和用户之间的互动循环。\n    - **社交集成**: 与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作。\n\n## 3. 功能需求\n\n### 高优先级需求\n\n- **FR-001**: **MCP 服务器列表展示** - 用户能够查看所有可用的 MCP 服务器列表，并显示关键元数据（名称、作者、质量指标、描述、语言、许可证、兼容性、更新日期）。\n  - 验收标准:\n    - 导航到服务器列表页面时，显示服务器总数和上次更新时间戳。\n    - 列表分页/无限滚动，每个条目显示指定元数据。\n    - 滚动到底部时，自动加载或显示“加载更多”按钮。\n  - 功能分类: 发现与浏览\n\n- **FR-002**: **MCP 服务器高级搜索与过滤** - 用户能够通过关键词、类别、编程语言、许可证类型、操作系统兼容性、官方状态和质量评分对服务器进行搜索和过滤。\n  - 验收标准:\n    - 搜索栏支持全文搜索服务器名称、描述、功能和标签。\n    - 类别筛选器显示每个类别的服务器计数，并能按类别过滤。\n    - 高级筛选器（语言、许可证、OS、官方、质量评分）能正确过滤结果。\n    - 支持“深度搜索”和“新搜索”功能。\n  - 功能分类: 发现与浏览\n\n- **FR-003**: **MCP 服务器排序** - 用户能够按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数和最近 GitHub 星数对服务器列表进行排序。\n  - 验收标准:\n    - 排序下拉菜单提供所有排序选项。\n    - 选择不同排序选项时，服务器列表按预期重新排序。\n  - 功能分类: 发现与浏览\n\n- **FR-004**: **MCP 服务器详情展示** - 用户能够查看单个 MCP 服务器的综合详情页面，包括其功能、设置、安装、故障排除、工具定义、质量指标、相关服务器和外部资源链接。\n  - 验收标准:\n    - 点击服务器列表中的条目，导航到其详情页面。\n    - 页面显示所有指定属性和功能（标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。\n    - “工具”部分清晰呈现名称、描述和示例提示。\n    - 提供“安装服务器”按钮、“查看源代码”和“报告问题”链接。\n    - 显示“相关 MCP 服务器”和“新 MCP 服务器”列表。\n    - 显示 HTTP 连接 URL。\n  - 功能分类: 详情查看\n\n- **FR-005**: **MCP 服务器提交** - 开发者能够向市场提交新的 MCP 服务器，并提供所有必要的结构化信息，包括工具的 JSON Schema 定义。\n  - 验收标准:\n    - 开发者登录后，点击“添加服务器”按钮，显示多步骤提交表单。\n    - 表单包含所有必需字段，支持富文本编辑器和代码块。\n    - 支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\n    - 提交后，服务器进入待审核状态，并收到确认消息。\n  - 功能分类: 内容管理\n\n### 中优先级需求\n\n- **FR-006**: **MCP 服务器编辑与更新** - 开发者能够编辑和更新其已提交的 MCP 服务器信息。\n  - 验收标准:\n    - 开发者登录后，在“我的服务器”仪表板中选择服务器进行编辑。\n    - 编辑表单预填充当前数据，允许修改并重新提交审核。\n    - 重新提交后，服务器的“上次更新”时间戳在批准后更新。\n  - 功能分类: 内容管理\n\n- **FR-007**: **用户注册与登录** - 用户能够注册新账户、登录和管理个人资料。\n  - 验收标准:\n    - 提供安全的注册和登录界面（支持 OAuth 或邮箱/密码）。\n    - 用户能够编辑显示名称、联系信息。\n    - 支持密码找回/重置功能。\n  - 功能分类: 用户管理\n\n- **FR-008**: **有用性反馈机制** - 用户能够对服务器列表或详情页面提供“这有用吗？是/否”的快速反馈。\n  - 验收标准:\n    - 页面上显示“这有用吗？是/否”按钮。\n    - 点击后，反馈被记录，按钮状态改变或消失。\n  - 功能分类: 社区互动\n\n- **FR-011**: **服务器版本管理** - 开发者能够为 MCP 服务器创建和管理不同版本，并查看版本历史。\n  - 验收标准:\n    - 开发者在编辑服务器时，可以选择创建新版本并提供版本号和更新日志。\n    - 服务器详情页面显示当前版本信息，并提供查看历史版本的入口。\n    - 用户可以根据版本号筛选或查看特定版本的服务器信息（如果适用）。\n  - 功能分类: 内容管理\n\n### 低优先级需求\n\n- **FR-009**: **MCP 服务器评论与评分** - 用户能够对 MCP 服务器留下评论和评分。\n  - 验收标准:\n    - 登录用户在服务器详情页面的“评论”部分能看到现有评论和提交新评论的表单。\n    - 用户可以选择评分（1-5星）并输入评论。\n    - 提交后，评论在审核后显示，并更新服务器的整体评分。\n  - 功能分类: 社区互动\n\n- **FR-010**: **平台管理仪表板** - 管理员能够管理用户账户、审核服务器提交和评论、管理类别以及查看平台分析数据。\n  - 验收标准:\n    - 管理员登录后，访问管理仪表板。\n    - 能够暂停/删除用户账户。\n    - 能够审核和批准/拒绝服务器提交和评论。\n    - 能够添加/编辑/删除类别。\n    - 能够查看服务器下载量、视图、搜索查询等分析数据。\n  - 功能分类: 平台管理\n\n- **FR-012**: **自动化质量指标生成** - 平台能够通过自动化工具分析 MCP 服务器的源代码或元数据，生成 Asecurity、Alicense、Aquality 评分。\n  - 验收标准:\n    - 开发者提交服务器后，系统自动触发质量分析流程。\n    - 分析结果自动填充或更新服务器的质量指标。\n    - 管理员可以手动触发或覆盖自动化结果。\n  - 功能分类: 平台管理 / 质量保障\n\n- **FR-013**: **API 访问市场数据** - 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序编程访问市场数据。\n  - 验收标准:\n    - 提供 API 文档，详细说明 MCP 服务器的查询、过滤、详情获取等接口。\n    - API 支持认证和授权机制。\n    - AI Agent 可以通过 API 自动化发现和集成 MCP 服务器。\n  - 功能分类: 集成与扩展\n\n- **FR-014**: **社交集成与分享** - 支持与 GitHub、Reddit、Discord 等平台集成，促进社区讨论和协作，并支持分享功能。\n  - 验收标准:\n    - 服务器详情页面提供一键分享到社交媒体的按钮。\n    - 平台可以集成外部社区讨论链接。\n    - 开发者可以关联其 GitHub 仓库，自动同步星数等数据。\n  - 功能分类: 社区互动\n\n## 4. 非功能需求\n\n### 性能要求\n\n- **NFR-001**: **响应时间**: 服务器列表页面加载时间应在 2 秒内，单个服务器详情页面加载时间应在 1.5 秒内（在正常网络条件下）。\n- **NFR-002**: **并发用户**: 系统应支持至少 1000 个并发用户浏览和搜索，且性能无明显下降。\n- **NFR-003**: **数据量**: 平台应能高效处理数万甚至数十万个 MCP 服务器条目，搜索和过滤操作应保持快速响应。\n\n### 安全要求\n\n- **NFR-004**: **用户认证与授权**: 采用安全的认证机制（如 OAuth 2.0），确保用户数据传输加密（HTTPS），并实施基于角色的访问控制。\n- **NFR-005**: **数据保护**: 用户敏感信息（如密码）必须加密存储。服务器提交的数据应进行输入验证和清理，防止注入攻击。\n- **NFR-006**: **内容审核**: 对用户提交的服务器信息和评论进行严格的内容审核，防止恶意或不当内容的发布。\n- **NFR-007**: **质量指标验证**: 平台应具备机制（自动化或人工）来验证或计算 Asecurity、Alicense、Aquality 等指标的真实性和准确性。\n- **NFR-014**: **API 安全**: 对所有 API 接口进行严格的身份验证、授权和速率限制，防止滥用和未经授权的访问。\n\n### 可用性要求\n\n- **NFR-008**: **用户界面**: 界面应直观、易于导航，提供清晰的指引和反馈。\n- **NFR-009**: **兼容性**: 平台应兼容主流浏览器和设备（桌面、移动）。\n- **NFR-010**: **错误处理**: 系统应提供友好的错误提示和恢复机制。\n\n### 可扩展性要求\n\n- **NFR-011**: **架构设计**: 采用微服务或领域驱动设计，确保系统各模块可独立扩展。\n- **NFR-012**: **API 优先**: 提供全面的 RESTful API 接口，支持 AI Agent 和其他应用程序的编程访问。\n- **NFR-013**: **数据存储**: 数据库设计应支持未来数据量的增长和查询性能优化。\n- **NFR-015**: **模块化设计**: 系统应采用高度模块化的设计，便于未来新增功能模块或替换现有模块。\n\n### 可维护性要求\n\n- **NFR-016**: **代码质量**: 代码应遵循统一的编码规范、注释规范，并进行充分的单元测试和集成测试。\n- **NFR-017**: **文档完善**: 提供详细的系统设计文档、API 文档和部署运维手册。\n- **NFR-018**: **日志与监控**: 系统应具备完善的日志记录和监控机制，便于故障排查和性能分析。\n\n## 5. 业务规则\n\n1.  **BR-001**: **MCP 服务器唯一性**: 每个 MCP 服务器在市场中必须具有唯一的 slug（URL 标识符）。\n    - 触发条件: 开发者提交新的 MCP 服务器。\n    - 执行动作: 系统检查 slug 的唯一性，若重复则提示开发者修改。\n\n2.  **BR-002**: **工具定义唯一性**: 在单个 MCP 服务器内部，其提供的所有工具的名称必须是唯一的。\n    - 触发条件: 开发者提交或编辑 MCP 服务器的工具定义。\n    - 执行动作: 系统验证工具名称的唯一性，若重复则提示错误。\n\n3.  **BR-003**: **质量指标生成/验证**: MCP 服务器的 Asecurity、Alicense、Aquality 评分应基于预定义的规则或人工审核结果生成/验证。\n    - 触发条件: MCP 服务器提交或更新，或定期进行质量评估。\n    - 执行动作: 系统根据规则计算或提示管理员进行审核，并更新评分。\n\n4.  **BR-004**: **服务器提交审核**: 所有新的 MCP 服务器提交和重大更新都必须经过管理员审核才能发布。\n    - 触发条件: 开发者提交新的 MCP 服务器或更新现有服务器。\n    - 执行动作: 服务器状态变为“待审核”，管理员收到通知并进行审核，审核通过后方可发布。\n\n5.  **BR-005**: **评论审核**: 用户提交的评论可能需要经过审核才能显示。\n    - 触发条件: 用户提交评论。\n    - 执行动作: 评论进入“待审核”状态，管理员审核通过后显示。\n\n6.  **BR-006**: **类别计数更新**: 当 MCP 服务器的类别关联发生变化时，相关类别的服务器计数应自动更新。\n    - 触发条件: MCP 服务器被添加、删除或其类别信息被修改。\n    - 执行动作: 系统自动重新计算并更新受影响类别的服务器计数。\n\n7.  **BR-007**: **开发者身份验证**: 只有已登录且具有开发者角色的用户才能提交或编辑 MCP 服务器。\n    - 触发条件: 用户尝试访问服务器提交/编辑功能。\n    - 执行动作: 系统检查用户登录状态和角色，若不符合则拒绝访问并提示登录或权限不足。\n\n8.  **BR-008**: **服务器版本号规范**: MCP 服务器的版本号必须遵循语义化版本控制规范（例如 Major.Minor.Patch）。\n    - 触发条件: 开发者提交新版本。\n    - 执行动作: 系统验证版本号格式，若不符合则提示错误。\n\n9.  **BR-009**: **服务器状态流转**: MCP 服务器的状态流转必须遵循预定义的流程（草稿 -> 待审核 -> (已发布 / 已拒绝)；已发布 -> 待更新审核 -> (已发布 / 已拒绝)；已发布 -> 已下架）。\n    - 触发条件: 管理员或开发者尝试更改服务器状态。\n    - 执行动作: 系统验证状态流转的合法性，若不符合则拒绝操作。\n\n## 6. 用户故事\n\n### 核心用户故事\n\n-   **US-001**: **作为一名用户**，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。\n    -   验收标准:\n        -   当我导航到服务器列表页面 (/mcp/servers) 时。\n        -   我看到“开源 MCP 服务器”的总数和整个列表的“上次更新”时间戳。\n        -   我看到一个分页/可滚动的 MCP 服务器条目列表。\n        -   每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）。\n        -   当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。\n\n-   **US-002**: **作为一名用户**，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。\n    -   验收标准:\n        -   当我位于服务器列表页面 (/mcp/servers) 时。\n        -   当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。\n        -   URL 更新以反映该类别（例如，/mcp/servers/categories/python）。\n        -   显示的列表仅显示标记为“Python”的服务器。\n        -   “Python”的类别计数准确（例如，“2,436”）。\n        -   每个显示的服务器条目都符合 US-001 的标准。\n\n-   **US-003**: **作为一名用户**，我想要按关键词搜索服务器，以便我能快速找到特定的服务器或功能。\n    -   验收标准:\n        -   当我位于服务器列表页面时。\n        -   当我在搜索栏中输入“区块链”并按 Enter 键时。\n        -   页面显示名称、描述或标签中包含“区块链”的服务器列表。\n        -   “深度搜索”和“新搜索”按钮可用。\n        -   每个显示的服务器条目都符合 US-001 的标准。\n\n-   **US-004**: **作为一名用户**，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。\n    -   验收标准:\n        -   当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。\n        -   我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）。\n        -   页面突出显示：服务器标题、作者、标签、许可证、兼容性。\n        -   页面包含导航链接：概述、Schema、相关服务器、评论、评分。\n        -   页面提供“查看源代码”和“报告问题”链接。\n        -   “您可以使用此服务器做什么？”部分详细说明了核心功能。\n        -   “此服务器有哪些集成可用？”部分概述了技术要求和集成点。\n        -   “设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）。\n        -   “安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）。\n        -   “故障排除”部分列出了常见问题和解决方案。\n        -   “工具”表格列出了每个工具的名称、描述和示例提示。\n        -   质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接。\n        -   “安装服务器”按钮和 HTTP 连接 URL 存在。\n        -   “相关 MCP 服务器”部分显示了类似服务器的列表。\n        -   “新 MCP 服务器”部分显示了最近添加的服务器。\n        -   “这有用吗？是/否”反馈按钮可用。\n\n-   **US-005**: **作为一名开发者**，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。\n    -   验收标准:\n        -   当我以开发者身份登录时。\n        -   当我点击“添加服务器”按钮时。\n        -   我看到一个多步骤表单，用于输入服务器详细信息。\n        -   表单包含所有必需字段，支持富文本编辑器和代码块。\n        -   支持添加多个功能、工具（含 JSON Schema）、集成步骤和故障排除提示。\n        -   提交后，服务器进入待审核状态，并收到确认消息。\n\n### 扩展用户故事\n\n-   **US-010**: **作为一名开发者**，我想要为我的 MCP 服务器创建新版本，以便我可以发布更新和变更日志。\n    -   验收标准:\n        -   当我以已提交服务器的作者身份登录时。\n        -   当我导航到我的“我的服务器”仪表板并选择一个服务器进行编辑时。\n        -   我看到一个选项可以创建新版本。\n        -   我可以输入版本号（例如 v1.0.1）和更新日志/变更说明。\n        -   提交新版本后，服务器的最新版本信息会更新，并且历史版本可供查看。\n\n-   **US-011**: **作为一名 AI Agent**，我想要通过 API 编程访问 MCP 服务器列表和详情，以便自动化发现和集成过程。\n    -   验收标准:\n        -   我可以使用提供的 API 密钥和凭证进行认证。\n        -   我可以通过 API 查询 MCP 服务器列表，并应用过滤和排序条件。\n        -   我可以通过 API 获取单个 MCP 服务器的详细信息，包括其工具定义和 JSON Schema。\n        -   API 响应格式清晰且易于解析。\n\n## 7. 关键业务流程\n\n### 流程1: MCP 服务器发现与浏览\n\n-   **描述**: 用户通过市场平台查找、筛选和查看 MCP 服务器的详细信息。\n-   **参与者**: 用户（包括最终用户、AI 代理）。\n-   **主要步骤**:\n    1.  **用户访问市场平台**: 用户通过浏览器或 API 访问 MCP 服务器市场。\n    2.  **浏览服务器列表**: 平台显示所有可用的 MCP 服务器列表，包含关键元数据。\n    3.  **筛选与排序**: 用户根据需求选择类别、应用高级筛选条件（如语言、许可证、质量评分）或选择排序方式（如更新日期、下载量）。\n    4.  **关键词搜索**: 用户在搜索栏输入关键词，查找特定服务器。\n    5.  **查看服务器详情**: 用户点击感兴趣的服务器条目，进入其详细页面。\n    6.  **评估服务器信息**: 用户在详情页面查看服务器的完整功能、集成说明、工具定义、质量指标和社区反馈。\n    7.  **获取集成信息**: 用户根据详情页面的说明，获取安装、配置和使用服务器所需的信息（如 HTTP 连接 URL、安装命令）。\n-   **输入**: 用户搜索关键词、筛选条件、排序选项、点击操作。\n-   **输出**: 过滤/排序后的服务器列表、单个服务器的详细信息、集成指南。\n\n### 流程2: MCP 服务器提交与发布\n\n-   **描述**: 开发者向市场平台提交新的 MCP 服务器，并经过审核后发布。\n-   **参与者**: 开发者、平台管理员。\n-   **主要步骤**:\n    1.  **开发者登录**: 开发者使用其账户登录市场平台。\n    2.  **发起提交**: 开发者点击“添加服务器”按钮，进入提交流程。\n    3.  **填写服务器信息**: 开发者在多步骤表单中填写服务器的各项详细信息，包括基本信息、技术属性、功能定义（特别是工具的 JSON Schema）、集成与部署说明等。\n    4.  **提交审核**: 开发者完成信息填写后，提交服务器信息。服务器状态变为“待审核”。\n    5.  **管理员审核**: 平台管理员收到新提交通知，对服务器信息进行审查，包括内容的完整性、准确性、合规性以及质量指标的初步评估。\n    6.  **审核结果反馈**:\n        -   **审核通过**: 管理员批准发布，服务器状态变为“已发布”，并在市场中可见。开发者收到发布成功的通知。\n        -   **审核拒绝**: 管理员拒绝发布，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n    7.  **服务器发布**: 审核通过的服务器在市场中可见，用户可以发现和使用。\n-   **输入**: 开发者提交的服务器详细信息。\n-   **输出**: 待审核的服务器记录、审核结果通知、已发布的 MCP 服务器。\n\n### 流程3: MCP 服务器信息更新与版本发布\n\n-   **描述**: 开发者更新其已提交的 MCP 服务器信息，并经过审核后生效，可选择发布新版本。\n-   **参与者**: 开发者、平台管理员。\n-   **主要步骤**:\n    1.  **开发者登录**: 开发者登录市场平台。\n    2.  **访问我的服务器**: 开发者导航到其“我的服务器”仪表板，查看其已提交的服务器列表。\n    3.  **选择编辑**: 开发者选择需要更新的 MCP 服务器，点击编辑按钮。\n    4.  **修改服务器信息**: 编辑表单预填充当前数据，开发者修改需要更新的字段。\n    5.  **选择版本操作**: 开发者可以选择：\n        -   **仅更新当前版本**: 修改信息后直接提交，不创建新版本。\n        -   **创建新版本**: 提供新版本号和更新日志，提交后创建新版本。\n    6.  **重新提交审核**: 开发者完成修改后，重新提交服务器信息。服务器状态可能暂时变为“待更新审核”。\n    7.  **管理员审核更新**: 平台管理员对更新内容进行审查。\n    8.  **更新结果反馈**:\n        -   **审核通过**: 管理员批准更新，服务器信息在市场中更新。如果创建了新版本，则新版本发布。开发者收到更新成功的通知。\n        -   **审核拒绝**: 管理员拒绝更新，并提供拒绝原因。开发者收到拒绝通知，并可根据反馈修改后重新提交。\n-   **输入**: 开发者修改后的服务器详细信息、新版本信息（可选）。\n-   **输出**: 更新后的 MCP 服务器信息、新发布的版本记录（可选）。\n\n### 流程4: 用户反馈与社区互动\n\n-   **描述**: 用户对 MCP 服务器提供反馈、评论和评分，促进社区互动。\n-   **参与者**: 用户、MCP 服务器开发者、平台管理员。\n-   **主要步骤**:\n    1.  **用户浏览服务器**: 用户在服务器列表或详情页面浏览 MCP 服务器。\n    2.  **提供有用性反馈**: 用户点击“这有用吗？是/否”按钮，快速表达对页面或服务器的有用性评价。\n    3.  **撰写评论与评分**: 登录用户在服务器详情页面的评论区，输入评论内容并选择评分。\n    4.  **提交评论**: 用户提交评论。\n    5.  **评论审核（可选）**: 平台管理员对评论进行审核（根据配置）。\n    6.  **评论发布**: 审核通过的评论显示在服务器详情页面，并更新服务器的整体评分。\n    7.  **开发者查看反馈**: MCP 服务器开发者可以查看用户对其服务器的评论和反馈，以便改进。\n-   **输入**: 用户有用性选择、评论内容、评分。\n-   **输出**: 记录的有用性反馈、发布的评论、更新的服务器评分。\n\n---\n\n**总结改进点：**\n\n1.  **新增业务实体**:\n    *   **版本 (Version)**: 明确了 MCP 服务器的版本管理需求，包括版本号、发布日期、更新日志等，并建立了与 MCP 服务器的一对多关系。这对于追踪服务器迭代和兼容性至关重要。\n    *   **质量指标 (QualityMetric)**: 将质量指标独立为一个实体，可以更灵活地管理和更新 Asecurity、Alicense、Aquality 等评分，并记录其生成方式，增强透明度和可信度。\n2.  **增强现有实体属性**:\n    *   **MCP 服务器**: 增加了 `状态` 属性（草稿、待审核、已发布、已拒绝、已下架），并明确了状态流转规则，更全面地覆盖了服务器的生命周期管理。\n    *   **用户**: 增加了 `注册日期` 和 `上次登录日期`，有助于用户管理和分析。\n    *   **类别**: 增加了 `创建日期` 和 `上次更新日期`。\n    *   **评论**: 增加了 `状态` 属性（待审核、已发布、已拒绝），并明确了状态流转，细化了评论审核流程。\n    *   **反馈**: 增加了 `IP地址` 属性，用于匿名反馈的追踪。\n3.  **补充功能需求**:\n    *   **FR-011: 服务器版本管理**: 明确了开发者创建和管理服务器版本的功能。\n    *   **FR-012: 自动化质量指标生成**: 提出了通过自动化工具生成质量评分的需求，提升效率和客观性。\n    *   **FR-013: API 访问市场数据**: 强调了为 AI Agent 提供编程访问接口的重要性，这是原始文档中“MCP 目录 API”的明确化。\n    *   **FR-014: 社交集成与分享**: 补充了社区互动和推广相关的需求。\n4.  **完善业务规则**:\n    *   **BR-008: 服务器版本号规范**: 增加了对版本号格式的约束。\n    *   **BR-009: 服务器状态流转**: 明确了 MCP 服务器生命周期中的状态转换规则。\n5.  **扩展用户故事**:\n    *   **US-010: 创建新版本**: 对应新增的版本管理功能。\n    *   **US-011: API 访问**: 对应 AI Agent 通过 API 自动化访问的需求。\n6.  **细化业务流程**:\n    *   **流程3: MCP 服务器信息更新与版本发布**: 将更新和版本发布合并，并细化了开发者选择“仅更新”或“创建新版本”的步骤。\n7.  **增加非功能需求**:\n    *   **NFR-014: API 安全**: 针对新增的 API 访问，强调了其安全要求。\n    *   **NFR-015: 模块化设计**: 强调了架构层面的模块化要求。\n    *   **可维护性要求**: 新增了代码质量、文档完善、日志与监控等方面的非功能需求，提升了报告的全面性。\n\n这些改进使得分析结果更加完整、准确，并能更好地指导后续的开发工作。"}
