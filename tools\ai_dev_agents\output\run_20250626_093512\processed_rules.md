<!-- 
处理后的开发规则
生成时间: 2025-06-26 09:35:45
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发
- **主要改进**: 
  - 整合重复内容，消除冗余
  - 优化组织结构，增强逻辑性
  - 补充测试规范和工程实践细节
  - 明确命名规范和约束条件

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计(DDD)为核心
  - 严格分层架构(接口层→应用层→领域层→基础设施层)
  - 模块化组织，业务功能优先
- **开发方法论**:
  - 测试驱动开发(TDD)
  - 依赖倒置原则(DIP)
  - 单一职责原则(SRP)
- **质量标准理念**:
  - 类型提示全覆盖
  - 文档与代码同步
  - 自动化测试保障

## 3. 技术规范
- **技术栈要求**:
  - Web框架: FastAPI
  - 数据验证: Pydantic
  - ORM: SQLAlchemy + Alembic
- **框架使用规范**:
  - 领域层禁止引入框架依赖
  - 接口层仅处理HTTP相关逻辑
- **编码标准**:
  - 严格遵循PEP 8
  - 强制类型提示
  - 文档字符串使用英文

## 4. 架构约束
- **分层架构规则**:
  - 单向依赖: 接口层→应用层→领域层←基础设施层
  - 领域层纯粹性: 禁止框架依赖
- **模块组织方式**:
  - 按业务功能划分模块(如auth, orders)
  - 模块内实现完整四层架构
- **依赖关系约束**:
  - 跨模块调用必须通过应用层接口
  - 通用代码置于common目录

## 5. 代码质量标准
- **代码风格规范**:
  - 业务子域前缀命名(如user_models.py)
  - 禁止技术实现细节命名
- **测试要求**:
  - 单元测试覆盖领域模型和应用服务
  - 集成测试验证接口层
  - BDD风格测试命名
- **文档标准**:
  - OpenAPI文档全覆盖
  - 注释与代码同步更新
  - 英文文档强制要求

## 6. 工程实践
- **开发流程**:
  - Domain First开发顺序
  - 原子化Git提交
- **版本控制**:
  - 遵循约定式提交规范
  - 提交信息包含业务模块scope
- **部署规范**:
  - 数据库迁移使用Alembic
  - 禁止自动建表

## 7. 最佳实践
- **开发技巧**:
  - 业务子域分离原则
  - 依赖注入解耦
- **常见问题解决方案**:
  - ORM模型与领域模型转换
  - 跨模块服务调用
- **性能优化建议**:
  - 外键字段索引
  - 批量操作优化

## 8. 约束与限制
- **禁止的做法**:
  - 领域层引入框架依赖
  - 直接访问其他模块领域层
  - 修改测试用例使测试通过
- **必须遵守的规则**:
  - UUID主键强制要求
  - 业务语义命名规范
  - 测试伴随开发
- **异常处理原则**:
  - 接口层处理HTTP异常
  - 领域层定义业务异常

## 质量要求
- **完整性**: 覆盖架构设计、编码、测试、部署全流程
- **一致性**: 各层职责明确，无逻辑冲突
- **可操作性**: 具体示例丰富，可直接指导开发
- **可维护性**: 模块化结构，便于扩展
- **专业性**: 准确使用DDD和Python技术术语