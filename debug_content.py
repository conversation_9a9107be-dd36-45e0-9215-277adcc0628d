#!/usr/bin/env python3
import sys
import os
import json
import re

sys.path.append('.')

# 读取实际的业务分析数据
llm_file = 'tools/ai_dev_agents/output/run_20250626_093512/llm_interactions/business_analyzer_llm_interactions.jsonl'
if os.path.exists(llm_file):
    with open(llm_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        # 获取最后一个响应
        business_content = ''
        for line in reversed(lines):
            data = json.loads(line)
            if data.get('interaction_type') == 'response':
                business_content = data.get('content', '')
                break
    
    print("业务分析内容片段:")
    # 查找包含US-的行
    lines = business_content.split('\n')
    for i, line in enumerate(lines):
        if 'US-' in line:
            print(f"Line {i}: {line}")
            # 打印前后几行作为上下文
            for j in range(max(0, i-2), min(len(lines), i+3)):
                if j != i:
                    print(f"  {j}: {lines[j]}")
            print("---")
    
    # 尝试简单的US-模式匹配
    simple_pattern = r'US-\d+'
    matches = re.findall(simple_pattern, business_content)
    print(f"\n找到的US-模式: {matches}")
    
else:
    print('LLM交互文件不存在')
