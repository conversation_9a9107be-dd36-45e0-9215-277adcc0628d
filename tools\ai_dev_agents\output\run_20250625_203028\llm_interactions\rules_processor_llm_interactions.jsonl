{"timestamp": "2025-06-25T20:30:28.365459", "agent_name": "rules_processor", "interaction_id": 1, "task_description": "规则处理", "interaction_type": "request", "content": [{"role": "system", "content": "**重要：请务必使用中文回答所有问题，所有输出内容必须是中文。**\n\n你是一个专业的技术规范专家，专门负责理解、分析和重新生成高质量的开发规则文档。\n\n你的任务是：\n1. 理解输入的规则文件内容\n2. 分析规则的完整性、一致性和实用性\n3. 重新组织和生成标准化的规则文档\n4. 确保规则内容清晰、准确、易于理解和执行\n\n## 处理原则\n\n### 内容分析\n- **完整性检查**: 识别规则覆盖的领域和可能缺失的重要方面\n- **一致性验证**: 发现并解决规则间的矛盾和冲突\n- **实用性评估**: 确保规则具有可操作性和实际指导价值\n\n### 内容重组\n- **逻辑结构**: 按照重要性和逻辑关系重新组织内容\n- **层次分明**: 建立清晰的规则层次和分类\n- **消除冗余**: 合并重复内容，避免信息冗余\n\n### 质量提升\n- **语言规范**: 使用准确、专业的技术术语\n- **表达清晰**: 确保规则表述明确、无歧义\n- **示例补充**: 在必要时添加具体示例说明\n\n## 输出格式要求\n\n请严格按照以下Markdown格式输出处理后的规则：\n\n# 处理后的开发规则\n\n## 1. 规则概述\n- **规则来源**: [原始规则文件列表]\n- **处理时间**: [处理时间]\n- **规则范围**: [规则适用范围]\n- **主要改进**: [本次处理的主要改进点]\n\n## 2. 核心原则\n[核心开发原则和理念，包括但不限于：]\n- 架构设计原则\n- 开发方法论\n- 质量标准理念\n\n## 3. 技术规范\n[具体的技术实现规范，包括：]\n- 技术栈要求\n- 框架使用规范\n- 编码标准\n\n## 4. 架构约束\n[架构设计相关的约束和要求，包括：]\n- 分层架构规则\n- 模块组织方式\n- 依赖关系约束\n\n## 5. 代码质量标准\n[代码质量相关的标准和要求，包括：]\n- 代码风格规范\n- 测试要求\n- 文档标准\n\n## 6. 工程实践\n[工程实践相关的规范，包括：]\n- 开发流程\n- 版本控制\n- 部署规范\n\n## 7. 最佳实践\n[推荐的最佳实践，包括：]\n- 开发技巧\n- 常见问题解决方案\n- 性能优化建议\n\n## 8. 约束与限制\n[明确的约束和限制，包括：]\n- 禁止的做法\n- 必须遵守的规则\n- 异常处理原则\n\n## 质量要求\n\n确保输出的规则：\n- **完整性**: 覆盖开发过程的各个重要方面\n- **一致性**: 规则之间无矛盾，逻辑自洽\n- **可操作性**: 规则具体明确，便于执行\n- **可维护性**: 结构清晰，便于后续更新维护\n- **专业性**: 使用准确的技术术语和表达\n"}, {"role": "user", "content": "请处理以下规则文件内容，生成标准化的开发规则文档：\n\n**选中的规则文件**:\nrules.md\n\n**规则文件内容**:\n## 来源文件: rules.md\n\n# Roo AI - FastAPI & DDD 项目代码生成规范\n\n本文档为 AI 代码生成助手（Roo）提供指导，确保在基于 FastAPI 和领域驱动设计（DDD）的 Python 项目中生成的代码符合既定的架构原则和高质量标准。\n\n## 1. 核心技术栈与原则\n\n- **Web 框架**: **FastAPI**\n- **数据验证与建模**: **Pydantic**\n- **ORM 与数据库**: **SQLAlchemy** (与 Alembic 配合进行数据库迁移)\n- **代码风格**: 严格遵循 **PEP 8** 规范。\n- **类型提示 (Type Hinting)**: **强制要求**。所有函数签名和变量声明都必须包含明确的类型提示。\n- **核心思想**: **领域驱动设计 (DDD)**。所有开发活动都应围绕领域模型展开，并严格遵守分层架构的职责分离原则。\n- **文档语言**: **强制要求**。所有注释、文档字符串和文档内容必须使用**英文**编写。\n\n## 2. 架构设计与项目结构\n\n整体架构遵循领域驱动设计（DDD）和分层原则，但项目文件结构以 **业务模块（Module）** 为第一组织层级。\n\n### 2.1 项目整体结构\n\n项目首先按业务功能（如 `auth`, `orders`）划分模块，每个模块内部独立应用四层架构。通用代码则放置在根目录的 `common` 文件夹下。\n\n```\n.\n├── modules/\n│   ├── auth/  # 用户认证模块\n│   │   ├── __init__.py\n│   │   ├── application/\n│   │   ├── domain/\n│   │   ├── infrastructure/\n│   │   └── interfaces/\n│   └── orders/ # 订单模块\n│       ├── __init__.py\n│       ├── application/\n│       ├── domain/\n│       ├── infrastructure/\n│       └── interfaces/\n├── common/  # 通用代码\n│   ├── __init__.py\n│   ├── security.py\n│   └── db/\n│       └── database.py\n├── tests/\n└── main.py\n```\n\n### 2.2 模块内部分层架构\n\n每个业务模块（例如 `modules/auth/`）内部都遵循以下经典四层 DDD 架构。\n\n```mermaid\ngraph TD;\n    subgraph \"模块外部\"\n        Client[客户端 / 其他模块]\n    end\n\n    subgraph \"接口层 (Interfaces Layer)\"\n        A[<b>interfaces/</b><br>FastAPI Routers<br>Schemas (Pydantic)<br><i>职责：处理HTTP请求，数据转换</i>]\n    end\n\n    subgraph \"应用层 (Application Layer)\"\n        B[<b>application/</b><br>Application Services<br>Use Cases / DTOs<br><i>职责：编排领域逻辑，协调用例</i>]\n    end\n\n    subgraph \"领域层 (Domain Layer)\"\n        C[<b>domain/</b><br>Entities & Aggregates<br>Value Objects<br>Repository Interfaces<br><i>职责：核心业务逻辑</i>]\n    end\n\n    subgraph \"基础设施层 (Infrastructure Layer)\"\n        D[<b>infrastructure/</b><br>Repository Implementations<br>ORM Models (SQLAlchemy)<br><i>职责：技术实现</i>]\n    end\n\n    Client --> A;\n    A --> B;\n    B --> C;\n    D -- \"实现 (Implements)\" --> C;\n```\n*注意：上图描述了单个模块内部的依赖关系。*\n\n### 2.3 核心依赖规则 (不可违背)\n\n1.  **模块化**: 新功能应在 `modules/` 目录下创建新的业务模块，或在现有模块内扩展。\n2.  **模块内部单向依赖**: 在一个模块内，依赖关系是严格单向的：`Interfaces` → `Application` → `Domain`。\n3.  **依赖倒置**: `Application` 层依赖 `Domain` 层定义的 **抽象接口** (例如 `UserRepository`)，而 `Infrastructure` 层 **实现** 这些接口。\n4.  **领域层纯粹性**: `Domain` 层是项目的核心。**绝对禁止** 在该层中出现任何对 `fastapi`, `sqlalchemy` 或其他外部框架的 `import` 语句。\n5.  **模块间通信**: 一个业务模块如果需要调用另一个模块的功能，**必须** 通过目标模块的 `Application` 层服务接口进行，严禁直接访问其他模块的 `Domain` 或 `Infrastructure` 层。\n6.  **通用模块使用**: 根目录 `common/` 或 `utils/` 下的通用代码可以被任何层级按需导入，但通用代码本身不能反向依赖任何业务模块。\n\n## 3. 各层详细实现规范\n\n以下规范适用于单个业务模块内（例如 `modules/auth/`）。\n\n### 3.1 `interfaces/` (接口层)\n\n- **位置**: `modules/{module_name}/interfaces/`\n- **内容**:\n  - `{module_name}_api.py` (例如 `auth_api.py`): 定义 FastAPI `APIRouter`，文件名应体现业务模块。\n  - `schemas.py`: 定义此模块 API 专用的 Pydantic 请求/响应模型。\n- **职责**:\n  - 仅负责 HTTP 协议相关事务（解析请求、序列化响应、处理 HTTP 错误），不包含任何业务逻辑。\n  - 通过依赖注入调用应用层服务。\n  - **API 文档要求**: 所有端点必须使用 FastAPI 装饰器包含全面的 OpenAPI 文档。文档内容必须与代码变更保持同步更新。\n\n### 3.2 `application/` (应用层)\n\n- **位置**: `modules/{module_name}/application/`\n- **内容**:\n  - `services.py`: 定义应用服务类（如 `AuthService`），封装业务用例。\n  - `dtos.py` (可选): 定义应用层内部或与接口层交互的数据传输对象。\n- **职责**: 编排领域对象和仓库来完成一个完整的业务用例。事务边界通常在这里定义。\n\n### 3.3 `domain/` (领域层)\n\n- **位置**: `modules/{module_name}/domain/`\n- **内容**:\n  - `models.py`: 定义领域实体（Entities）和值对象（Value Objects）。应包含丰富的业务方法。\n  - `repositories.py`: 定义数据持久化的抽象接口（如 `UserRepository`）。\n- **职责**: 体现核心业务逻辑和规则，保持纯粹，不依赖任何外部框架。\n\n### 3.4 `infrastructure/` (基础设施层)\n\n- **位置**: `modules/{module_name}/infrastructure/`\n- **内容**:\n  - `repositories.py`: 提供 `Domain` 层仓库接口的具体实现。为保持清晰，此文件可包含模块内所有仓库的实现。\n    - **命名规范**: 实现类的命名应遵循 `[InterfaceName]Impl` 格式，例如 `UserRepositoryImpl`。**严禁**在命名中包含具体技术（如 `SQLAlchemy`）。\n  - `orm.py` (或 `models.py`): 定义与数据库表严格映射的 SQLAlchemy 模型。\n- **职责**: 实现所有与外部世界（数据库、文件系统、第三方 API）交互的技术细节。负责领域模型与 ORM 模型之间的转换。\n\n## 4. 代码生成约束与流程\n\n### 案例：在 `orders` 模块中添加“创建订单”功能\n\n1.  **创建/定位模块**: 确保 `modules/orders/` 模块存在。\n2.  **Domain First (`modules/orders/domain/`)**:\n    - 在 `models.py` 中创建 `Order` 实体。\n    - 在 `repositories.py` 中定义抽象的 `OrderRepository` 接口（**注意**: 非 `IOrderRepository`）。\n3.  **Application (`modules/orders/application/`)**:\n    - 在 `services.py` 中创建 `OrderService`。\n    - 注入 `OrderRepository` 接口，并创建 `create_order` 方法。\n4.  **Infrastructure (`modules/orders/infrastructure/`)**:\n    - 在 `orm.py` 中创建 `OrderORM` SQLAlchemy 模型。\n    - 在 `repositories.py` 中创建 `OrderRepositoryImpl`，实现 `OrderRepository` 接口。\n5.  **Interface (`modules/orders/interfaces/`)**:\n    - 在 `schemas.py` 中创建 `OrderCreateRequest` 和 `OrderResponse` Pydantic 模型。\n    - 在 `api.py` 中创建一个新的 POST `/orders/` 端点，并使用 `Depends` 注入 `OrderService`。\n6.  **集成到主应用**:\n    - 在顶层的 `main.py` 文件中，导入 `orders` 模块的 `api` router，并使用 `app.include_router()` 将其挂载到主应用中。\n## 5. 自动化测试规范 (Automated Testing)\n\n为了确保代码质量和系统稳定性，所有代码生成和修改都必须伴随着严格的自动化测试。\n\n### 5.1 测试框架与工具\n\n- **主要框架**: **Pytest** 是项目的标准测试框架。\n- **HTTP 测试**: 使用 FastAPI 提供的 `TestClient` 进行接口测试。\n- **Mocking**: 使用 `unittest.mock` 或 `pytest-mock` 来模拟依赖项，实现单元测试的隔离性。\n\n### 5.2 测试目录结构\n\n`tests` 目录的结构必须与 `modules` 目录保持一致，按业务模块进行组织。\n\n```\ntests/\n├── __init__.py\n├── auth/  # 'auth' 模块的测试\n│   ├── __init__.py\n│   ├── test_application_services.py\n│   ├── test_domain_models.py\n│   └── test_interfaces_api.py\n└── orders/ # 'orders' 模块的测试\n    ├── __init__.py\n    ├── test_application_services.py\n    ├── test_domain_models.py\n    └── test_interfaces_api.py\n```\n\n### 5.3 测试类型与分层\n\n- **单元测试 (Unit Tests)**:\n  - **位置**: `tests/{module_name}/test_domain_models.py`, `tests/{module_name}/test_application_services.py`\n  - **目标**: 专注于测试单一模块或函数的逻辑正确性。\n  - **`Domain Layer` 测试**: 测试实体（Entity）的业务方法。这些测试不应有任何外部依赖。\n  - **`Application Layer` 测试**: 测试应用服务（Service）的业务流程。在此类测试中，应**模拟 (Mock)** 仓库（Repository）和其他外部依赖项，确保只测试服务本身的逻辑。\n\n- **接口/集成测试 (Interface/Integration Tests)**:\n  - **位置**: `tests/{module_name}/test_interfaces_api.py`\n  - **目标**: 测试从 HTTP 端点到数据库的完整流程，验证各层之间的集成是否正确。\n  - **方法**: 使用 `TestClient` 发起真实的网络请求，并与测试数据库进行交互来验证端到端的行为。\n  - **依赖覆盖**: 依赖注入系统应该被用来覆盖（override）真实的依赖，例如使用一个独立的测试数据库。\n\n### 5.4 核心测试原则\n\n1.  **测试伴随**: 任何新的功能代码或对现有逻辑的修改，都**必须**有相应的测试用例进行覆盖。\n2.  **极狐验证**: 代码生成后，必须立即运行相关的测试套件。所有测试用例**必须通过**。\n3.  **优先修复代码**: 当出现测试失败时，首要任务是分析并**修复生产代码**中的问题。\n4.  **禁止随意修改测试**: **严禁**为了让测试通过而随意修改测试用例本身。只有在需求变更或测试用例设计本身存在缺陷时，才允许修改测试。\n\n### 5.5 测试用例命名规范\n- **命名格式**：使用 `should_[预期行为]_when_[条件]` 格式（BDD风格）\n- **语言要求**：必须使用**英文**描述测试场景\n- **示例**：\n  ```python\n  def should_return_access_token_when_valid_credentials_provided()\n  def should_deny_access_when_invalid_password_submitted()\n  def should_allow_access_to_protected_route_with_valid_token()\n  ```\n\n## 6. 工程实践与环境管理 (Engineering Practices & Environment Management)\n\n### 6.1 依赖管理 (Dependency Management)\n\n1.  **依赖检查**: 在生成任何需要新库的代码之前，必须首先检查 `requirements.txt` 文件，确认所需的功能库是否已经存在。\n2.  **依赖同步**: 如果需要添加新的依赖，在使用 `pip install <package>` 安装后，必须立即执行 `pip freeze > requirements.txt` 命令，将新依赖固化到声明文件中，以保持环境一致性。\n\n### 6.2 环境变量管理 (Environment Variable Management)\n\n1.  **优先复用**: 在添加任何配置或密钥之前，必须首先检查 `.env` 文件，优先复用已有的环境变量。\n2.  **命名规范**: 如需添加新的环境变量，必须确保其命名具有清晰的业务含义，并统一使用 `AI4SE_MCP_HUB_` 作为前缀。\n3.  **同步更新**: 添加新的环境变量后，应一并更新 `.env.example` 文件（如果存在），以方便其他开发者配置环境。\n\n### 6.3 验证流程 (Verification Workflow)\n\n1.  **测试提醒**: 任何代码（无论是新增还是修改）生成之后，都必须提醒用户运行相关的自动化测试套件，并将所有测试通过作为代码生成活动成功完成的标志。\n\n### 6.4 虚拟环境管理 (Virtual Environment Management)\n\n1.  **环境检查**: 在生成任何需要执行终端命令的指令之前，必须首先提醒用户，确保他们已经激活了项目的 `venv` 虚拟环境。\n2.  **激活指令**: 应当在必要时，提供标准的激活命令作为参考：\n    - **Windows**: `.\\.venv\\Scripts\\activate`\n    - **macOS/Linux**: `source .venv/bin/activate`\n3.  **安全执行**: 在不确定环境状态时，应优先考虑将激活命令与真实命令串联执行，以保证命令在正确的依赖环境中运行。\n\n## 6.5 数据库迁移规范 (Database Migration Practices)\n\n1. **禁止自动建表**: 禁止在应用启动时自动创建数据库表（例如使用 `Base.metadata.create_all`）。所有数据库结构的变更必须通过 Alembic 迁移脚本完成，以确保数据库版本可控和可追溯。\n\n## 6.6 数据库设计约束 (Database Design Constraints)\n\n### 6.6.1 主键设计约束\n\n1. **UUID 主键约束**: **强制要求**。所有实体的主键字段必须使用 UUID 类型，严禁使用 Integer 或其他类型作为主键。\n   - **领域模型**: 所有实体类的 `id` 字段必须声明为 `UUID` 类型\n   - **ORM 模型**: 使用 `sqlalchemy.dialects.postgresql.UUID(as_uuid=True)` 类型，并设置 `default=uuid.uuid4`\n   - **API Schema**: 所有涉及实体 ID 的 Pydantic 模型字段必须声明为 `UUID` 类型\n   - **路由参数**: FastAPI 路由中的 ID 参数必须声明为 `UUID` 类型\n   - **外键关系**: 所有外键字段也必须使用 UUID 类型以保持一致性\n\n### 6.6.2 字段命名约束\n\n2. **业务语义命名**: **强制要求**。数据库字段名必须只反映业务含义，严禁包含技术实现细节。\n   - **禁止技术后缀**: 严禁在字段名中包含技术类型信息，如 `id_uuid`, `name_str`, `count_int`, `is_active_bool`, `data_json` 等\n   - **业务含义优先**: 字段名应清晰表达业务概念，如 `user_id`, `created_at`, `email`, `status` 等\n   - **一致性要求**: 相同业务概念在不同表中应使用相同的字段名\n\n   **正确示例**:\n   ```sql\n   -- [正确] 只包含业务含义\n   CREATE TABLE users (\n       id UUID PRIMARY KEY,\n       username VARCHAR(50) NOT NULL,\n       email VARCHAR(100) NOT NULL,\n       created_at TIMESTAMP DEFAULT NOW()\n   );\n\n   CREATE TABLE oauth_accounts (\n       id UUID PRIMARY KEY,\n       user_id UUID REFERENCES users(id),\n       provider VARCHAR(20) NOT NULL,\n       provider_account_id VARCHAR(255) NOT NULL\n   );\n   ```\n\n   **错误示例**:\n   ```sql\n   -- [错误] 包含技术实现细节\n   CREATE TABLE users (\n       id_uuid UUID PRIMARY KEY,           -- 包含技术类型\n       username_str VARCHAR(50),           -- 包含技术类型\n       email_varchar VARCHAR(100),         -- 包含技术类型\n       is_active_bool BOOLEAN,             -- 包含技术类型\n       metadata_json JSONB                 -- 包含技术类型\n   );\n   ```\n\n### 6.6.3 表设计约束\n\n3. **表命名规范**:\n   - 使用复数形式的英文单词，如 `users`, `oauth_accounts`, `orders`\n   - 使用下划线分隔多个单词，如 `user_profiles`, `order_items`\n   - 避免缩写，使用完整的业务术语\n\n4. **索引设计**:\n   - 主键自动创建唯一索引\n   - 外键字段应创建索引以提高查询性能\n   - 经常用于查询条件的字段应考虑创建索引\n   - 唯一约束字段自动创建唯一索引\n\n### 6.6.4 实现示例\n\n**完整的实体设计示例**:\n```python\n# 领域模型\nclass User(BaseModel):\n    id: UUID\n    username: str\n    email: EmailStr\n    status: UserStatus\n    created_at: datetime\n\n# ORM 模型\nclass UserDB(Base):\n    __tablename__ = \"users\"\n\n    id: ColumnType[UUID] = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)\n    username: ColumnType[str] = Column(String(50), unique=True, nullable=False)\n    email: ColumnType[str] = Column(String(100), unique=True, nullable=False)\n    status: ColumnType[UserStatus] = Column(Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False)\n    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())\n\n# API Schema\nclass UserPublic(BaseModel):\n    id: UUID\n    username: str\n    email: EmailStr\n    status: UserStatus\n\n# API 路由\<EMAIL>(\"/users/{user_id}\")\nasync def get_user(user_id: UUID = Path(..., description=\"User unique identifier\")):\n    pass\n```\n\n## 7. 模块内代码组织规范 (Intra-Module Code Organization)\n\n### 7.1 业务子域分离原则\n\n当单个模块内包含多个相关但职责不同的业务子域时，**强制要求**通过文件命名前缀来区分不同的业务子域，而不是创建深层嵌套的目录结构。\n\n### 7.2 文件命名规范\n\n#### 7.2.0 通用命名约束\n\n**强制要求**: 所有模块内的文件必须使用业务子域前缀命名，严禁使用通用文件名。这确保了：\n1. **可读性**: 文件名直接反映其业务职责\n2. **可维护性**: 避免在大型项目中出现同名文件混淆\n3. **一致性**: 所有模块遵循统一的命名标准\n\n**禁止的通用命名**:\n- [错误] `models.py`, `repositories.py`, `services.py`, `schemas.py`, `orm.py`\n- [错误] 这些通用名称无法体现具体的业务含义\n\n**正确的业务化命名**:\n- [正确] `user_models.py`, `user_repositories.py`, `user_service.py`, `user_schemas.py`, `user_orm.py`\n- [正确] `credential_auth_models.py`, `oauth_provider_service.py`\n\n#### 7.2.1 服务层命名规范\n\n- **格式**: `{业务子域}_{层级类型}.py`\n- **业务化命名**: 避免使用过于通用的词汇（如 `auth`, `service`），应使用具体的业务术语\n- **一致性要求**: 同一业务子域的所有文件必须使用相同的前缀\n\n**示例**:\n```\napplication/\n├── credential_auth_service.py         # 用户名密码认证服务\n├── oauth_authentication_service.py   # OAuth认证服务\n└── oauth_provider_service.py         # OAuth提供方管理服务\n```\n\n#### 7.2.2 领域层命名规范\n\n- **模型文件**: `{业务子域}_models.py`\n- **仓库接口**: `{业务子域}_repositories.py`\n\n**示例**:\n```\ndomain/\n├── credential_auth_models.py          # 用户凭证认证模型\n├── credential_auth_repositories.py    # 用户凭证认证仓库接口\n├── oauth_account_models.py           # OAuth账户模型\n├── oauth_account_repositories.py     # OAuth账户仓库接口\n├── oauth_provider_models.py          # OAuth提供方模型\n└── oauth_provider_repositories.py    # OAuth提供方仓库接口\n```\n\n#### 7.2.3 基础设施层命名规范\n\n- **ORM 模型**: `{业务子域}_orm.py`\n- **仓库实现**: `{业务子域}_repositories.py`\n\n**示例**:\n```\ninfrastructure/\n├── credential_auth_orm.py             # 用户凭证认证ORM\n├── credential_auth_repositories.py   # 用户凭证认证仓库实现\n├── oauth_account_orm.py              # OAuth账户ORM\n├── oauth_account_repositories.py     # OAuth账户仓库实现\n├── oauth_provider_orm.py             # OAuth提供方ORM\n└── oauth_provider_repositories.py    # OAuth提供方仓库实现\n```\n\n#### 7.2.4 接口层命名规范\n\n- **API 路由**: `{业务子域}_api.py`\n- **Schema 定义**: `{业务子域}_schemas.py`\n\n**示例**:\n```\ninterfaces/\n├── credential_auth_api.py             # 用户名密码认证API\n├── credential_auth_schemas.py         # 用户名密码认证Schema\n├── oauth_authentication_api.py       # OAuth认证API\n├── oauth_authentication_schemas.py   # OAuth认证Schema\n├── oauth_provider_api.py             # OAuth提供方管理API\n├── oauth_provider_schemas.py         # OAuth提供方Schema\n└── dependencies.py                    # 统一的依赖注入\n```\n\n### 7.3 业务子域识别原则\n\n#### 7.3.1 职责分离标准\n\n不同的业务子域必须满足以下条件之一：\n1. **数据模型不同**: 操作不同的实体或聚合根\n2. **业务流程不同**: 解决不同的业务问题或用例\n3. **外部依赖不同**: 依赖不同的外部系统或服务\n4. **生命周期不同**: 具有不同的变更频率或演进路径\n\n#### 7.3.2 常见业务子域示例\n\n**认证模块 (auth) 的业务子域划分**:\n- `credential_auth`: 用户名密码认证\n- `oauth_authentication`: OAuth第三方认证\n- `oauth_provider`: OAuth提供方配置管理\n\n**订单模块 (orders) 的业务子域划分**:\n- `order_management`: 订单创建和管理\n- `order_payment`: 订单支付处理\n- `order_fulfillment`: 订单履行和配送\n\n### 7.4 禁止的组织方式\n\n#### 7.4.1 禁止使用类似 `_management` 功能性命名的后缀\n\n**错误示例**:\n```\n[错误] oauth_provider_management_service.py\n[错误] user_management_api.py\n[错误] order_management_models.py\n```\n\n**正确示例**:\n```\n[正确] oauth_provider_service.py\n[正确] user_admin_api.py\n[正确] order_models.py\n```\n\n#### 7.4.2 禁止过度嵌套目录\n\n**错误示例**:\n```\n[错误] modules/auth/authentication/credential/application/\n[错误] modules/auth/provider_management/oauth/domain/\n```\n\n**正确示例**:\n```\n[正确] modules/auth/application/credential_auth_service.py\n[正确] modules/auth/domain/oauth_provider_models.py\n```\n\n### 7.5 跨业务子域依赖处理\n\n#### 7.5.1 依赖方向约束\n\n- **同模块内**: 业务子域之间可以相互依赖，但必须通过应用层服务接口\n- **跨模块**: 必须通过目标模块的应用层服务接口，严禁直接依赖其他模块的业务子域\n\n#### 7.5.2 共享组件处理\n\n当多个业务子域需要共享代码时：\n1. **模块内共享**: 创建 `shared/` 目录存放共享组件\n2. **跨模块共享**: 放置在根目录的 `common/` 或 `shared/` 目录\n\n**示例**:\n```\nmodules/auth/\n├── application/\n├── domain/\n├── infrastructure/\n├── interfaces/\n└── shared/                    # 模块内共享组件\n    └── oauth/\n        └── client_service.py  # OAuth客户端工具服务\n```\n\n## 8. 重构约束原则\n\n1. **创建新文件优先**: 任何重构操作前，应先创建新文件并完成修改，最后再删除不必要的文件。\n2. **上下文分析**: 重构前需分析被重构文件相关联的代码和测试，掌握完整上下文避免错误。\n3. **测试保障**: 重构后必须运行自动化测试确保功能正常。对于没有测试覆盖的部分，应询问用户是否补充测试用例。\n\n## 9. Git 提交规范 (Commit Message Guidelines)\n\n为了保持版本历史的清晰、可读，并能够基于提交信息自动生成变更日志（Changelog），所有 Git 提交都必须遵循**约定式提交（Conventional Commits）**规范。\n\n### 9.1 格式\n\n每个 commit message 都由一个 **header**, 一个可选的 **body**, 和一个可选的 **footer** 组成。\n\n```\n<type>(<scope>): <subject>\n<BLANK LINE>\n<body>\n<BLANK LINE>\n<footer>\n```\n\n---\n\n### 9.2 Header (必需)\n\nHeader 部分只有一行，包含三个字段：`type`, `scope`, 和 `subject`。\n\n#### `type` (必需)\n\n`type` 必须是以下关键字之一：\n\n*   **feat**: 新功能（feature）\n*   **fix**: 修复 bug\n*   **docs**: 仅文档更改\n*   **style**: 不影响代码含义的更改（空格、格式、缺少分号等）\n*   **refactor**: 代码重构，既不修复 bug 也不添加功能\n*   **perf**: 提升性能的代码更改\n*   **test**: 添加或修改测试\n*   **build**: 影响构建系统或外部依赖项的更改（例如：`requirements.txt`, `Dockerfile`）\n*   **ci**: 对 CI 配置文件和脚本的更改（例如：GitHub Actions）\n*   **chore**: 其他不修改 `src` 或 `test` 文件的更改（例如：更新 `.gitignore`）\n*   **revert**: 撤销之前的 commit\n\n#### `scope` (可选)\n\n`scope` 用于指定本次 commit 影响的范围。在本项目中，`scope` 应该是受影响的**业务模块名**。\n\n*   **示例**: `auth`, `orders`, `common`, `tests`\n\n#### `subject` (必需)\n\n`subject` 是对 commit 目的的简短描述，不超过 50 个字符。\n\n*   使用祈使句，现在时态：\"change\" not \"changed\" nor \"changes\"。\n*   第一个字母不要大写。\n*   末尾不加句号（`.`）。\n*   **语言要求**: 必须使用**英文**。\n\n---\n\n### 9.3 Body (可选)\n\n`body` 部分是对本次 commit 的详细描述，可以分成多行。\n\n*   与 `subject` 一样，使用祈使句，现在时态。\n*   应该包括改变的动机，以及和之前行为的对比。\n*   **语言要求**: 必须使用**英文**。\n\n---\n\n### 9.4 Footer (可选)\n\n`footer` 部分只用于两种情况：\n\n1.  **重大更改 (Breaking Change)**:\n    *   以 `BREAKING CHANGE:` 开头，后面是对变动的描述、理由和迁移方法。\n\n2.  **关闭 Issue**:\n    *   例如: `Closes #123`, `Fixes #456`\n\n---\n\n### 9.5 示例\n\n**修复 `auth` 模块中的密码验证 bug:**\n```\nfix(auth): correct password verification logic\n\nThe previous implementation was using an incorrect salt, causing all password checks to fail. This commit corrects the salt retrieval process.\n\nCloses #234\n```\n\n**为 `orders` 模块添加新功能，并包含重大更改:**\n```\nfeat(orders): allow multiple items per order\n\nUsers can now add more than one item to their shopping cart and create an order with all of them.\n\nBREAKING CHANGE: The `create_order` service method signature has changed from `(item_id: int)` to `(item_ids: List[int])`. All clients must be updated.\n```\n\n---\n\n### 9.6 提交策略：原子化与分批提交\n\n为了保持版本历史的清晰和可追溯性，强烈建议遵循**原子化提交**的原则。当一次开发任务包含多种性质的变更（例如，既重构了代码，又添加了新功能，还更新了测试），应当将这些变更**分批提交**。\n\n- **核心原则**: 每个提交应只关注一个独立的、逻辑相关的变更。\n- **目的**:\n    - 使代码审查（Code Review）更轻松、更聚焦。\n    - 简化问题追溯和变更回滚 (`git revert`)。\n    - 保持每个提交信息 (`feat`, `fix`, `docs` 等) 的纯粹性。\n\n- **推荐的分批提交示例**:\n  在一次大型重构后，提交序列可能如下：\n  1. `git commit -m \"docs: update development guidelines\"`\n  2. `git commit -m \"refactor(auth): migrate authentication logic to modular structure\"`\n  3. `git commit -m \"test(auth): refactor test cases for new architecture\"`\n  4. `git commit -m \"build(deps): add email-validator dependency\"`\n\n\n\n请分析这些规则的内容，理解其核心要求，然后重新组织生成一份高质量、标准化的规则文档。确保：\n\n1. 消除重复和冗余内容\n2. 解决可能存在的矛盾\n3. 补充缺失的重要信息\n4. 提高规则的可操作性\n5. 保持逻辑结构清晰\n\n请严格按照系统提示词中的格式要求输出处理后的规则。\n"}]}
{"timestamp": "2025-06-25T20:31:07.638661", "agent_name": "rules_processor", "interaction_id": 2, "task_description": "规则处理", "interaction_type": "response", "content": "# 处理后的开发规则\n\n## 1. 规则概述\n- **规则来源**: rules.md\n- **处理时间**: 2023年11月15日\n- **规则范围**: FastAPI和DDD架构的Python项目开发\n- **主要改进**: \n  - 重新组织了规则结构，使其更加系统化\n  - 消除了重复内容，特别是关于架构分层和模块组织的部分\n  - 补充了测试规范和工程实践的细节\n  - 增强了规则的可操作性和一致性\n\n## 2. 核心原则\n- **技术栈原则**:\n  - 使用FastAPI作为Web框架\n  - 采用Pydantic进行数据验证\n  - 使用SQLAlchemy和Alembic进行数据库操作和迁移\n- **开发方法论**:\n  - 严格遵循领域驱动设计(DDD)原则\n  - 采用分层架构，明确各层职责\n- **质量标准**:\n  - 强制类型提示(Type Hinting)\n  - 严格遵循PEP 8代码风格\n  - 所有文档和注释使用英文\n\n## 3. 技术规范\n- **语言要求**:\n  - Python 3.8+\n  - 强制类型提示\n- **框架规范**:\n  - FastAPI路由定义规范\n  - SQLAlchemy ORM使用规范\n- **工具链**:\n  - Pytest测试框架\n  - Alembic数据库迁移工具\n  - Git版本控制\n\n## 4. 架构约束\n### 4.1 项目结构\n```\n.\n├── modules/          # 业务模块\n│   ├── auth/         # 认证模块\n│   └── orders/       # 订单模块\n├── common/           # 通用代码\n├── tests/            # 测试代码\n└── main.py           # 应用入口\n```\n\n### 4.2 分层架构\n1. **接口层(Interfaces)**:\n   - 处理HTTP请求和响应\n   - 包含FastAPI路由和Pydantic模型\n2. **应用层(Application)**:\n   - 编排业务用例\n   - 定义服务类和DTO\n3. **领域层(Domain)**:\n   - 核心业务逻辑\n   - 包含实体、值对象和仓库接口\n4. **基础设施层(Infrastructure)**:\n   - 技术实现细节\n   - 包含仓库实现和ORM模型\n\n### 4.3 依赖规则\n1. 严格单向依赖: Interfaces → Application → Domain\n2. 领域层禁止依赖外部框架\n3. 模块间通信必须通过应用层服务\n\n## 5. 代码质量标准\n### 5.1 代码风格\n- 严格遵循PEP 8规范\n- 所有函数和变量必须有类型提示\n- 文档字符串使用英文\n\n### 5.2 测试要求\n- **测试覆盖率**:\n  - 核心业务逻辑100%覆盖\n  - 关键路径90%以上覆盖\n- **测试分层**:\n  - 单元测试: 测试独立单元\n  - 集成测试: 测试模块集成\n  - E2E测试: 测试完整流程\n\n### 5.3 文档标准\n- 所有API必须有OpenAPI文档\n- 复杂逻辑必须有详细注释\n- 重要设计决策必须有文档记录\n\n## 6. 工程实践\n### 6.1 开发流程\n1. 领域驱动设计优先\n2. 测试驱动开发(TDD)\n3. 持续集成\n\n### 6.2 版本控制\n- 使用Git进行版本管理\n- 遵循约定式提交规范\n- 分支策略:\n  - main: 生产环境\n  - develop: 集成分支\n  - feature/*: 功能开发\n\n### 6.3 环境管理\n- 使用虚拟环境隔离依赖\n- 环境变量统一管理\n- 依赖版本固化\n\n## 7. 最佳实践\n### 7.1 开发技巧\n- 小步提交，频繁集成\n- 代码审查前自检\n- 及时更新文档\n\n### 7.2 问题解决\n- 优先修复生产代码\n- 禁止为通过测试而修改测试\n- 复杂问题先写测试再修复\n\n### 7.3 性能优化\n- 数据库查询优化\n- 缓存策略\n- 异步处理\n\n## 8. 约束与限制\n### 8.1 严格禁止\n- 领域层引入框架依赖\n- 直接跨模块访问领域层\n- 使用通用文件名(如models.py)\n\n### 8.2 必须遵守\n- UUID作为主键类型\n- 业务语义命名规范\n- 原子化提交原则\n\n### 8.3 异常处理\n- 统一错误处理机制\n- 业务异常与系统异常分离\n- 详细的错误日志\n\n## 质量要求\n1. **完整性**: 覆盖从架构设计到代码提交的全流程\n2. **一致性**: 各层规范相互协调，无冲突\n3. **可操作性**: 每条规则都有明确的执行标准\n4. **可维护性**: 模块化组织，便于更新\n5. **专业性**: 使用准确的DDD和Python技术术语"}
