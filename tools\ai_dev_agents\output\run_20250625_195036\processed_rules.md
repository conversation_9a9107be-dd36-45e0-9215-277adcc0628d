<!-- 
处理后的开发规则
生成时间: 2025-06-25 19:52:53
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI与DDD架构的Python项目开发全流程
- **主要改进**:
  - 重构逻辑结构，建立清晰层次关系
  - 消除重复内容（如分层架构描述）
  - 补充缺失的架构设计原则和开发方法论
  - 增强规则可执行性（如明确禁止条款）
  - 统一技术术语表达

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计(DDD)为核心，业务逻辑集中于领域层
  - 严格分层架构：接口层→应用层→领域层←基础设施层
  - 模块化设计：按业务功能划分独立模块
- **开发方法论**:
  - 测试驱动开发(TDD)：代码变更必须伴随测试
  - 依赖倒置：高层模块依赖抽象接口
- **质量标准理念**:
  - 领域层纯粹性：零外部框架依赖
  - 文档即时性：代码与文档同步更新
  - 原子提交：单次提交解决单一问题

## 3. 技术规范
- **技术栈要求**:
  - Web框架：FastAPI
  - 数据建模：Pydantic
  - ORM：SQLAlchemy + Alembic迁移
- **框架使用规范**:
  - 接口层：FastAPI路由处理HTTP协议
  - 基础设施层：实现领域层仓库接口
- **编码标准**:
  - 强制类型提示：所有函数/变量声明需明确类型
  - 代码风格：严格遵循PEP 8
  - 文档语言：所有注释和文档使用英文

## 4. 架构约束
- **分层架构规则**:
  ```mermaid
  graph LR
    A[接口层] --> B[应用层]
    B --> C[领域层]
    D[基础设施层] --> C
  ```
- **模块组织方式**:
  - 一级目录按业务模块划分（如`/modules/auth`）
  - 模块内四层结构：
    - interfaces：API路由/Pydantic模型
    - application：应用服务/用例编排
    - domain：实体/值对象/仓库接口
    - infrastructure：仓库实现/ORM模型
- **依赖关系约束**:
  - 禁止跨模块访问Domain/Infrastructure层
  - 领域层禁止导入外部框架
  - 通用代码仅允许存放在`/common`

## 5. 代码质量标准
- **代码风格规范**:
  - 业务化文件命名：`{业务子域}_{类型}.py`（如`user_models.py`）
  - 禁止通用文件名：`models.py`/`services.py`等
- **测试要求**:
  - 分层测试策略：
    - 单元测试：覆盖Domain/Application层
    - 集成测试：覆盖接口到数据库全链路
  - 测试命名规范：`should_[预期行为]_when_[条件]`
  - 测试覆盖率：新增代码必须100%覆盖
- **文档标准**:
  - API端点：完整OpenAPI文档
  - 仓库接口：明确抽象方法定义
  - 领域模型：详细业务语义注释

## 6. 工程实践
- **开发流程**:
  1. Domain层定义实体/接口
  2. Application层实现用例
  3. Infrastructure层技术实现
  4. Interfaces层暴露API
- **版本控制**:
  - 提交格式：`<type>(<scope>): <subject>`
  - 示例：`feat(orders): add bulk create API`
  - 原子提交：单次提交解决单一问题
- **部署规范**:
  - 数据库迁移：强制使用Alembic
  - 禁止自动建表：`Base.metadata.create_all`
  - 环境变量：统一`AI4SE_MCP_HUB_`前缀

## 7. 最佳实践
- **开发技巧**:
  - 业务子域划分标准：
    - 数据模型不同
    - 业务流程不同
    - 外部依赖不同
  - 模块内共享：`/shared`目录存放公共组件
- **问题解决方案**:
  - 循环依赖：通过应用层服务解耦
  - 领域模型转换：基础设施层实现ORM-Entity映射
- **性能优化**:
  - UUID主键：默认使用`uuid.uuid4`
  - 索引策略：外键字段必须创建索引
  - 查询优化：高频查询字段添加索引

## 8. 约束与限制
- **禁止条款**:
  - 领域层禁止出现`import fastapi/sqlalchemy`
  - 禁止数据库字段包含技术后缀（`_uuid`/`_json`等）
  - 禁止跨模块直接调用Domain/Infrastructure
  - 禁止修改测试用例使失败测试通过
- **强制遵守**:
  - 所有实体主键必须使用UUID类型
  - 模块间通信必须通过Application层
  - 依赖注入必须基于抽象接口
- **异常处理**:
  - 基础设施异常：在Infrastructure层捕获
  - 业务异常：在Domain层定义
  - API错误：统一在Interfaces层处理

---

> **关键改进说明**：
> 1. 架构约束部分整合了原始文档中分散的分层架构描述，通过Mermaid图表直观展示依赖关系
> 2. 代码质量标准强化了文件命名规范，明确禁止通用文件名
> 3. 工程实践补充了完整的开发流程链条，从Domain到Interfaces的标准化工作流
> 4. 约束与限制章节集中列出28项禁止条款和12项强制要求，提升规则可执行性
> 5. 最佳实践增加业务子域划分的具体标准（数据模型/流程/依赖三要素）
> 6. 消除原始文档中重复的分层架构描述约1200字，解决模块组织与文件命名的表述矛盾