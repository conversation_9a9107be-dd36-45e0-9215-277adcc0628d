# Domain-Driven Design Architecture Rules

## Core Principles

### 1. Layered Architecture
- **Interfaces Layer**: FastAPI routers, Pydantic schemas
- **Application Layer**: Application services, use cases
- **Domain Layer**: Entities, value objects, domain services
- **Infrastructure Layer**: Repository implementations, ORM models

### 2. Dependency Rules
- Dependencies flow inward: Interfaces → Application → Domain
- Infrastructure implements Domain interfaces
- Domain layer must be pure (no external framework imports)

### 3. Module Organization
- Organize by business modules first, then by layers
- Each module contains all four layers
- Common code in shared directories

## Entity Design

### Primary Keys
- **MANDATORY**: All entities must use UUID as primary key
- Field name should be `id` (not `id_uuid` or similar)
- Database field names must reflect business meaning only

### Naming Conventions
- Use business-meaningful names
- Avoid technical implementation details in names
- Repository implementations: `[InterfaceName]Impl`

## File Structure

```
modules/
├── {module_name}/
│   ├── interfaces/
│   │   ├── {module_name}_api.py
│   │   └── schemas.py
│   ├── application/
│   │   ├── services.py
│   │   └── dtos.py
│   ├── domain/
│   │   ├── models.py
│   │   └── repositories.py
│   └── infrastructure/
│       ├── repositories.py
│       └── orm.py
```

## Cross-Module Communication
- Modules communicate through Application layer services only
- No direct access to other modules' Domain or Infrastructure layers
- Use dependency injection for service communication
