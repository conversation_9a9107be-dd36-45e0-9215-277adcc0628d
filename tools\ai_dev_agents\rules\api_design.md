# API Design Rules

## FastAPI Standards

### Router Organization
- **MANDATORY**: All routers must have prefixes
- Group related endpoints in single router files
- Use business module names for router prefixes

### Documentation
- **MANDATORY**: Comprehensive OpenAPI documentation for all endpoints
- Include request/response examples
- Document all possible error responses
- Use meaningful operation IDs

### Request/Response Design
- Use Pydantic models for all request/response bodies
- Validate all input data
- Return consistent error response format
- Use appropriate HTTP status codes

## Authentication & Authorization

### OAuth Integration
- Support multiple OAuth providers dynamically
- Store provider configurations in database
- Use flexible redirect URI patterns
- Return appropriate response format (JSON for API, HTML for web)

### Security
- Validate all authentication tokens
- Implement proper CORS policies
- Use HTTPS in production
- Rate limiting for public endpoints

## Data Validation

### Input Validation
- Validate all request parameters
- Use Pydantic validators for complex validation
- Provide clear validation error messages
- Sanitize user input

### Output Formatting
- Consistent response structure
- Include metadata when appropriate
- Handle pagination properly
- Support multiple response formats when needed

## Error Handling

### Error Responses
- Use standard HTTP status codes
- Provide detailed error messages
- Include error codes for programmatic handling
- Log errors for debugging
