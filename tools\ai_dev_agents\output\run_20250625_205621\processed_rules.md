<!-- 
处理后的开发规则
生成时间: 2025-06-25 20:56:47
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发
- **主要改进**: 
  - 重构了规则组织结构，使其更具逻辑性
  - 消除了重复内容，特别是关于架构分层和命名规范的部分
  - 补充了测试规范和工程实践细节
  - 增强了规则的可操作性和一致性

## 2. 核心原则
- **技术栈选择**:
  - Web框架: FastAPI
  - 数据验证: Pydantic
  - ORM: SQLAlchemy + Alembic
- **开发方法论**:
  - 严格遵循领域驱动设计(DDD)原则
  - 强调分层架构和职责分离
- **质量标准**:
  - 强制类型提示(Type Hinting)
  - 严格遵循PEP 8代码风格
  - 所有文档使用英文编写

## 3. 技术规范
- **代码风格**:
  - 所有Python代码必须符合PEP 8规范
  - 强制使用类型提示
- **框架使用**:
  - FastAPI路由定义规范
  - SQLAlchemy模型定义标准
  - Pydantic模型验证规则

## 4. 架构约束
- **分层架构**:
  - 接口层(Interfaces): 处理HTTP请求/响应
  - 应用层(Application): 业务用例编排
  - 领域层(Domain): 核心业务逻辑
  - 基础设施层(Infrastructure): 技术实现
- **依赖规则**:
  - 严格单向依赖: Interfaces→Application→Domain
  - 领域层禁止依赖外部框架
  - 模块间通信必须通过应用层接口

## 5. 代码质量标准
- **代码组织**:
  - 按业务模块组织代码
  - 禁止使用通用文件名，必须使用业务前缀
- **测试要求**:
  - 测试目录结构与业务模块对应
  - 必须包含单元测试和集成测试
  - 测试用例命名采用BDD风格
- **文档标准**:
  - 所有API端点必须有完整OpenAPI文档
  - 代码变更必须同步更新文档

## 6. 工程实践
- **开发流程**:
  - 领域驱动开发: 从领域层开始实现
  - 测试驱动开发: 代码变更必须伴随测试
- **版本控制**:
  - 遵循约定式提交规范
  - 提倡原子化提交
- **环境管理**:
  - 使用虚拟环境隔离依赖
  - 环境变量统一命名规范
- **数据库管理**:
  - 使用Alembic进行迁移
  - 禁止自动建表

## 7. 最佳实践
- **模块设计**:
  - 按业务功能划分模块
  - 模块内按业务子域组织代码
- **数据库设计**:
  - 强制使用UUID主键
  - 字段命名反映业务语义
- **测试策略**:
  - 领域层测试无外部依赖
  - 应用层测试使用Mock
  - 接口层测试验证端到端流程

## 8. 约束与限制
- **严格禁止**:
  - 领域层引入框架依赖
  - 直接跨模块访问领域层
  - 修改测试用例使其通过而非修复代码
- **必须遵守**:
  - 所有实体使用UUID主键
  - 数据库字段命名不包含技术细节
  - 提交信息遵循约定式提交规范
- **异常处理**:
  - 接口层处理HTTP相关异常
  - 应用层处理业务逻辑异常
  - 领域层保持纯粹不处理异常

## 质量要求
- **完整性**: 覆盖从架构设计到代码提交的全流程
- **一致性**: 各层职责明确，无交叉依赖
- **可操作性**: 提供具体实施步骤和示例
- **可维护性**: 模块化设计便于扩展
- **专业性**: 使用准确的DDD和Python技术术语

## 补充说明
1. 新增了数据库设计约束章节，明确UUID主键和字段命名规范
2. 强化了测试规范，包括测试目录结构和分层测试策略
3. 细化了工程实践，特别是环境管理和依赖管理
4. 完善了提交规范，强调原子化提交原则
5. 优化了文件命名规范，避免通用名称带来的混淆