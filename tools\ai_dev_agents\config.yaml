# AI Development Agents Configuration
# 配置文件仅包含必要的LLM配置和模型预设

# LLM 配置
llm:
  # 提供商类型: openrouter, openai, anthropic
  provider: "openrouter"

  # OpenRouter 配置
  openrouter:
    api_key: "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"
    base_url: "https://openrouter.ai/api/v1"
    timeout: 60

# 模型预设配置
model_presets:
  # 默认选择（DeepSeek V3 免费版）
  default:
    provider: "openrouter"
    model: "deepseek/deepseek-chat-v3-0324:free"
    temperature: 0.1
    max_tokens: 32000
    description: "DeepSeek V3 免费版"

  # 高质量分析
  high_quality:
    provider: "openrouter"
    model: "google/gemini-2.5-flash-preview-05-20"
    temperature: 0.05
    max_tokens: 66000
    description: "Google: Gemini 2.5 Flash Preview 05-20"

  # 创意模式
  creative:
    provider: "openrouter"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.3
    max_tokens: 4000
    description: "DeepSeek R1 创意模式，更高温度"
