<!-- 
处理后的开发规则
生成时间: 2025-06-26 11:02:16
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发规范
- **主要改进**: 
  - 重组了内容结构，使其更加系统化
  - 消除了重复内容，如架构分层原则
  - 补充了测试规范和工程实践的细节
  - 增强了规则的可操作性

## 2. 核心原则
- **领域驱动设计(DDD)**: 所有开发活动围绕领域模型展开
- **分层架构**: 严格遵循接口层→应用层→领域层→基础设施层的单向依赖
- **模块化设计**: 以业务功能为第一组织层级
- **质量优先**: 强制类型提示、PEP8规范和全面测试覆盖

## 3. 技术规范
### 3.1 技术栈要求
- Web框架: FastAPI
- 数据建模: Pydantic
- ORM: SQLAlchemy + Alembic
- 测试框架: Pytest

### 3.2 编码标准
- 强制类型提示(Type Hinting)
- 严格遵循PEP8规范
- 所有文档使用英文编写
- 禁止领域层引入框架依赖

## 4. 架构约束
### 4.1 项目结构
```
.
├── modules/          # 业务模块
│   ├── {module}/     # 每个业务模块
│   │   ├── application/
│   │   ├── domain/
│   │   ├── infrastructure/
│   │   └── interfaces/
├── common/           # 通用代码
└── tests/            # 测试代码
```

### 4.2 分层架构原则
1. **接口层(Interfaces)**: 处理HTTP请求/响应
2. **应用层(Application)**: 编排业务用例
3. **领域层(Domain)**: 核心业务逻辑
4. **基础设施层(Infrastructure)**: 技术实现

### 4.3 依赖规则
- 严格单向依赖: Interfaces→Application→Domain
- 领域层保持纯粹，不依赖外部框架
- 模块间通信必须通过应用层接口

## 5. 代码质量标准
### 5.1 代码风格
- 遵循PEP8规范
- 使用业务子域前缀命名文件
- 禁止通用文件名(models.py等)

### 5.2 测试要求
- 测试目录结构与业务模块对应
- 必须包含单元测试和集成测试
- 测试命名使用BDD风格
- 测试覆盖率100%

### 5.3 文档标准
- 所有API端点必须有OpenAPI文档
- 代码变更必须同步更新文档
- 使用英文编写所有文档

## 6. 工程实践
### 6.1 开发流程
1. Domain First: 先设计领域模型
2. 实现应用服务
3. 完成基础设施实现
4. 最后开发接口层

### 6.2 版本控制
- 遵循约定式提交规范
- 提交信息必须包含类型和范围
- 提倡原子化提交

### 6.3 环境管理
- 使用虚拟环境隔离依赖
- 环境变量统一前缀
- 依赖变更及时更新requirements.txt

## 7. 最佳实践
### 7.1 数据库设计
- 主键强制使用UUID类型
- 字段命名反映业务含义
- 禁止自动建表，使用Alembic迁移

### 7.2 模块组织
- 按业务子域划分代码
- 禁止过度嵌套目录
- 共享代码放入shared目录

### 7.3 重构原则
- 先创建新文件再删除旧文件
- 重构前分析完整上下文
- 重构后必须运行测试

## 8. 约束与限制
### 8.1 严格禁止
- 领域层引入框架依赖
- 模块间直接访问领域层
- 修改测试用例使测试通过
- 使用通用文件名

### 8.2 必须遵守
- 类型提示全覆盖
- 测试伴随开发
- 文档与代码同步
- 单向依赖原则

### 8.3 异常处理
- 接口层处理HTTP错误
- 应用层定义事务边界
- 领域层抛出业务异常

## 质量要求
- **完整性**: 覆盖从架构设计到代码提交的全流程
- **一致性**: 各层规范相互协调，无矛盾
- **可操作性**: 提供具体示例和实施步骤
- **可维护性**: 模块化组织，便于扩展
- **专业性**: 使用准确的DDD和Python技术术语