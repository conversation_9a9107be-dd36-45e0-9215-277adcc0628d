# Database Design Rules

## Schema Design

### Primary Keys
- **MANDATORY**: All tables must use UUID primary keys
- Field name should be `id` (not `id_uuid`)
- Use `uuid.uuid4()` for generating UUIDs

### Field Naming
- Use business-meaningful names only
- No technical implementation details in field names
- Use snake_case for database fields
- Avoid abbreviations unless widely understood

### Relationships
- Use foreign keys with proper constraints
- Define cascade behaviors explicitly
- Index foreign key columns
- Document relationship semantics

## Migration Management

### Alembic Usage
- **MANDATORY**: All schema changes through Alembic migrations
- **FORBIDDEN**: Auto-creation of tables (no `create_all()`)
- Descriptive migration messages
- Test migrations in both directions

### Migration Best Practices
- One logical change per migration
- Include data migrations when needed
- Test with production-like data volumes
- Backup before major migrations

## Performance

### Indexing
- Index all foreign keys
- Index frequently queried columns
- Composite indexes for multi-column queries
- Monitor index usage and effectiveness

### Query Optimization
- Use SQLAlchemy efficiently
- Avoid N+1 query problems
- Use eager loading appropriately
- Monitor query performance

## Data Integrity

### Constraints
- Use database constraints for data integrity
- Validate data at application level too
- Handle constraint violations gracefully
- Document business rules as constraints
