#!/usr/bin/env python3
"""
Script to fix logging method calls in agent files.
"""

import re
from pathlib import Path

def fix_logging_in_file(file_path: Path):
    """Fix logging method calls in a single file."""
    if not file_path.exists():
        print(f"File not found: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace log_info, log_error, log_warning with logger equivalents
        content = re.sub(r'self\.log_info\(', 'self.logger.info(', content)
        content = re.sub(r'self\.log_error\(', 'self.logger.error(', content)
        content = re.sub(r'self\.log_warning\(', 'self.logger.warning(', content)
        content = re.sub(r'self\.log_debug\(', 'self.logger.debug(', content)

        # Fix AgentResult error parameter
        content = re.sub(r'error=str\(e\)', 'errors=[str(e)]', content)
        content = re.sub(r'error=([^,\)]+)', r'errors=[\1]', content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Fixed logging in: {file_path}")
        
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")

def main():
    """Fix logging in all agent files."""
    agent_files = [
        "tools/ai_dev_agents/agents/improved_requirements_analyzer.py",
        "tools/ai_dev_agents/agents/technical_leader.py",
        "tools/ai_dev_agents/agents/result_generator.py",
        "tools/ai_dev_agents/agents/domain_modeler.py"
    ]
    
    for file_path in agent_files:
        fix_logging_in_file(Path(file_path))
    
    print("Logging fixes completed!")

if __name__ == "__main__":
    main()
