# Code Quality Rules

## Python Standards

### Code Style
- **<PERSON><PERSON><PERSON>ORY**: Follow PEP 8 strictly
- **MANDATORY**: All functions and variables must have type hints
- **MANDATORY**: All documentation must be in English

### Documentation
- All classes and functions must have docstrings
- Use Google-style docstrings
- Include parameter types and return types
- Provide usage examples for complex functions

### Error Handling
- Use specific exception types
- Provide meaningful error messages
- Log errors appropriately
- Handle edge cases explicitly

## Testing Requirements

### Coverage
- **MANDATORY**: Minimum 80% test coverage
- Unit tests for all business logic
- Integration tests for API endpoints
- Mock external dependencies in unit tests

### Test Structure
- Tests organized by module structure
- Use descriptive test names: `should_[expected_behavior]_when_[condition]`
- Each test manages its own test data
- Clean up test data after execution

### Test Types
- **Unit Tests**: Domain models, application services
- **Integration Tests**: API endpoints, database operations
- **Contract Tests**: Inter-module communication

## Dependencies

### Management
- Use `uv add` for adding dependencies
- Never manually edit pyproject.toml for dependencies
- Keep dependencies minimal and justified
- Document why each dependency is needed

### Security
- Regular dependency updates
- Scan for security vulnerabilities
- Use pinned versions in production
