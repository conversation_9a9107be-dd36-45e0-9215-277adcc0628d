<!-- 
处理后的开发规则
生成时间: 2025-06-25 17:50:53
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年10月15日
- **规则范围**: 基于FastAPI和领域驱动设计（DDD）的Python项目开发
- **主要改进**:
  - 重组内容结构，建立清晰的规则层次体系
  - 消除重复内容（如分层架构在多个章节重复描述）
  - 补充缺失的架构设计原则和开发方法论
  - 增强规则可操作性（如明确禁止性条款）
  - 统一技术术语表达（如领域层、基础设施层等）
  - 优化测试规范的组织逻辑

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计（DDD）为核心：所有开发围绕领域模型展开
  - 分层架构：严格遵循接口层→应用层→领域层→基础设施层的单向依赖
  - 依赖倒置：高层模块依赖抽象接口，低层模块实现接口
- **开发方法论**:
  - 测试驱动开发（TDD）：代码生成必须伴随测试用例
  - 领域优先开发流程：从领域模型开始逐层实现
- **质量标准理念**:
  - 领域层纯粹性：禁止污染业务逻辑的技术实现
  - 文档即代码：API文档与代码同步更新
  - 原子化提交：每个提交解决单一问题

## 3. 技术规范
- **技术栈要求**:
  - Web框架：FastAPI
  - 数据建模：Pydantic
  - ORM：SQLAlchemy + Alembic迁移
- **框架使用规范**:
  - 类型提示：所有函数/变量必须声明类型
  - API文档：端点必须包含完整OpenAPI文档
  - 路由定义：按业务模块组织APIRouter
- **编码标准**:
  - 代码风格：严格遵循PEP 8
  - 命名规范：业务语义优先（禁止技术后缀）
  - 文档语言：所有注释和文档必须使用英文

## 4. 架构约束
- **分层架构规则**:
  ```mermaid
  graph LR
    A[接口层] --> B[应用层]
    B --> C[领域层]
    D[基础设施层] --> C
  ```
  - 依赖方向：严格单向（接口层→应用层→领域层）
  - 领域层隔离：禁止导入FastAPI/SQLAlchemy等框架
- **模块组织方式**:
  - 一级目录：按业务模块划分（如`/modules/auth`）
  - 二级目录：模块内四层架构（interfaces/application/domain/infrastructure）
  - 通用代码：存放在`/common`目录
- **依赖关系约束**:
  - 模块间通信：必须通过目标模块的应用层服务
  - 基础设施层：仅实现领域层定义的接口
  - 禁止循环依赖：模块间不得形成依赖环

## 5. 代码质量标准
- **代码风格规范**:
  - 文件命名：`{业务子域}_{类型}.py`（如`user_models.py`）
  - 禁止通用名：`models.py`/`services.py`等不符合规范
  - 类命名：领域接口无`I`前缀（如`UserRepository`）
- **测试要求**:
  - 分层测试策略：
    - 单元测试：覆盖领域模型和应用服务（使用mock）
    - 集成测试：验证API到数据库完整流程
  - 测试覆盖率：所有代码变更必须伴随测试
  - 命名规范：`should_[预期行为]_when_[条件]`（英文）
- **文档标准**:
  - API文档：端点参数/响应模型必须文档化
  - 类型提示：作为自动文档生成的基础
  - 更新要求：文档与代码变更同步

## 6. 工程实践
- **开发流程**:
  1. 领域层：定义实体和仓库接口
  2. 应用层：实现业务用例服务
  3. 基础设施：提供仓库实现和ORM
  4. 接口层：创建API路由和Schema
- **版本控制**:
  - 提交规范：遵循Conventional Commits
  - 原子提交：每个提交解决单一类型变更
  - 提交示例：
    ```bash
    feat(orders): add bulk create API
    fix(auth): correct token validation
    ```
- **部署规范**:
  - 数据库迁移：强制使用Alembic（禁止auto-create）
  - 环境变量：统一`AI4SE_MCP_HUB_`前缀
  - 虚拟环境：执行命令前必须激活

## 7. 最佳实践
- **开发技巧**:
  - 业务子域划分：
    - 认证模块：`credential_auth`，`oauth_authentication`
    - 订单模块：`order_management`，`order_payment`
  - 领域模型设计：实体应包含丰富业务方法
- **问题解决方案**:
  - 跨模块调用：通过应用服务接口代理
  - 共享组件：模块内用`shared/`，跨模块用`common/`
- **性能优化**:
  - 数据库索引：外键字段必须创建索引
  - 批量操作：支持集合处理接口

## 8. 约束与限制
- **禁止的做法**:
  - 领域层引入框架依赖（零容忍）
  - 数据库字段含技术后缀（如`id_uuid`）
  - 使用深层嵌套目录组织业务子域
  - 修改测试用例使测试通过（而非修复代码）
- **必须遵守的规则**:
  - 主键类型：所有实体必须使用UUID
  - 测试伴随：代码变更必须有对应测试
  - 依赖管理：添加库后立即更新requirements.txt
- **异常处理原则**:
  - HTTP异常：在接口层统一处理
  - 业务异常：在应用层转换为领域异常
  - 技术异常：在基础设施层捕获处理

---
**完整性说明**：  
本规范覆盖DDD项目全生命周期，从架构设计（核心原则/架构约束）、编码实现（技术规范/代码质量）、到工程管理（工程实践/最佳实践）。重点强化了领域层纯粹性、模块化隔离、测试驱动等关键质量属性，通过明确的禁止条款和标准化要求确保规则可执行。

**一致性验证**：  
消除原始规则中分层架构的重复描述，统一业务子域命名规范，解决模块间通信与依赖倒置原则的表述矛盾，确保各章节技术术语表述一致。

**实用性增强**：  
提供可视化架构图、文件命名示例、提交消息模板等实操指导，将数据库设计约束转化为具体字段命名规范，并通过分层测试策略明确各层验证方法。