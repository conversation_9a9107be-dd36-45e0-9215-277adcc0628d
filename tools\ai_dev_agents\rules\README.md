# AI Development Rules

This directory contains rule files that provide constraints and guidelines for AI agents during the development workflow.

## Rule Files

- `ddd_architecture.md` - Domain-Driven Design architecture rules
- `code_quality.md` - Code quality and style guidelines
- `api_design.md` - API design principles
- `database_design.md` - Database design constraints
- `testing.md` - Testing requirements and standards

## Usage

Agents automatically load relevant rules based on their task type:

- **Business Analysis**: General project rules
- **Domain Modeling**: DDD architecture rules
- **Requirements Generation**: Code quality, API design, database design rules
- **Prompt Building**: All applicable rules

## Rule Format

Rules should be written in Markdown format with clear sections and actionable guidelines.
