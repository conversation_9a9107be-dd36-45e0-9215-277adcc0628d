<!-- 
处理后的开发规则
生成时间: 2025-06-25 21:05:33
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: FastAPI和DDD架构的Python项目开发
- **主要改进**: 
  - 重构了规则的组织结构，使其更加系统化
  - 消除了重复内容，特别是关于架构分层和模块组织的部分
  - 补充了测试规范和工程实践的细节
  - 增强了数据库设计约束的明确性
  - 统一了代码组织和命名规范

## 2. 核心原则
- **领域驱动设计(DDD)**: 所有开发活动围绕领域模型展开，严格遵循分层架构
- **技术栈统一性**: 强制使用FastAPI、Pydantic和SQLAlchemy等技术栈
- **代码质量**: 严格遵循PEP 8规范，强制类型提示
- **文档标准**: 所有文档和注释必须使用英文
- **模块化设计**: 以业务功能为第一组织层级，确保高内聚低耦合

## 3. 技术规范
### 3.1 核心技术栈
- **Web框架**: FastAPI
- **数据建模**: Pydantic
- **数据库**: SQLAlchemy + Alembic
- **测试框架**: Pytest + FastAPI TestClient

### 3.2 编码标准
- 严格遵循PEP 8规范
- 强制类型提示(Type Hinting)
- 所有函数签名和变量声明必须包含明确的类型提示
- 文档字符串必须使用英文编写

## 4. 架构约束
### 4.1 分层架构
- **接口层(Interfaces)**: 处理HTTP请求和响应
- **应用层(Application)**: 编排业务用例
- **领域层(Domain)**: 核心业务逻辑
- **基础设施层(Infrastructure)**: 技术实现细节

### 4.2 依赖规则
1. 模块内部单向依赖: Interfaces → Application → Domain
2. 依赖倒置原则: Application依赖Domain抽象，Infrastructure实现这些抽象
3. 领域层纯粹性: 禁止在Domain层引入外部框架依赖
4. 模块间通信: 必须通过目标模块的Application层接口

### 4.3 项目结构
```
.
├── modules/
│   ├── {module_name}/
│   │   ├── application/
│   │   ├── domain/
│   │   ├── infrastructure/
│   │   └── interfaces/
├── common/
├── tests/
└── main.py
```

## 5. 代码质量标准
### 5.1 代码风格
- 严格遵循PEP 8规范
- 使用明确的类型提示
- 文档字符串完整且使用英文

### 5.2 测试要求
- **单元测试**: 覆盖Domain和Application层
- **集成测试**: 覆盖接口层和完整流程
- **测试命名**: 使用BDD风格的should_when格式
- **测试覆盖率**: 新功能必须附带测试

### 5.3 文档标准
- API端点必须有完整的OpenAPI文档
- 代码变更必须同步更新文档
- 所有文档使用英文

## 6. 工程实践
### 6.1 开发流程
1. Domain First: 先定义领域模型
2. 应用层实现用例
3. 基础设施层实现技术细节
4. 接口层暴露API

### 6.2 版本控制
- 遵循约定式提交规范(Conventional Commits)
- 提交信息必须使用英文
- 原子化提交，每个提交只关注一个独立变更

### 6.3 环境管理
- 使用虚拟环境隔离依赖
- 环境变量统一前缀AI4SE_MCP_HUB_
- 依赖变更必须更新requirements.txt

## 7. 最佳实践
### 7.1 模块组织
- 按业务功能划分模块
- 模块内按业务子域前缀命名文件
- 禁止使用通用文件名(models.py等)

### 7.2 数据库设计
- 强制使用UUID主键
- 字段名只反映业务含义，禁止技术后缀
- 表名使用复数形式，避免缩写

### 7.3 重构原则
- 创建新文件优先
- 重构前分析完整上下文
- 重构后必须运行测试

## 8. 约束与限制
### 8.1 严格禁止
- 在Domain层引入外部框架依赖
- 直接访问其他模块的Domain或Infrastructure层
- 使用通用文件名(models.py, services.py等)
- 字段名包含技术类型信息(id_uuid, name_str等)

### 8.2 必须遵守
- 模块内部单向依赖规则
- 类型提示和文档要求
- 测试覆盖率要求
- 提交信息规范

### 8.3 异常处理
- 领域异常应在Domain层定义
- 应用异常应在Application层处理
- 接口异常应在Interfaces层转换