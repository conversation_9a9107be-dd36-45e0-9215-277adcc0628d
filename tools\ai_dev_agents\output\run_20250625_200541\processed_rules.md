<!-- 
处理后的开发规则
生成时间: 2025-06-25 20:07:50
源文件: rules.md
处理器: RulesProcessorAgent
-->

# 处理后的开发规则

## 1. 规则概述
- **规则来源**: rules.md
- **处理时间**: 2023年11月15日
- **规则范围**: 基于FastAPI和领域驱动设计(DDD)的Python项目开发
- **主要改进**:
  1. 重构逻辑结构，建立清晰的规则层次体系
  2. 消除重复内容（如分层架构描述）
  3. 补充缺失的架构约束和工程实践细节
  4. 增强规则可操作性，明确禁止项和必须项
  5. 统一术语表达，解决潜在矛盾点

## 2. 核心原则
- **架构设计原则**:
  - 领域驱动设计(DDD)为核心，所有开发围绕领域模型展开
  - 严格分层架构：接口层→应用层→领域层←基础设施层
  - 模块化优先：按业务功能划分独立模块
- **开发方法论**:
  - 测试驱动开发(TDD)：代码生成必须伴随测试用例
  - 领域优先开发：从领域模型开始逐层实现
- **质量标准理念**:
  - 类型提示强制要求：所有函数签名和变量声明必须包含类型提示
  - 文档完整性：所有注释、文档字符串必须使用英文编写
  - 代码规范：严格遵循PEP 8标准

## 3. 技术规范
- **技术栈要求**:
  - Web框架：FastAPI
  - 数据建模：Pydantic
  - ORM：SQLAlchemy + Alembic（数据库迁移）
- **框架使用规范**:
  - FastAPI路由仅限接口层使用
  - SQLAlchemy模型仅限基础设施层使用
  - 禁止在领域层引入框架依赖
- **编码标准**:
  - 类型提示覆盖率100%
  - 函数长度不超过50行
  - 类长度不超过300行
  - 模块导入必须显式声明

## 4. 架构约束
- **分层架构规则**:
  ```mermaid
  graph LR
    A[接口层] --> B[应用层]
    B --> C[领域层]
    D[基础设施层] --> C
  ```
- **模块组织方式**:
  - 一级目录按业务模块划分（如`auth`, `orders`）
  - 模块内部采用四层结构：
    - interfaces：API路由和DTO
    - application：服务编排
    - domain：核心业务逻辑
    - infrastructure：技术实现
- **依赖关系约束**:
  - 严格禁止跨层逆向依赖
  - 模块间通信必须通过目标模块应用层
  - 领域层禁止导入任何外部框架
  - 通用代码仅允许存放在`common`目录

## 5. 代码质量标准
- **代码风格规范**:
  - 遵循PEP 8标准
  - 函数和变量命名使用snake_case
  - 类命名使用PascalCase
- **测试要求**:
  - 单元测试覆盖率≥90%
  - 测试目录结构镜像生产代码结构
  - 测试命名规范：`should_[预期行为]_when_[条件]`
  - 禁止为通过测试而修改测试用例
- **文档标准**:
  - 所有API端点必须有OpenAPI文档
  - 复杂业务逻辑必须有文档字符串说明
  - 领域模型必须有业务含义注释

## 6. 工程实践
- **开发流程**:
  1. 创建/定位业务模块
  2. 领域层：定义实体和仓库接口
  3. 应用层：实现服务逻辑
  4. 基础设施层：提供技术实现
  5. 接口层：暴露API端点
- **版本控制**:
  - 提交信息遵循约定式提交规范
  - 原子化提交：每个提交只包含单一逻辑变更
  - 提交类型标识：feat/fix/docs/refactor等
- **部署规范**:
  - 数据库迁移必须使用Alembic
  - 禁止应用启动时自动建表
  - 环境变量前缀：`AI4SE_MCP_HUB_`

## 7. 最佳实践
- **开发技巧**:
  - 业务子域识别原则：
    - 数据模型不同
    - 业务流程不同
    - 外部依赖不同
    - 生命周期不同
  - 模块内共享代码使用`shared`目录
- **问题解决方案**:
  - 循环依赖：通过依赖注入解耦
  - 复杂查询：在仓库实现中封装
  - 跨模块调用：通过应用层服务接口
- **性能优化**:
  - 高频查询字段创建索引
  - 批量操作使用SQLAlchemy bulk操作
  - 避免N+1查询问题

## 8. 约束与限制
- **禁止的做法**:
  - 禁止在领域层使用框架相关import
  - 禁止使用通用文件名（如`models.py`）
  - 禁止数据库字段包含技术后缀（如`id_uuid`）
  - 禁止Integer类型主键（必须使用UUID）
  - 禁止跨模块直接访问领域层
- **必须遵守的规则**:
  - 所有实体主键必须是UUID类型
  - 所有层必须使用业务子域前缀命名文件
  - 所有提交必须包含明确的范围标识
  - 所有代码变更必须伴随测试用例
- **异常处理原则**:
  - 业务异常在应用层捕获
  - 技术异常在基础设施层处理
  - HTTP异常在接口层统一转换

> 本次重组优化了原始规则的逻辑结构，消除了重复内容（如分层架构的多处描述），补充了关键约束（如模块间通信规则），增强了可操作性（明确文件命名规范），并确保各规则间无矛盾冲突。所有技术术语保持准确一致，重点约束条款使用**禁止**/**必须**等强制表述。