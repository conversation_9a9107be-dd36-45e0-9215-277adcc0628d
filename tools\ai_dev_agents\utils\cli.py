"""
Command Line Interface for AI Development Agents

Provides CLI access to the AI development workflow.
"""

import argparse
import sys
import json
import platform
import subprocess
import shutil
from pathlib import Path
from typing import List, Optional
from datetime import datetime

from ..core.orchestrator import AIDevWorkflowOrchestrator


def list_input_files(input_dir: str = "input") -> List[str]:
    """List available input files in the input directory."""
    input_path = Path(input_dir)
    if not input_path.exists():
        return []

    files = []
    for file_path in input_path.iterdir():
        if file_path.is_file() and not file_path.name.startswith('.'):
            files.append(str(file_path))

    return sorted(files)


def interactive_file_selection(input_dir: str = "input") -> Optional[str]:
    """Interactive file selection from input directory."""
    files = list_input_files(input_dir)

    if not files:
        print(f"❌ No input files found in '{input_dir}' directory.")
        return None

    print(f"\n📁 Available input files in '{input_dir}':")
    for i, file_path in enumerate(files, 1):
        file_name = Path(file_path).name
        file_size = Path(file_path).stat().st_size
        print(f"  {i}. {file_name} ({file_size} bytes)")

    while True:
        try:
            choice = input(f"\n🔍 Please select a file (1-{len(files)}) or 'q' to quit: ").strip()

            if choice.lower() == 'q':
                print("👋 Goodbye!")
                sys.exit(0)

            index = int(choice) - 1
            if 0 <= index < len(files):
                selected_file = files[index]
                print(f"✅ Selected: {Path(selected_file).name}")
                return selected_file
            else:
                print(f"❌ Invalid choice. Please enter a number between 1 and {len(files)}.")

        except ValueError:
            print("❌ Invalid input. Please enter a number or 'q' to quit.")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)


def load_prd_content(prd_path: Optional[str] = None, input_dir: str = "input") -> str:
    """Load PRD content from file, with interactive selection if path not provided."""
    # If no path provided, try interactive selection
    if prd_path is None:
        prd_path = interactive_file_selection(input_dir)
        if prd_path is None:
            sys.exit(1)

    try:
        with open(prd_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ Error: PRD file not found: {prd_path}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error reading PRD file: {e}")
        sys.exit(1)


def clean_output_directory(output_dir: str = "output", force: bool = False, keep_latest: int = 0) -> None:
    """
    Clean the output directory with cross-platform compatibility.

    Args:
        output_dir: Output directory path
        force: Skip confirmation prompt
        keep_latest: Number of latest runs to keep
    """
    output_path = Path(output_dir)

    if not output_path.exists():
        print(f"📁 Output directory '{output_dir}' does not exist.")
        return

    # Get all run directories (timestamped directories)
    run_dirs = []
    for item in output_path.iterdir():
        if item.is_dir() and item.name.startswith("run_"):
            try:
                # Parse timestamp from directory name (run_YYYYMMDD_HHMMSS)
                timestamp_str = item.name[4:]  # Remove "run_" prefix
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                run_dirs.append((timestamp, item))
            except ValueError:
                # If parsing fails, treat as regular directory
                run_dirs.append((datetime.min, item))

    if not run_dirs:
        print(f"📁 No run directories found in '{output_dir}'.")
        return

    # Sort by timestamp (newest first)
    run_dirs.sort(key=lambda x: x[0], reverse=True)

    # Determine which directories to remove
    if keep_latest > 0:
        dirs_to_remove = [item for _, item in run_dirs[keep_latest:]]
        dirs_to_keep = [item for _, item in run_dirs[:keep_latest]]
    else:
        dirs_to_remove = [item for _, item in run_dirs]
        dirs_to_keep = []

    if not dirs_to_remove:
        print(f"📁 No directories to clean (keeping latest {keep_latest} runs).")
        return

    # Show what will be cleaned
    print(f"🧹 Clean Output Directory: {output_path.absolute()}")
    print(f"📊 Found {len(run_dirs)} run directories")

    if dirs_to_keep:
        print(f"✅ Keeping {len(dirs_to_keep)} latest runs:")
        for dir_path in dirs_to_keep:
            print(f"   • {dir_path.name}")

    print(f"🗑️  Will remove {len(dirs_to_remove)} directories:")
    for dir_path in dirs_to_remove:
        dir_size = get_directory_size(dir_path)
        print(f"   • {dir_path.name} ({format_size(dir_size)})")

    # Confirmation
    if not force:
        response = input(f"\n❓ Are you sure you want to remove {len(dirs_to_remove)} directories? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Clean operation cancelled.")
            return

    # Remove directories
    removed_count = 0
    total_size_freed = 0

    for dir_path in dirs_to_remove:
        try:
            dir_size = get_directory_size(dir_path)

            # Use cross-platform removal
            if platform.system() == "Windows":
                # Use PowerShell Remove-Item for Windows
                subprocess.run([
                    "powershell", "-Command",
                    f"Remove-Item -Recurse -Force '{dir_path}'"
                ], check=True, capture_output=True)
            else:
                # Use shutil for Unix-like systems (macOS, Linux)
                shutil.rmtree(dir_path)

            removed_count += 1
            total_size_freed += dir_size
            print(f"✅ Removed: {dir_path.name}")

        except Exception as e:
            print(f"❌ Failed to remove {dir_path.name}: {e}")

    print(f"\n🎉 Clean completed!")
    print(f"📊 Removed {removed_count} directories")
    print(f"💾 Freed {format_size(total_size_freed)} of disk space")


def get_directory_size(path: Path) -> int:
    """Get the total size of a directory in bytes."""
    total_size = 0
    try:
        for item in path.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
    except (OSError, PermissionError):
        pass
    return total_size


def format_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    size = float(size_bytes)

    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1

    return f"{size:.1f} {size_names[i]}"


def parse_module_list(modules_str: str) -> List[str]:
    """Parse comma-separated module list."""
    if not modules_str:
        return []
    return [m.strip() for m in modules_str.split(',') if m.strip()]


def create_orchestrator(verbose: bool = False, config_path: Optional[str] = None,
                       model_preset: Optional[str] = None) -> AIDevWorkflowOrchestrator:
    """Create orchestrator instance with configuration."""
    return AIDevWorkflowOrchestrator(
        llm=None,  # Will be created from config
        verbose=verbose,
        config_path=config_path,
        model_preset=model_preset
    )


def cmd_full_workflow(args):
    """Execute full workflow command."""
    print("🚀 Starting AI Development Workflow...")

    # Load PRD content (with interactive selection if no file specified)
    prd_file = getattr(args, 'prd_file', None)
    input_dir = getattr(args, 'input_dir', 'input')

    if prd_file:
        print(f"📄 PRD File: {prd_file}")
    else:
        print(f"📁 Selecting from input directory: {input_dir}")

    prd_content = load_prd_content(prd_file, input_dir)
    print(f"📊 PRD Content Length: {len(prd_content)} characters")
    print(f"📁 Output Directory: {args.output}")
    
    # Parse selected modules
    selected_modules = None
    if args.modules:
        selected_modules = parse_module_list(args.modules)
        print(f"🎯 Selected Modules: {', '.join(selected_modules)}")
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None)
    )
    
    # Execute workflow
    result = orchestrator.execute_full_workflow(
        prd_content=prd_content,
        project_root=args.project_root,
        output_dir=args.output,
        selected_modules=selected_modules
    )
    
    # Handle results
    if result.get("success"):
        print("\n✅ Workflow completed successfully!")
        print(f"📁 Results saved to: {result['output_directory']}")
        print(f"📊 Summary: {result['summary_file']}")
        
        # Print module summary
        modules = result.get("modules", {})
        if modules:
            print(f"\n📦 Processed Modules ({len(modules)}):")
            for module_name, module_data in modules.items():
                files = module_data.get("files", {})
                print(f"  • {module_name}")
                if "requirements" in files:
                    print(f"    - Requirements: {files['requirements']}")
                if "prompt" in files:
                    print(f"    - AI Prompt: {files['prompt']}")
    else:
        print(f"\n❌ Workflow failed: {result.get('error', 'Unknown error')}")
        if result.get("details"):
            for detail in result["details"]:
                print(f"   - {detail}")
        sys.exit(1)


def cmd_business_analysis(args):
    """Execute business analysis command."""
    print("🔍 Running Business Analysis...")

    # Load PRD content (with interactive selection if no file specified)
    prd_file = getattr(args, 'prd_file', None)
    input_dir = getattr(args, 'input_dir', 'input')
    prd_content = load_prd_content(prd_file, input_dir)
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None)
    )
    
    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute business analysis
    result = orchestrator._execute_business_analysis(prd_content)
    
    # Save results
    if result.success:
        output_file = args.output or "business_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Business analysis completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Entities: {len(result.data.get('core_entities', []))}")
        print(f"📊 Requirements: {len(result.data.get('functional_requirements', []))}")
        print(f"📊 User Stories: {len(result.data.get('user_stories', []))}")
    else:
        print(f"❌ Business analysis failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_domain_modeling(args):
    """Execute domain modeling command."""
    print("🏗️ Running Domain Modeling...")
    
    # Load business analysis
    try:
        with open(args.business_analysis, 'r', encoding='utf-8') as f:
            business_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading business analysis: {e}")
        sys.exit(1)
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None)
    )
    
    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute domain modeling
    result = orchestrator._execute_domain_modeling(business_data)
    
    # Save results
    if result.success:
        output_file = args.output or "domain_model.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Domain modeling completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Bounded Contexts: {len(result.data.get('bounded_contexts', []))}")
        print(f"📊 Aggregates: {len(result.data.get('aggregates', []))}")
        print(f"📊 Entities: {len(result.data.get('domain_entities', []))}")
    else:
        print(f"❌ Domain modeling failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_generate_requirements(args):
    """Execute requirements generation command."""
    print("📋 Generating Technical Requirements...")
    
    # Load domain model
    try:
        with open(args.domain_model, 'r', encoding='utf-8') as f:
            domain_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading domain model: {e}")
        sys.exit(1)
    
    # Filter for specific module if provided
    if args.module:
        print(f"🎯 Filtering for module: {args.module}")
        # TODO: Implement module filtering logic
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None)
    )
    
    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute requirements generation
    result = orchestrator._execute_requirements_generation(domain_data)
    
    # Save results
    if result.success:
        output_file = args.output or f"{args.module or 'module'}_requirements.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Requirements generation completed!")
        print(f"📁 Results saved to: {output_file}")
        
        # Print summary
        data = result.data
        print(f"📊 User Stories: {len(data.get('user_stories', []))}")
        print(f"📊 API Endpoints: {len(data.get('api_design', {}).get('endpoints', []))}")
        print(f"📊 Database Tables: {len(data.get('data_models', {}).get('tables', []))}")
    else:
        print(f"❌ Requirements generation failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_build_prompt(args):
    """Execute prompt building command."""
    print("🤖 Building AI Development Prompt...")
    
    # Load requirements
    try:
        with open(args.requirements, 'r', encoding='utf-8') as f:
            requirements_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading requirements: {e}")
        sys.exit(1)
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None)
    )
    
    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute prompt building
    result = orchestrator._execute_prompt_building(requirements_data)
    
    # Save results
    if result.success:
        module_name = result.data.get("module_name", "module")
        output_file = args.output or f"{module_name}_ai_prompt.md"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result.data.get("prompt_content", ""))
        
        print(f"✅ AI prompt building completed!")
        print(f"📁 Results saved to: {output_file}")
        print(f"📊 Word Count: {result.data.get('word_count', 0)}")
        print(f"📊 Estimated Tokens: {result.data.get('estimated_tokens', 0)}")
    else:
        print(f"❌ Prompt building failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_clean(args):
    """Execute clean command."""
    print("🧹 Cleaning output directory...")

    # Use default output directory if not specified
    output_dir = getattr(args, 'output', 'output')

    clean_output_directory(
        output_dir=output_dir,
        force=args.force,
        keep_latest=args.keep_latest
    )


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="AI Development Workflow - Intelligent code generation from PRD to AI prompts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Full workflow with file selection
  python -m tools.ai_dev_agents.cli workflow

  # Full workflow with specific file
  python -m tools.ai_dev_agents.cli workflow design/mcp-market-prd.txt

  # Step by step with interactive file selection
  python -m tools.ai_dev_agents.cli business-analysis
  python -m tools.ai_dev_agents.cli domain-modeling business_analysis.json
  python -m tools.ai_dev_agents.cli generate-requirements domain_model.json --module mcp_server
  python -m tools.ai_dev_agents.cli build-prompt mcp_server_requirements.json

  # Clean output directory
  python -m tools.ai_dev_agents.cli clean
  python -m tools.ai_dev_agents.cli clean --keep-latest 3
  python -m tools.ai_dev_agents.cli clean --force

Features:
  - Interactive file selection from input directory
  - Context-aware rule loading for each agent type
  - Timestamped output directories with organized results
  - Module-based context storage for workflow steps
  - Cross-platform output directory cleaning
"""
    )
    
    # Global arguments
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--project-root", default=".", help="Project root directory (default: current directory)")
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument("--preset", "-p", help="Model preset to use (e.g., high_quality, economical)")
    parser.add_argument("--input-dir", default="input", help="Input directory for file selection (default: input)")

    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Full workflow command
    workflow_parser = subparsers.add_parser("workflow", help="Execute full workflow")
    workflow_parser.add_argument("prd_file", nargs="?", help="Path to PRD file (optional - will prompt for selection if not provided)")
    workflow_parser.add_argument("--output", "-o", default="output", help="Base output directory (timestamped subdirectory will be created)")
    workflow_parser.add_argument("--modules", "-m", help="Comma-separated list of modules to process")

    # Business analysis command
    business_parser = subparsers.add_parser("business-analysis", help="Analyze business requirements")
    business_parser.add_argument("prd_file", nargs="?", help="Path to PRD file (optional - will prompt for selection if not provided)")
    business_parser.add_argument("--output", "-o", help="Output file path")
    
    # Domain modeling command
    domain_parser = subparsers.add_parser("domain-modeling", help="Create domain models")
    domain_parser.add_argument("business_analysis", help="Path to business analysis JSON file")
    domain_parser.add_argument("--output", "-o", help="Output file path")
    
    # Requirements generation command
    req_parser = subparsers.add_parser("generate-requirements", help="Generate technical requirements")
    req_parser.add_argument("domain_model", help="Path to domain model JSON file")
    req_parser.add_argument("--module", help="Specific module to generate requirements for")
    req_parser.add_argument("--output", "-o", help="Output file path")
    
    # Prompt building command
    prompt_parser = subparsers.add_parser("build-prompt", help="Build AI development prompt")
    prompt_parser.add_argument("requirements", help="Path to requirements JSON file")
    prompt_parser.add_argument("--output", "-o", help="Output file path")

    # Clean command
    clean_parser = subparsers.add_parser("clean", help="Clean output directory")
    clean_parser.add_argument("--force", "-f", action="store_true", help="Force clean without confirmation")
    clean_parser.add_argument("--keep-latest", "-k", type=int, default=0, help="Keep latest N runs (default: 0 - remove all)")

    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    try:
        if args.command == "workflow":
            cmd_full_workflow(args)
        elif args.command == "business-analysis":
            cmd_business_analysis(args)
        elif args.command == "domain-modeling":
            cmd_domain_modeling(args)
        elif args.command == "generate-requirements":
            cmd_generate_requirements(args)
        elif args.command == "build-prompt":
            cmd_build_prompt(args)
        elif args.command == "clean":
            cmd_clean(args)
        else:
            print(f"❌ Unknown command: {args.command}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
